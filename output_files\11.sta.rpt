Timing Analyzer report for 11
Thu Jul 17 01:46:07 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Timing Analyzer Summary
  3. Parallel Compilation
  4. Clocks
  5. Slow 1200mV 85C Model Fmax Summary
  6. Timing Closure Recommendations
  7. Slow 1200mV 85C Model Setup Summary
  8. Slow 1200mV 85C Model Hold Summary
  9. Slow 1200mV 85C Model Recovery Summary
 10. Slow 1200mV 85C Model Removal Summary
 11. Slow 1200mV 85C Model Minimum Pulse Width Summary
 12. Slow 1200mV 85C Model Setup: 'clk_50MHz'
 13. Slow 1200mV 85C Model Hold: 'clk_50MHz'
 14. Slow 1200mV 85C Model Metastability Summary
 15. Slow 1200mV 0C Model Fmax Summary
 16. Slow 1200mV 0C Model Setup Summary
 17. Slow 1200mV 0C Model Hold Summary
 18. Slow 1200mV 0C Model Recovery Summary
 19. Slow 1200mV 0C Model Removal Summary
 20. Slow 1200mV 0C Model Minimum Pulse Width Summary
 21. Slow 1200mV 0C Model Setup: 'clk_50MHz'
 22. Slow 1200mV 0C Model Hold: 'clk_50MHz'
 23. Slow 1200mV 0C Model Metastability Summary
 24. Fast 1200mV 0C Model Setup Summary
 25. Fast 1200mV 0C Model Hold Summary
 26. Fast 1200mV 0C Model Recovery Summary
 27. Fast 1200mV 0C Model Removal Summary
 28. Fast 1200mV 0C Model Minimum Pulse Width Summary
 29. Fast 1200mV 0C Model Setup: 'clk_50MHz'
 30. Fast 1200mV 0C Model Hold: 'clk_50MHz'
 31. Fast 1200mV 0C Model Metastability Summary
 32. Multicorner Timing Analysis Summary
 33. Board Trace Model Assignments
 34. Input Transition Times
 35. Signal Integrity Metrics (Slow 1200mv 0c Model)
 36. Signal Integrity Metrics (Slow 1200mv 85c Model)
 37. Signal Integrity Metrics (Fast 1200mv 0c Model)
 38. Setup Transfers
 39. Hold Transfers
 40. Report TCCS
 41. Report RSKM
 42. Unconstrained Paths Summary
 43. Clock Status Summary
 44. Unconstrained Input Ports
 45. Unconstrained Output Ports
 46. Unconstrained Input Ports
 47. Unconstrained Output Ports
 48. Timing Analyzer Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+-----------------------------------------------------------------------------+
; Timing Analyzer Summary                                                     ;
+-----------------------+-----------------------------------------------------+
; Quartus Prime Version ; Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition ;
; Timing Analyzer       ; Legacy Timing Analyzer                              ;
; Revision Name         ; 11                                                  ;
; Device Family         ; Cyclone IV E                                        ;
; Device Name           ; EP4CE6E22C8                                         ;
; Timing Models         ; Final                                               ;
; Delay Model           ; Combined                                            ;
; Rise/Fall Delays      ; Enabled                                             ;
+-----------------------+-----------------------------------------------------+


+------------------------------------------+
; Parallel Compilation                     ;
+----------------------------+-------------+
; Processors                 ; Number      ;
+----------------------------+-------------+
; Number detected on machine ; 20          ;
; Maximum allowed            ; 14          ;
;                            ;             ;
; Average used               ; 1.07        ;
; Maximum used               ; 14          ;
;                            ;             ;
; Usage by Processor         ; % Time Used ;
;     Processor 1            ; 100.0%      ;
;     Processors 2-14        ;   0.5%      ;
+----------------------------+-------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clocks                                                                                                                                                                                ;
+------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------------+
; Clock Name ; Type ; Period ; Frequency  ; Rise  ; Fall  ; Duty Cycle ; Divide by ; Multiply by ; Phase ; Offset ; Edge List ; Edge Shift ; Inverted ; Master ; Source ; Targets       ;
+------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------------+
; clk_50MHz  ; Base ; 1.000  ; 1000.0 MHz ; 0.000 ; 0.500 ;            ;           ;             ;       ;        ;           ;            ;          ;        ;        ; { clk_50MHz } ;
+------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------------+


+--------------------------------------------------+
; Slow 1200mV 85C Model Fmax Summary               ;
+------------+-----------------+------------+------+
; Fmax       ; Restricted Fmax ; Clock Name ; Note ;
+------------+-----------------+------------+------+
; 190.95 MHz ; 190.95 MHz      ; clk_50MHz  ;      ;
+------------+-----------------+------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


----------------------------------
; Timing Closure Recommendations ;
----------------------------------
HTML report is unavailable in plain text report export.


+-------------------------------------+
; Slow 1200mV 85C Model Setup Summary ;
+-----------+--------+----------------+
; Clock     ; Slack  ; End Point TNS  ;
+-----------+--------+----------------+
; clk_50MHz ; -4.237 ; -331.662       ;
+-----------+--------+----------------+


+------------------------------------+
; Slow 1200mV 85C Model Hold Summary ;
+-----------+-------+----------------+
; Clock     ; Slack ; End Point TNS  ;
+-----------+-------+----------------+
; clk_50MHz ; 0.432 ; 0.000          ;
+-----------+-------+----------------+


------------------------------------------
; Slow 1200mV 85C Model Recovery Summary ;
------------------------------------------
No paths to report.


-----------------------------------------
; Slow 1200mV 85C Model Removal Summary ;
-----------------------------------------
No paths to report.


+---------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width Summary ;
+-----------+--------+------------------------------+
; Clock     ; Slack  ; End Point TNS                ;
+-----------+--------+------------------------------+
; clk_50MHz ; -3.000 ; -202.258                     ;
+-----------+--------+------------------------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'clk_50MHz'                                                                                                                                      ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node                                      ; To Node                                       ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+
; -4.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.659      ;
; -4.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.659      ;
; -4.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.659      ;
; -4.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.659      ;
; -4.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.659      ;
; -4.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.659      ;
; -4.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.659      ;
; -4.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.659      ;
; -4.209 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.144      ;
; -4.209 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.144      ;
; -4.209 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.144      ;
; -4.209 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.144      ;
; -4.209 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.144      ;
; -4.209 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.144      ;
; -4.209 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.144      ;
; -4.209 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.144      ;
; -4.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.019      ;
; -4.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.019      ;
; -4.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.019      ;
; -4.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.019      ;
; -4.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.019      ;
; -4.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.019      ;
; -4.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.019      ;
; -4.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 5.019      ;
; -4.077 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.996      ;
; -4.077 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.996      ;
; -4.077 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.996      ;
; -4.077 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.996      ;
; -4.077 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.996      ;
; -4.077 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.996      ;
; -4.077 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.996      ;
; -4.077 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.996      ;
; -4.064 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 4.999      ;
; -4.064 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 4.999      ;
; -4.064 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 4.999      ;
; -4.064 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 4.999      ;
; -4.064 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 4.999      ;
; -4.064 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 4.999      ;
; -4.064 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 4.999      ;
; -4.064 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 4.999      ;
; -4.053 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.972      ;
; -4.053 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.972      ;
; -4.053 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.972      ;
; -4.053 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.972      ;
; -4.053 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.972      ;
; -4.053 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.972      ;
; -4.053 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.972      ;
; -4.053 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.972      ;
; -4.047 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.966      ;
; -4.047 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.966      ;
; -4.047 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.966      ;
; -4.047 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.966      ;
; -4.047 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.966      ;
; -4.047 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.966      ;
; -4.047 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.966      ;
; -4.047 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.966      ;
; -4.042 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.961      ;
; -4.042 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.961      ;
; -4.042 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.961      ;
; -4.042 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.961      ;
; -4.042 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.961      ;
; -4.042 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.961      ;
; -4.042 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.961      ;
; -4.042 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.961      ;
; -4.039 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.958      ;
; -4.039 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.958      ;
; -4.039 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.958      ;
; -4.039 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.958      ;
; -4.039 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.958      ;
; -4.039 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.958      ;
; -4.039 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.958      ;
; -4.039 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.958      ;
; -4.034 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.953      ;
; -4.034 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.953      ;
; -4.034 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.953      ;
; -4.034 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.953      ;
; -4.034 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.953      ;
; -4.034 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.953      ;
; -4.034 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.953      ;
; -4.034 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.953      ;
; -3.987 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.906      ;
; -3.987 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.906      ;
; -3.987 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.906      ;
; -3.987 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.906      ;
; -3.987 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.906      ;
; -3.987 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.906      ;
; -3.987 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.906      ;
; -3.987 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.906      ;
; -3.963 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.385      ;
; -3.963 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.385      ;
; -3.963 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.385      ;
; -3.963 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.385      ;
; -3.963 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.385      ;
; -3.963 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.385      ;
; -3.963 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.385      ;
; -3.963 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.579     ; 4.385      ;
; -3.942 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 4.877      ;
; -3.942 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 4.877      ;
; -3.942 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 4.877      ;
; -3.942 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.066     ; 4.877      ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'clk_50MHz'                                                                                                                                                         ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node                                                ; To Node                                                ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; 0.432 ; zhuhoujia2023111413_005:dht11_sensor|received_data[13]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[13] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.102      ; 0.746      ;
; 0.432 ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE      ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.102      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[31]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[31] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[15]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[15] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[14]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[14] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[30]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[30] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[29]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[29] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[12]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[12] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[28]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[28] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[11]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[11] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[27]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[27] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[10]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[10] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[26]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[26] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]    ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[25]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[25] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]    ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.434 ; zhuhoujia2023111413_005:dht11_sensor|received_data[24]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[24] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 0.746      ;
; 0.444 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.102      ; 0.758      ;
; 0.451 ; zhuhoujia2023111413_006:lcd_display|lcd_en               ; zhuhoujia2023111413_006:lcd_display|lcd_en             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.083      ; 0.746      ;
; 0.451 ; zhuhoujia2023111413_006:lcd_display|lcd_rst              ; zhuhoujia2023111413_006:lcd_display|lcd_rst            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.083      ; 0.746      ;
; 0.451 ; zhuhoujia2023111413_006:lcd_display|state.RESET          ; zhuhoujia2023111413_006:lcd_display|state.RESET        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.083      ; 0.746      ;
; 0.451 ; zhuhoujia2023111413_006:lcd_display|state.INIT           ; zhuhoujia2023111413_006:lcd_display|state.INIT         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.083      ; 0.746      ;
; 0.451 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP   ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.083      ; 0.746      ;
; 0.451 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM    ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.083      ; 0.746      ;
; 0.452 ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE          ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE       ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.START         ; zhuhoujia2023111413_005:dht11_sensor|state.START       ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.463 ; zhuhoujia2023111413_006:lcd_display|state.IDLE           ; zhuhoujia2023111413_006:lcd_display|state.IDLE         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.083      ; 0.758      ;
; 0.466 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.758      ;
; 0.500 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.793      ;
; 0.517 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.810      ;
; 0.532 ; zhuhoujia2023111413_005:dht11_sensor|timer[19]           ; zhuhoujia2023111413_005:dht11_sensor|timer[19]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.826      ;
; 0.554 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.847      ;
; 0.556 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.849      ;
; 0.556 ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.849      ;
; 0.559 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.852      ;
; 0.560 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.853      ;
; 0.560 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.853      ;
; 0.579 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.872      ;
; 0.611 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]            ; zhuhoujia2023111413_005:dht11_sensor|timer[3]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.579      ; 1.402      ;
; 0.620 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.579      ; 1.411      ;
; 0.640 ; zhuhoujia2023111413_005:dht11_sensor|temperature[3]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.934      ;
; 0.641 ; zhuhoujia2023111413_005:dht11_sensor|temperature[4]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[4]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.935      ;
; 0.641 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.934      ;
; 0.660 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.953      ;
; 0.687 ; zhuhoujia2023111413_005:dht11_sensor|humidity[0]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[0]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.981      ;
; 0.690 ; zhuhoujia2023111413_005:dht11_sensor|humidity[6]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[6]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.984      ;
; 0.693 ; zhuhoujia2023111413_005:dht11_sensor|humidity[4]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[4]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.987      ;
; 0.693 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.986      ;
; 0.696 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.989      ;
; 0.697 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.990      ;
; 0.697 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.990      ;
; 0.698 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.991      ;
; 0.715 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.008      ;
; 0.739 ; zhuhoujia2023111413_005:dht11_sensor|temperature[0]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[0]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.033      ;
; 0.739 ; zhuhoujia2023111413_005:dht11_sensor|temperature[6]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[6]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.033      ;
; 0.740 ; zhuhoujia2023111413_005:dht11_sensor|temperature[1]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.034      ;
; 0.742 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]            ; zhuhoujia2023111413_005:dht11_sensor|timer[5]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.036      ;
; 0.742 ; zhuhoujia2023111413_006:lcd_display|state.RESET          ; zhuhoujia2023111413_006:lcd_display|lcd_rst            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.083      ; 1.037      ;
; 0.744 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.102      ; 1.058      ;
; 0.747 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]            ; zhuhoujia2023111413_005:dht11_sensor|timer[2]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.041      ;
; 0.748 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.040      ;
; 0.749 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[3]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.579      ; 1.540      ;
; 0.750 ; zhuhoujia2023111413_005:dht11_sensor|timer[11]           ; zhuhoujia2023111413_005:dht11_sensor|timer[11]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.044      ;
; 0.753 ; zhuhoujia2023111413_005:dht11_sensor|timer[10]           ; zhuhoujia2023111413_005:dht11_sensor|timer[10]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.047      ;
; 0.753 ; zhuhoujia2023111413_005:dht11_sensor|timer[12]           ; zhuhoujia2023111413_005:dht11_sensor|timer[12]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.047      ;
; 0.753 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.065      ;
; 0.755 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.067      ;
; 0.758 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.579      ; 1.549      ;
; 0.760 ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.053      ;
; 0.761 ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]            ; zhuhoujia2023111413_005:dht11_sensor|timer[6]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.073      ;
; 0.762 ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.056      ;
; 0.762 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[0]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.056      ;
; 0.763 ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.763 ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.763 ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.763 ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.764 ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.058      ;
; 0.764 ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.058      ;
; 0.765 ; zhuhoujia2023111413_006:lcd_display|delay_counter[12]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[12]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.059      ;
; 0.765 ; zhuhoujia2023111413_006:lcd_display|delay_counter[8]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[8]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.059      ;
; 0.765 ; zhuhoujia2023111413_006:lcd_display|delay_counter[10]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[10]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.059      ;
; 0.765 ; zhuhoujia2023111413_005:dht11_sensor|timer[14]           ; zhuhoujia2023111413_005:dht11_sensor|timer[14]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.059      ;
; 0.767 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.079      ;
; 0.768 ; zhuhoujia2023111413_005:dht11_sensor|timer[13]           ; zhuhoujia2023111413_005:dht11_sensor|timer[13]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.062      ;
; 0.768 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.080      ;
; 0.769 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.061      ;
; 0.770 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.082      ;
; 0.771 ; zhuhoujia2023111413_005:dht11_sensor|timer[16]           ; zhuhoujia2023111413_005:dht11_sensor|timer[16]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.065      ;
; 0.771 ; zhuhoujia2023111413_005:dht11_sensor|timer[17]           ; zhuhoujia2023111413_005:dht11_sensor|timer[17]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.065      ;
; 0.773 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.066      ;
; 0.774 ; zhuhoujia2023111413_005:dht11_sensor|timer[18]           ; zhuhoujia2023111413_005:dht11_sensor|timer[18]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.068      ;
; 0.777 ; zhuhoujia2023111413_005:dht11_sensor|timer[15]           ; zhuhoujia2023111413_005:dht11_sensor|timer[15]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.071      ;
; 0.777 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]            ; zhuhoujia2023111413_005:dht11_sensor|timer[8]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.071      ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+


-----------------------------------------------
; Slow 1200mV 85C Model Metastability Summary ;
-----------------------------------------------
No synchronizer chains to report.


+-------------------------------------------------+
; Slow 1200mV 0C Model Fmax Summary               ;
+-----------+-----------------+------------+------+
; Fmax      ; Restricted Fmax ; Clock Name ; Note ;
+-----------+-----------------+------------+------+
; 200.8 MHz ; 200.8 MHz       ; clk_50MHz  ;      ;
+-----------+-----------------+------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+------------------------------------+
; Slow 1200mV 0C Model Setup Summary ;
+-----------+--------+---------------+
; Clock     ; Slack  ; End Point TNS ;
+-----------+--------+---------------+
; clk_50MHz ; -3.980 ; -303.642      ;
+-----------+--------+---------------+


+-----------------------------------+
; Slow 1200mV 0C Model Hold Summary ;
+-----------+-------+---------------+
; Clock     ; Slack ; End Point TNS ;
+-----------+-------+---------------+
; clk_50MHz ; 0.381 ; 0.000         ;
+-----------+-------+---------------+


-----------------------------------------
; Slow 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Slow 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width Summary ;
+-----------+--------+-----------------------------+
; Clock     ; Slack  ; End Point TNS               ;
+-----------+--------+-----------------------------+
; clk_50MHz ; -3.000 ; -202.258                    ;
+-----------+--------+-----------------------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'clk_50MHz'                                                                                                                                       ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node                                      ; To Node                                       ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+
; -3.980 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.441      ;
; -3.980 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.441      ;
; -3.980 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.441      ;
; -3.980 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.441      ;
; -3.980 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.441      ;
; -3.980 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.441      ;
; -3.980 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.441      ;
; -3.980 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.441      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.881      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.881      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.881      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.881      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.881      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.881      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.881      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.881      ;
; -3.815 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.743      ;
; -3.815 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.743      ;
; -3.815 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.743      ;
; -3.815 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.743      ;
; -3.815 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.743      ;
; -3.815 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.743      ;
; -3.815 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.743      ;
; -3.815 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.743      ;
; -3.808 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.736      ;
; -3.808 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.736      ;
; -3.808 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.736      ;
; -3.808 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.736      ;
; -3.808 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.736      ;
; -3.808 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.736      ;
; -3.808 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.736      ;
; -3.808 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.736      ;
; -3.796 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.724      ;
; -3.796 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.724      ;
; -3.796 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.724      ;
; -3.796 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.724      ;
; -3.796 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.724      ;
; -3.796 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.724      ;
; -3.796 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.724      ;
; -3.796 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.724      ;
; -3.783 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.711      ;
; -3.783 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.711      ;
; -3.783 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.711      ;
; -3.783 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.711      ;
; -3.783 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.711      ;
; -3.783 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.711      ;
; -3.783 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.711      ;
; -3.783 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.711      ;
; -3.744 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.672      ;
; -3.744 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.672      ;
; -3.744 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.672      ;
; -3.744 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.672      ;
; -3.744 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.672      ;
; -3.744 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.672      ;
; -3.744 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.672      ;
; -3.744 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.672      ;
; -3.741 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.681      ;
; -3.741 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.681      ;
; -3.741 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.681      ;
; -3.741 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.681      ;
; -3.741 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.681      ;
; -3.741 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.681      ;
; -3.741 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.681      ;
; -3.741 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.681      ;
; -3.733 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.661      ;
; -3.733 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.661      ;
; -3.733 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.661      ;
; -3.733 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.661      ;
; -3.733 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.661      ;
; -3.733 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.661      ;
; -3.733 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.661      ;
; -3.733 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.661      ;
; -3.728 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.668      ;
; -3.728 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.668      ;
; -3.728 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.668      ;
; -3.728 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.668      ;
; -3.728 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.668      ;
; -3.728 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.668      ;
; -3.728 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.668      ;
; -3.728 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.668      ;
; -3.722 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.183      ;
; -3.722 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.183      ;
; -3.722 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.183      ;
; -3.722 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.183      ;
; -3.722 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.183      ;
; -3.722 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.183      ;
; -3.722 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.183      ;
; -3.722 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.541     ; 4.183      ;
; -3.698 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.626      ;
; -3.698 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.626      ;
; -3.698 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.626      ;
; -3.698 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.626      ;
; -3.698 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.626      ;
; -3.698 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.626      ;
; -3.698 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.626      ;
; -3.698 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.074     ; 4.626      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.612      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.612      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.612      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.062     ; 4.612      ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'clk_50MHz'                                                                                                                                                          ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node                                                ; To Node                                                ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; 0.381 ; zhuhoujia2023111413_005:dht11_sensor|received_data[13]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[13] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.093      ; 0.669      ;
; 0.382 ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE      ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.092      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[31]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[31] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[15]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[15] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[14]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[14] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[30]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[30] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[29]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[29] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[12]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[12] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[28]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[28] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[11]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[11] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[27]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[27] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[10]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[10] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[26]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[26] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]    ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[25]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[25] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]    ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[24]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[24] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.397 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.092      ; 0.684      ;
; 0.400 ; zhuhoujia2023111413_006:lcd_display|lcd_en               ; zhuhoujia2023111413_006:lcd_display|lcd_en             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.669      ;
; 0.400 ; zhuhoujia2023111413_006:lcd_display|lcd_rst              ; zhuhoujia2023111413_006:lcd_display|lcd_rst            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.669      ;
; 0.400 ; zhuhoujia2023111413_006:lcd_display|state.RESET          ; zhuhoujia2023111413_006:lcd_display|state.RESET        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.669      ;
; 0.400 ; zhuhoujia2023111413_006:lcd_display|state.INIT           ; zhuhoujia2023111413_006:lcd_display|state.INIT         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.669      ;
; 0.400 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP   ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.669      ;
; 0.400 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM    ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE          ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE       ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|state.START         ; zhuhoujia2023111413_005:dht11_sensor|state.START       ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.415 ; zhuhoujia2023111413_006:lcd_display|state.IDLE           ; zhuhoujia2023111413_006:lcd_display|state.IDLE         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.684      ;
; 0.417 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.684      ;
; 0.464 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.731      ;
; 0.486 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.753      ;
; 0.491 ; zhuhoujia2023111413_005:dht11_sensor|timer[19]           ; zhuhoujia2023111413_005:dht11_sensor|timer[19]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.759      ;
; 0.509 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.776      ;
; 0.511 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.778      ;
; 0.514 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.781      ;
; 0.514 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.781      ;
; 0.515 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.782      ;
; 0.531 ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.798      ;
; 0.539 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.806      ;
; 0.549 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]            ; zhuhoujia2023111413_005:dht11_sensor|timer[3]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.541      ; 1.285      ;
; 0.564 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.541      ; 1.300      ;
; 0.596 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.863      ;
; 0.598 ; zhuhoujia2023111413_005:dht11_sensor|temperature[3]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.866      ;
; 0.599 ; zhuhoujia2023111413_005:dht11_sensor|temperature[4]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[4]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.867      ;
; 0.610 ; zhuhoujia2023111413_005:dht11_sensor|humidity[0]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[0]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.878      ;
; 0.616 ; zhuhoujia2023111413_005:dht11_sensor|humidity[6]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[6]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.884      ;
; 0.617 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.884      ;
; 0.618 ; zhuhoujia2023111413_005:dht11_sensor|humidity[4]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[4]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.887      ;
; 0.644 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.911      ;
; 0.648 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.915      ;
; 0.648 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.915      ;
; 0.649 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.916      ;
; 0.649 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.916      ;
; 0.668 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[3]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.541      ; 1.404      ;
; 0.670 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.937      ;
; 0.683 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.541      ; 1.419      ;
; 0.687 ; zhuhoujia2023111413_005:dht11_sensor|temperature[0]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[0]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.955      ;
; 0.688 ; zhuhoujia2023111413_005:dht11_sensor|temperature[6]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[6]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.956      ;
; 0.689 ; zhuhoujia2023111413_005:dht11_sensor|temperature[1]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.957      ;
; 0.689 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]            ; zhuhoujia2023111413_005:dht11_sensor|timer[5]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.958      ;
; 0.691 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.093      ; 0.979      ;
; 0.697 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]            ; zhuhoujia2023111413_005:dht11_sensor|timer[2]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.966      ;
; 0.697 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.964      ;
; 0.698 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.984      ;
; 0.699 ; zhuhoujia2023111413_006:lcd_display|state.RESET          ; zhuhoujia2023111413_006:lcd_display|lcd_rst            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.968      ;
; 0.700 ; zhuhoujia2023111413_005:dht11_sensor|timer[11]           ; zhuhoujia2023111413_005:dht11_sensor|timer[11]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.968      ;
; 0.701 ; zhuhoujia2023111413_005:dht11_sensor|timer[10]           ; zhuhoujia2023111413_005:dht11_sensor|timer[10]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.969      ;
; 0.702 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.988      ;
; 0.704 ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; zhuhoujia2023111413_005:dht11_sensor|timer[12]           ; zhuhoujia2023111413_005:dht11_sensor|timer[12]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.972      ;
; 0.705 ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.974      ;
; 0.705 ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.974      ;
; 0.706 ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.975      ;
; 0.706 ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.975      ;
; 0.707 ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.976      ;
; 0.707 ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.976      ;
; 0.708 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.994      ;
; 0.708 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.975      ;
; 0.709 ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.978      ;
; 0.709 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]            ; zhuhoujia2023111413_005:dht11_sensor|timer[6]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.978      ;
; 0.710 ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.979      ;
; 0.710 ; zhuhoujia2023111413_006:lcd_display|delay_counter[12]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[12]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.979      ;
; 0.710 ; zhuhoujia2023111413_006:lcd_display|delay_counter[10]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[10]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.979      ;
; 0.710 ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.979      ;
; 0.711 ; zhuhoujia2023111413_006:lcd_display|delay_counter[8]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[8]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.980      ;
; 0.711 ; zhuhoujia2023111413_005:dht11_sensor|timer[14]           ; zhuhoujia2023111413_005:dht11_sensor|timer[14]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.979      ;
; 0.711 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.997      ;
; 0.713 ; zhuhoujia2023111413_005:dht11_sensor|timer[13]           ; zhuhoujia2023111413_005:dht11_sensor|timer[13]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.981      ;
; 0.714 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.096      ; 1.005      ;
; 0.715 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[0]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.984      ;
; 0.715 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 1.001      ;
; 0.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[16]           ; zhuhoujia2023111413_005:dht11_sensor|timer[16]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.985      ;
; 0.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[17]           ; zhuhoujia2023111413_005:dht11_sensor|timer[17]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.985      ;
; 0.718 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 1.004      ;
; 0.719 ; zhuhoujia2023111413_005:dht11_sensor|timer[18]           ; zhuhoujia2023111413_005:dht11_sensor|timer[18]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.987      ;
; 0.721 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.988      ;
; 0.722 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]            ; zhuhoujia2023111413_005:dht11_sensor|timer[8]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.991      ;
; 0.723 ; zhuhoujia2023111413_005:dht11_sensor|timer[15]           ; zhuhoujia2023111413_005:dht11_sensor|timer[15]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.991      ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+


----------------------------------------------
; Slow 1200mV 0C Model Metastability Summary ;
----------------------------------------------
No synchronizer chains to report.


+------------------------------------+
; Fast 1200mV 0C Model Setup Summary ;
+-----------+--------+---------------+
; Clock     ; Slack  ; End Point TNS ;
+-----------+--------+---------------+
; clk_50MHz ; -1.229 ; -74.929       ;
+-----------+--------+---------------+


+-----------------------------------+
; Fast 1200mV 0C Model Hold Summary ;
+-----------+-------+---------------+
; Clock     ; Slack ; End Point TNS ;
+-----------+-------+---------------+
; clk_50MHz ; 0.178 ; 0.000         ;
+-----------+-------+---------------+


-----------------------------------------
; Fast 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Fast 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width Summary ;
+-----------+--------+-----------------------------+
; Clock     ; Slack  ; End Point TNS               ;
+-----------+--------+-----------------------------+
; clk_50MHz ; -3.000 ; -146.061                    ;
+-----------+--------+-----------------------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'clk_50MHz'                                                                                                                                       ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node                                      ; To Node                                       ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+
; -1.229 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.978      ;
; -1.229 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.978      ;
; -1.229 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.978      ;
; -1.229 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.978      ;
; -1.229 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.978      ;
; -1.229 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.978      ;
; -1.229 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.978      ;
; -1.229 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.978      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.140      ;
; -1.178 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.127      ;
; -1.178 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.127      ;
; -1.178 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.127      ;
; -1.178 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.127      ;
; -1.178 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.127      ;
; -1.178 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.127      ;
; -1.178 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.127      ;
; -1.178 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.127      ;
; -1.171 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.128      ;
; -1.171 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.128      ;
; -1.171 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.128      ;
; -1.171 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.128      ;
; -1.171 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.128      ;
; -1.171 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.128      ;
; -1.171 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.128      ;
; -1.171 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.128      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.111      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.111      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.111      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.111      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.111      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.111      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.111      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.111      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.119      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.119      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.119      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.119      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.119      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.119      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.119      ;
; -1.162 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.119      ;
; -1.149 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.106      ;
; -1.149 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.106      ;
; -1.149 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.106      ;
; -1.149 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.106      ;
; -1.149 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.106      ;
; -1.149 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.106      ;
; -1.149 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.106      ;
; -1.149 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.030     ; 2.106      ;
; -1.147 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.096      ;
; -1.147 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.096      ;
; -1.147 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.096      ;
; -1.147 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.096      ;
; -1.147 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.096      ;
; -1.147 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.096      ;
; -1.147 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.096      ;
; -1.147 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.096      ;
; -1.145 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.094      ;
; -1.145 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.094      ;
; -1.145 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.094      ;
; -1.145 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.094      ;
; -1.145 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.094      ;
; -1.145 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.094      ;
; -1.145 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.094      ;
; -1.145 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.094      ;
; -1.137 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.886      ;
; -1.137 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.886      ;
; -1.137 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.886      ;
; -1.137 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.886      ;
; -1.137 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.886      ;
; -1.137 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.886      ;
; -1.137 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.886      ;
; -1.137 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.238     ; 1.886      ;
; -1.112 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.061      ;
; -1.112 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.061      ;
; -1.112 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.061      ;
; -1.112 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.061      ;
; -1.112 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.061      ;
; -1.112 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.061      ;
; -1.112 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.061      ;
; -1.112 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.061      ;
; -1.101 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.050      ;
; -1.101 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.050      ;
; -1.101 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.050      ;
; -1.101 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.038     ; 2.050      ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'clk_50MHz'                                                                                                                                                          ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node                                                ; To Node                                                ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[31]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[31] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[13]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[13] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[29]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[29] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[27]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[27] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[25]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[25] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE      ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[15]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[15] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[14]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[14] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[30]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[30] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[12]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[12] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[28]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[28] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[11]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[11] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[10]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[10] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[26]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[26] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]    ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]    ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[24]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[24] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.185 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.314      ;
; 0.186 ; zhuhoujia2023111413_006:lcd_display|lcd_en               ; zhuhoujia2023111413_006:lcd_display|lcd_en             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_006:lcd_display|lcd_rst              ; zhuhoujia2023111413_006:lcd_display|lcd_rst            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_006:lcd_display|state.RESET          ; zhuhoujia2023111413_006:lcd_display|state.RESET        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_006:lcd_display|state.INIT           ; zhuhoujia2023111413_006:lcd_display|state.INIT         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP   ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM    ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE          ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE       ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_005:dht11_sensor|state.START         ; zhuhoujia2023111413_005:dht11_sensor|state.START       ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.193 ; zhuhoujia2023111413_006:lcd_display|state.IDLE           ; zhuhoujia2023111413_006:lcd_display|state.IDLE         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.314      ;
; 0.194 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.314      ;
; 0.202 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.322      ;
; 0.206 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.326      ;
; 0.216 ; zhuhoujia2023111413_005:dht11_sensor|timer[19]           ; zhuhoujia2023111413_005:dht11_sensor|timer[19]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.337      ;
; 0.226 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.346      ;
; 0.228 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.348      ;
; 0.228 ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.348      ;
; 0.231 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.351      ;
; 0.232 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.352      ;
; 0.232 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.352      ;
; 0.233 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.353      ;
; 0.252 ; zhuhoujia2023111413_005:dht11_sensor|temperature[3]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.373      ;
; 0.253 ; zhuhoujia2023111413_005:dht11_sensor|temperature[4]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[4]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.374      ;
; 0.256 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]            ; zhuhoujia2023111413_005:dht11_sensor|timer[3]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.238      ; 0.578      ;
; 0.259 ; zhuhoujia2023111413_005:dht11_sensor|humidity[0]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[0]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.380      ;
; 0.259 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.238      ; 0.581      ;
; 0.260 ; zhuhoujia2023111413_005:dht11_sensor|humidity[6]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[6]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.381      ;
; 0.260 ; zhuhoujia2023111413_005:dht11_sensor|humidity[4]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[4]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.038      ; 0.382      ;
; 0.262 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.382      ;
; 0.262 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.382      ;
; 0.288 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.408      ;
; 0.290 ; zhuhoujia2023111413_006:lcd_display|state.RESET          ; zhuhoujia2023111413_006:lcd_display|lcd_rst            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.411      ;
; 0.290 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.410      ;
; 0.291 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.411      ;
; 0.292 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.412      ;
; 0.292 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.412      ;
; 0.293 ; zhuhoujia2023111413_005:dht11_sensor|temperature[0]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[0]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.414      ;
; 0.294 ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; zhuhoujia2023111413_005:dht11_sensor|temperature[6]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[6]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.415      ;
; 0.295 ; zhuhoujia2023111413_005:dht11_sensor|temperature[1]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.416      ;
; 0.296 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]            ; zhuhoujia2023111413_005:dht11_sensor|timer[5]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.038      ; 0.418      ;
; 0.298 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]            ; zhuhoujia2023111413_005:dht11_sensor|timer[2]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.038      ; 0.420      ;
; 0.298 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.427      ;
; 0.300 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.420      ;
; 0.301 ; zhuhoujia2023111413_005:dht11_sensor|timer[10]           ; zhuhoujia2023111413_005:dht11_sensor|timer[10]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.422      ;
; 0.301 ; zhuhoujia2023111413_005:dht11_sensor|timer[11]           ; zhuhoujia2023111413_005:dht11_sensor|timer[11]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.422      ;
; 0.302 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.431      ;
; 0.303 ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; zhuhoujia2023111413_005:dht11_sensor|timer[12]           ; zhuhoujia2023111413_005:dht11_sensor|timer[12]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.432      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.425      ;
; 0.306 ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.427      ;
; 0.306 ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.427      ;
; 0.306 ; zhuhoujia2023111413_006:lcd_display|delay_counter[8]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[8]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.427      ;
; 0.306 ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.427      ;
; 0.306 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[0]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.038      ; 0.428      ;
; 0.306 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]            ; zhuhoujia2023111413_005:dht11_sensor|timer[6]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.038      ; 0.428      ;
; 0.307 ; zhuhoujia2023111413_006:lcd_display|delay_counter[12]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[12]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.428      ;
; 0.307 ; zhuhoujia2023111413_006:lcd_display|delay_counter[10]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[10]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.428      ;
; 0.307 ; zhuhoujia2023111413_005:dht11_sensor|timer[14]           ; zhuhoujia2023111413_005:dht11_sensor|timer[14]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.428      ;
; 0.307 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.436      ;
; 0.309 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.049      ; 0.442      ;
; 0.309 ; zhuhoujia2023111413_005:dht11_sensor|timer[13]           ; zhuhoujia2023111413_005:dht11_sensor|timer[13]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.430      ;
; 0.309 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.438      ;
; 0.309 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.429      ;
; 0.310 ; zhuhoujia2023111413_005:dht11_sensor|timer[18]           ; zhuhoujia2023111413_005:dht11_sensor|timer[18]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.431      ;
; 0.310 ; zhuhoujia2023111413_005:dht11_sensor|timer[16]           ; zhuhoujia2023111413_005:dht11_sensor|timer[16]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.431      ;
; 0.310 ; zhuhoujia2023111413_005:dht11_sensor|timer[17]           ; zhuhoujia2023111413_005:dht11_sensor|timer[17]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.431      ;
; 0.311 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.440      ;
; 0.311 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.440      ;
; 0.312 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]            ; zhuhoujia2023111413_005:dht11_sensor|timer[8]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.038      ; 0.434      ;
; 0.313 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.433      ;
; 0.314 ; zhuhoujia2023111413_005:dht11_sensor|timer[15]           ; zhuhoujia2023111413_005:dht11_sensor|timer[15]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.435      ;
; 0.316 ; zhuhoujia2023111413_006:lcd_display|delay_counter[0]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[0]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.437      ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+


----------------------------------------------
; Fast 1200mV 0C Model Metastability Summary ;
----------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------+
; Multicorner Timing Analysis Summary                                            ;
+------------------+----------+-------+----------+---------+---------------------+
; Clock            ; Setup    ; Hold  ; Recovery ; Removal ; Minimum Pulse Width ;
+------------------+----------+-------+----------+---------+---------------------+
; Worst-case Slack ; -4.237   ; 0.178 ; N/A      ; N/A     ; -3.000              ;
;  clk_50MHz       ; -4.237   ; 0.178 ; N/A      ; N/A     ; -3.000              ;
; Design-wide TNS  ; -331.662 ; 0.0   ; 0.0      ; 0.0     ; -202.258            ;
;  clk_50MHz       ; -331.662 ; 0.000 ; N/A      ; N/A     ; -202.258            ;
+------------------+----------+-------+----------+---------+---------------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Board Trace Model Assignments                                                                                                                                                                                                                                                                                                                                                                                    ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; Pin           ; I/O Standard ; Near Tline Length ; Near Tline L per Length ; Near Tline C per Length ; Near Series R ; Near Differential R ; Near Pull-up R ; Near Pull-down R ; Near C ; Far Tline Length ; Far Tline L per Length ; Far Tline C per Length ; Far Series R ; Far Pull-up R ; Far Pull-down R ; Far C ; Termination Voltage ; Far Differential R ; EBD File Name ; EBD Signal Name ; EBD Far-end ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; motor_dir[0]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[1]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[2]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[3]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm[0]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm[1]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm[2]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm[3]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_rst       ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_rw        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_en        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[0]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[1]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[2]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[3]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[4]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[5]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[6]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[7]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; dht11_data    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+


+----------------------------------------------------------------------------+
; Input Transition Times                                                     ;
+-------------------------+--------------+-----------------+-----------------+
; Pin                     ; I/O Standard ; 10-90 Rise Time ; 90-10 Fall Time ;
+-------------------------+--------------+-----------------+-----------------+
; dht11_data              ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; clk_50MHz               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; rst_n                   ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[0]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[1]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[2]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[3]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; obstacle_sensor[0]      ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; obstacle_sensor[1]      ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_ASDO_DATA1~     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_FLASH_nCE_nCSO~ ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_DATA0~          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
+-------------------------+--------------+-----------------+-----------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; motor_dir[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.33 V              ; -0.00425 V          ; 0.168 V                              ; 0.058 V                              ; 3.12e-09 s                  ; 2.87e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.33 V             ; -0.00425 V         ; 0.168 V                             ; 0.058 V                             ; 3.12e-09 s                 ; 2.87e-09 s                 ; Yes                       ; Yes                       ;
; motor_pwm[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rst       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.33 V              ; -0.00421 V          ; 0.165 V                              ; 0.078 V                              ; 3.11e-09 s                  ; 2.85e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.33 V             ; -0.00421 V         ; 0.165 V                             ; 0.078 V                             ; 3.11e-09 s                 ; 2.85e-09 s                 ; Yes                       ; Yes                       ;
; lcd_rw        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_en        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; lcd_data[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; lcd_data[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; dht11_data    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.74e-09 V                   ; 2.37 V              ; -0.0346 V           ; 0.198 V                              ; 0.094 V                              ; 3.14e-10 s                  ; 2.92e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.74e-09 V                  ; 2.37 V             ; -0.0346 V          ; 0.198 V                             ; 0.094 V                             ; 3.14e-10 s                 ; 2.92e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.57e-09 V                   ; 2.37 V              ; -0.00683 V          ; 0.171 V                              ; 0.018 V                              ; 4.97e-10 s                  ; 6.66e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.57e-09 V                  ; 2.37 V             ; -0.00683 V         ; 0.171 V                             ; 0.018 V                             ; 4.97e-10 s                 ; 6.66e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 85c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; motor_dir[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.33 V              ; -0.00229 V          ; 0.111 V                              ; 0.057 V                              ; 3.78e-09 s                  ; 3.5e-09 s                   ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.33 V             ; -0.00229 V         ; 0.111 V                             ; 0.057 V                             ; 3.78e-09 s                 ; 3.5e-09 s                  ; Yes                       ; Yes                       ;
; motor_pwm[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rst       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.33 V              ; -0.0022 V           ; 0.088 V                              ; 0.056 V                              ; 3.76e-09 s                  ; 3.48e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.33 V             ; -0.0022 V          ; 0.088 V                             ; 0.056 V                             ; 3.76e-09 s                 ; 3.48e-09 s                 ; Yes                       ; Yes                       ;
; lcd_rw        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_en        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; dht11_data    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.55e-07 V                   ; 2.35 V              ; -0.00221 V          ; 0.097 V                              ; 0.005 V                              ; 4.49e-10 s                  ; 3.85e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.55e-07 V                  ; 2.35 V             ; -0.00221 V         ; 0.097 V                             ; 0.005 V                             ; 4.49e-10 s                 ; 3.85e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.54e-07 V                   ; 2.34 V              ; -0.00774 V          ; 0.109 V                              ; 0.026 V                              ; 6.58e-10 s                  ; 8.24e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.54e-07 V                  ; 2.34 V             ; -0.00774 V         ; 0.109 V                             ; 0.026 V                             ; 6.58e-10 s                 ; 8.24e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Fast 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; motor_dir[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.64 V              ; -0.0113 V           ; 0.208 V                              ; 0.179 V                              ; 2.38e-09 s                  ; 2.23e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.64 V             ; -0.0113 V          ; 0.208 V                             ; 0.179 V                             ; 2.38e-09 s                 ; 2.23e-09 s                 ; No                        ; Yes                       ;
; motor_pwm[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rst       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.64 V              ; -0.0117 V           ; 0.202 V                              ; 0.176 V                              ; 2.38e-09 s                  ; 2.22e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.64 V             ; -0.0117 V          ; 0.202 V                             ; 0.176 V                             ; 2.38e-09 s                 ; 2.22e-09 s                 ; No                        ; Yes                       ;
; lcd_rw        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_en        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; lcd_data[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; lcd_data[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; dht11_data    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.22e-08 V                   ; 2.74 V              ; -0.06 V             ; 0.158 V                              ; 0.08 V                               ; 2.68e-10 s                  ; 2.19e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.22e-08 V                  ; 2.74 V             ; -0.06 V            ; 0.158 V                             ; 0.08 V                              ; 2.68e-10 s                 ; 2.19e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 3.54e-08 V                   ; 2.7 V               ; -0.00943 V          ; 0.276 V                              ; 0.035 V                              ; 3.19e-10 s                  ; 4.99e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 3.54e-08 V                  ; 2.7 V              ; -0.00943 V         ; 0.276 V                             ; 0.035 V                             ; 3.19e-10 s                 ; 4.99e-10 s                 ; No                        ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------+
; Setup Transfers                                                    ;
+------------+-----------+----------+----------+----------+----------+
; From Clock ; To Clock  ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+------------+-----------+----------+----------+----------+----------+
; clk_50MHz  ; clk_50MHz ; 3919     ; 0        ; 0        ; 0        ;
+------------+-----------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


+--------------------------------------------------------------------+
; Hold Transfers                                                     ;
+------------+-----------+----------+----------+----------+----------+
; From Clock ; To Clock  ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+------------+-----------+----------+----------+----------+----------+
; clk_50MHz  ; clk_50MHz ; 3919     ; 0        ; 0        ; 0        ;
+------------+-----------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


---------------
; Report TCCS ;
---------------
No dedicated SERDES Transmitter circuitry present in device or used in design


---------------
; Report RSKM ;
---------------
No non-DPA dedicated SERDES Receiver circuitry present in device or used in design


+------------------------------------------------+
; Unconstrained Paths Summary                    ;
+---------------------------------+-------+------+
; Property                        ; Setup ; Hold ;
+---------------------------------+-------+------+
; Illegal Clocks                  ; 0     ; 0    ;
; Unconstrained Clocks            ; 0     ; 0    ;
; Unconstrained Input Ports       ; 8     ; 8    ;
; Unconstrained Input Port Paths  ; 192   ; 192  ;
; Unconstrained Output Ports      ; 19    ; 19   ;
; Unconstrained Output Port Paths ; 20    ; 20   ;
+---------------------------------+-------+------+


+--------------------------------------------+
; Clock Status Summary                       ;
+-----------+-----------+------+-------------+
; Target    ; Clock     ; Type ; Status      ;
+-----------+-----------+------+-------------+
; clk_50MHz ; clk_50MHz ; Base ; Constrained ;
+-----------+-----------+------+-------------+


+-----------------------------------------------------------------------------------------------------------+
; Unconstrained Input Ports                                                                                 ;
+--------------------+--------------------------------------------------------------------------------------+
; Input Port         ; Comment                                                                              ;
+--------------------+--------------------------------------------------------------------------------------+
; dht11_data         ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[0]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[1]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[2]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[3]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[0] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[1] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; rst_n              ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------------+--------------------------------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------+
; Unconstrained Output Ports                                                                           ;
+--------------+---------------------------------------------------------------------------------------+
; Output Port  ; Comment                                                                               ;
+--------------+---------------------------------------------------------------------------------------+
; dht11_data   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[0]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[1]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[2]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[3]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[4]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[5]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[6]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[7]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_en       ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_rst      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------+---------------------------------------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------+
; Unconstrained Input Ports                                                                                 ;
+--------------------+--------------------------------------------------------------------------------------+
; Input Port         ; Comment                                                                              ;
+--------------------+--------------------------------------------------------------------------------------+
; dht11_data         ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[0]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[1]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[2]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[3]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[0] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[1] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; rst_n              ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------------+--------------------------------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------+
; Unconstrained Output Ports                                                                           ;
+--------------+---------------------------------------------------------------------------------------+
; Output Port  ; Comment                                                                               ;
+--------------+---------------------------------------------------------------------------------------+
; dht11_data   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[0]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[1]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[2]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[3]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[4]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[5]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[6]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[7]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_en       ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_rst      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------+---------------------------------------------------------------------------------------+


+--------------------------+
; Timing Analyzer Messages ;
+--------------------------+
Info: *******************************************************************
Info: Running Quartus Prime Timing Analyzer
    Info: Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
    Info: Processing started: Thu Jul 17 01:46:05 2025
Info: Command: quartus_sta 11 -c 11
Info: qsta_default_script.tcl version: #1
Warning (18236): Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance.
Info (20030): Parallel compilation is enabled and will use 14 of the 14 processors detected
Info (21077): Low junction temperature is 0 degrees C
Info (21077): High junction temperature is 85 degrees C
Critical Warning (332012): Synopsys Design Constraints File file not found: '11.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design.
Info (332142): No user constrained base clocks found in the design. Calling "derive_clocks -period 1.0"
Info (332105): Deriving Clocks
    Info (332105): create_clock -period 1.000 -name clk_50MHz clk_50MHz
Info (332143): No user constrained clock uncertainty found in the design. Calling "derive_clock_uncertainty"
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Info: Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON
Info: Analyzing Slow 1200mV 85C Model
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -4.237
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -4.237            -331.662 clk_50MHz 
Info (332146): Worst-case hold slack is 0.432
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.432               0.000 clk_50MHz 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000            -202.258 clk_50MHz 
Info: Analyzing Slow 1200mV 0C Model
Info (334003): Started post-fitting delay annotation
Info (334004): Delay annotation completed successfully
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -3.980
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.980            -303.642 clk_50MHz 
Info (332146): Worst-case hold slack is 0.381
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.381               0.000 clk_50MHz 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000            -202.258 clk_50MHz 
Info: Analyzing Fast 1200mV 0C Model
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -1.229
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -1.229             -74.929 clk_50MHz 
Info (332146): Worst-case hold slack is 0.178
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.178               0.000 clk_50MHz 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000            -146.061 clk_50MHz 
Info (332102): Design is not fully constrained for setup requirements
Info (332102): Design is not fully constrained for hold requirements
Info: Quartus Prime Timing Analyzer was successful. 0 errors, 5 warnings
    Info: Peak virtual memory: 4875 megabytes
    Info: Processing ended: Thu Jul 17 01:46:07 2025
    Info: Elapsed time: 00:00:02
    Info: Total CPU time (on all processors): 00:00:02


