Timing Analyzer report for 11
Thu Jul 17 19:51:23 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Timing Analyzer Summary
  3. Parallel Compilation
  4. Clocks
  5. Slow 1200mV 85C Model Fmax Summary
  6. Timing Closure Recommendations
  7. Slow 1200mV 85C Model Setup Summary
  8. Slow 1200mV 85C Model Hold Summary
  9. Slow 1200mV 85C Model Recovery Summary
 10. Slow 1200mV 85C Model Removal Summary
 11. Slow 1200mV 85C Model Minimum Pulse Width Summary
 12. Slow 1200mV 85C Model Setup: 'clk_50MHz'
 13. Slow 1200mV 85C Model Hold: 'clk_50MHz'
 14. Slow 1200mV 85C Model Metastability Summary
 15. Slow 1200mV 0C Model Fmax Summary
 16. Slow 1200mV 0C Model Setup Summary
 17. Slow 1200mV 0C Model Hold Summary
 18. Slow 1200mV 0C Model Recovery Summary
 19. Slow 1200mV 0C Model Removal Summary
 20. Slow 1200mV 0C Model Minimum Pulse Width Summary
 21. Slow 1200mV 0C Model Setup: 'clk_50MHz'
 22. Slow 1200mV 0C Model Hold: 'clk_50MHz'
 23. Slow 1200mV 0C Model Metastability Summary
 24. Fast 1200mV 0C Model Setup Summary
 25. Fast 1200mV 0C Model Hold Summary
 26. Fast 1200mV 0C Model Recovery Summary
 27. Fast 1200mV 0C Model Removal Summary
 28. Fast 1200mV 0C Model Minimum Pulse Width Summary
 29. Fast 1200mV 0C Model Setup: 'clk_50MHz'
 30. Fast 1200mV 0C Model Hold: 'clk_50MHz'
 31. Fast 1200mV 0C Model Metastability Summary
 32. Multicorner Timing Analysis Summary
 33. Board Trace Model Assignments
 34. Input Transition Times
 35. Signal Integrity Metrics (Slow 1200mv 0c Model)
 36. Signal Integrity Metrics (Slow 1200mv 85c Model)
 37. Signal Integrity Metrics (Fast 1200mv 0c Model)
 38. Setup Transfers
 39. Hold Transfers
 40. Report TCCS
 41. Report RSKM
 42. Unconstrained Paths Summary
 43. Clock Status Summary
 44. Unconstrained Input Ports
 45. Unconstrained Output Ports
 46. Unconstrained Input Ports
 47. Unconstrained Output Ports
 48. Timing Analyzer Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+-----------------------------------------------------------------------------+
; Timing Analyzer Summary                                                     ;
+-----------------------+-----------------------------------------------------+
; Quartus Prime Version ; Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition ;
; Timing Analyzer       ; Legacy Timing Analyzer                              ;
; Revision Name         ; 11                                                  ;
; Device Family         ; Cyclone IV E                                        ;
; Device Name           ; EP4CE6E22C8                                         ;
; Timing Models         ; Final                                               ;
; Delay Model           ; Combined                                            ;
; Rise/Fall Delays      ; Enabled                                             ;
+-----------------------+-----------------------------------------------------+


+------------------------------------------+
; Parallel Compilation                     ;
+----------------------------+-------------+
; Processors                 ; Number      ;
+----------------------------+-------------+
; Number detected on machine ; 20          ;
; Maximum allowed            ; 14          ;
;                            ;             ;
; Average used               ; 1.09        ;
; Maximum used               ; 14          ;
;                            ;             ;
; Usage by Processor         ; % Time Used ;
;     Processor 1            ; 100.0%      ;
;     Processors 2-14        ;   0.7%      ;
+----------------------------+-------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clocks                                                                                                                                                                                ;
+------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------------+
; Clock Name ; Type ; Period ; Frequency  ; Rise  ; Fall  ; Duty Cycle ; Divide by ; Multiply by ; Phase ; Offset ; Edge List ; Edge Shift ; Inverted ; Master ; Source ; Targets       ;
+------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------------+
; clk_50MHz  ; Base ; 1.000  ; 1000.0 MHz ; 0.000 ; 0.500 ;            ;           ;             ;       ;        ;           ;            ;          ;        ;        ; { clk_50MHz } ;
+------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------------+


+--------------------------------------------------+
; Slow 1200mV 85C Model Fmax Summary               ;
+------------+-----------------+------------+------+
; Fmax       ; Restricted Fmax ; Clock Name ; Note ;
+------------+-----------------+------------+------+
; 194.82 MHz ; 194.82 MHz      ; clk_50MHz  ;      ;
+------------+-----------------+------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


----------------------------------
; Timing Closure Recommendations ;
----------------------------------
HTML report is unavailable in plain text report export.


+-------------------------------------+
; Slow 1200mV 85C Model Setup Summary ;
+-----------+--------+----------------+
; Clock     ; Slack  ; End Point TNS  ;
+-----------+--------+----------------+
; clk_50MHz ; -4.133 ; -330.271       ;
+-----------+--------+----------------+


+------------------------------------+
; Slow 1200mV 85C Model Hold Summary ;
+-----------+-------+----------------+
; Clock     ; Slack ; End Point TNS  ;
+-----------+-------+----------------+
; clk_50MHz ; 0.433 ; 0.000          ;
+-----------+-------+----------------+


------------------------------------------
; Slow 1200mV 85C Model Recovery Summary ;
------------------------------------------
No paths to report.


-----------------------------------------
; Slow 1200mV 85C Model Removal Summary ;
-----------------------------------------
No paths to report.


+---------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width Summary ;
+-----------+--------+------------------------------+
; Clock     ; Slack  ; End Point TNS                ;
+-----------+--------+------------------------------+
; clk_50MHz ; -3.000 ; -194.823                     ;
+-----------+--------+------------------------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'clk_50MHz'                                                                                                                                      ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node                                      ; To Node                                       ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+
; -4.133 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.574     ; 4.560      ;
; -4.133 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.574     ; 4.560      ;
; -4.133 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.574     ; 4.560      ;
; -4.133 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.574     ; 4.560      ;
; -4.133 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.574     ; 4.560      ;
; -4.133 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.574     ; 4.560      ;
; -4.133 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.574     ; 4.560      ;
; -4.133 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.574     ; 4.560      ;
; -4.133 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.574     ; 4.560      ;
; -4.070 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.983      ;
; -4.070 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.983      ;
; -4.070 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.983      ;
; -4.070 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.983      ;
; -4.070 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.983      ;
; -4.070 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.983      ;
; -4.070 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.983      ;
; -4.070 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.983      ;
; -4.070 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.983      ;
; -4.004 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.917      ;
; -4.004 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.917      ;
; -4.004 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.917      ;
; -4.004 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.917      ;
; -4.004 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.917      ;
; -4.004 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.917      ;
; -4.004 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.917      ;
; -4.004 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.917      ;
; -4.004 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.917      ;
; -3.989 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.902      ;
; -3.989 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.902      ;
; -3.989 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.902      ;
; -3.989 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.902      ;
; -3.989 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.902      ;
; -3.989 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.902      ;
; -3.989 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.902      ;
; -3.989 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.902      ;
; -3.989 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.902      ;
; -3.957 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.877      ;
; -3.957 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.877      ;
; -3.957 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.877      ;
; -3.957 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.877      ;
; -3.957 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.877      ;
; -3.957 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.877      ;
; -3.957 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.877      ;
; -3.957 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.877      ;
; -3.957 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.877      ;
; -3.945 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.865      ;
; -3.945 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.865      ;
; -3.945 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.865      ;
; -3.945 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.865      ;
; -3.945 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.865      ;
; -3.945 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.865      ;
; -3.945 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.865      ;
; -3.945 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.865      ;
; -3.945 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.865      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.861      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.861      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.861      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.861      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.861      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.861      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.861      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.861      ;
; -3.941 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.861      ;
; -3.940 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.860      ;
; -3.940 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.860      ;
; -3.940 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.860      ;
; -3.940 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.860      ;
; -3.940 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.860      ;
; -3.940 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.860      ;
; -3.940 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.860      ;
; -3.940 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.860      ;
; -3.940 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.860      ;
; -3.920 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.833      ;
; -3.920 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.833      ;
; -3.920 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.833      ;
; -3.920 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.833      ;
; -3.920 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.833      ;
; -3.920 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.833      ;
; -3.920 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.833      ;
; -3.920 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.833      ;
; -3.920 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.833      ;
; -3.891 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.811      ;
; -3.891 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.811      ;
; -3.891 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.811      ;
; -3.891 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.811      ;
; -3.891 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.811      ;
; -3.891 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.811      ;
; -3.891 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.811      ;
; -3.891 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.811      ;
; -3.891 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.811      ;
; -3.875 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.788      ;
; -3.875 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.788      ;
; -3.875 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.788      ;
; -3.875 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.788      ;
; -3.875 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.788      ;
; -3.875 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.788      ;
; -3.875 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.788      ;
; -3.875 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.788      ;
; -3.875 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.088     ; 4.788      ;
; -3.871 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 4.791      ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'clk_50MHz'                                                                                                                                                         ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node                                                ; To Node                                                ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[31]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[31] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[15]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[15] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[30]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[30] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[14]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[14] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[29]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[29] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[12]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[12] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[27]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[27] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[11]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[11] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[26]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[26] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[10]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[10] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]    ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[25]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[25] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.433 ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]    ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.746      ;
; 0.445 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.758      ;
; 0.452 ; zhuhoujia2023111413_005:dht11_sensor|received_data[28]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[28] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.746      ;
; 0.452 ; zhuhoujia2023111413_005:dht11_sensor|received_data[24]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[24] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|received_data[13]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[13] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_006:lcd_display|lcd_en               ; zhuhoujia2023111413_006:lcd_display|lcd_en             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_006:lcd_display|lcd_rst              ; zhuhoujia2023111413_006:lcd_display|lcd_rst            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_006:lcd_display|state.INIT           ; zhuhoujia2023111413_006:lcd_display|state.INIT         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP   ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_006:lcd_display|state.RESET          ; zhuhoujia2023111413_006:lcd_display|state.RESET        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM    ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE      ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE          ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.START         ; zhuhoujia2023111413_005:dht11_sensor|state.START       ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE       ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.465 ; zhuhoujia2023111413_006:lcd_display|state.IDLE           ; zhuhoujia2023111413_006:lcd_display|state.IDLE         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.758      ;
; 0.465 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.758      ;
; 0.497 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[2] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.790      ;
; 0.500 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.793      ;
; 0.503 ; zhuhoujia2023111413_005:dht11_sensor|temperature[4]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[4]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.795      ;
; 0.504 ; zhuhoujia2023111413_005:dht11_sensor|temperature[7]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[7]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.796      ;
; 0.516 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.809      ;
; 0.521 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[2] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.814      ;
; 0.534 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.827      ;
; 0.542 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.835      ;
; 0.556 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.849      ;
; 0.606 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.574      ; 1.392      ;
; 0.642 ; zhuhoujia2023111413_005:dht11_sensor|humidity[7]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[7]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.934      ;
; 0.643 ; zhuhoujia2023111413_005:dht11_sensor|humidity[5]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[5]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.935      ;
; 0.643 ; zhuhoujia2023111413_005:dht11_sensor|humidity[3]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.935      ;
; 0.643 ; zhuhoujia2023111413_005:dht11_sensor|humidity[4]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[4]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.935      ;
; 0.645 ; zhuhoujia2023111413_001:line_follow|control_signal[0]    ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.938      ;
; 0.647 ; zhuhoujia2023111413_001:line_follow|control_signal[1]    ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.940      ;
; 0.663 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.956      ;
; 0.675 ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.968      ;
; 0.678 ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.971      ;
; 0.710 ; zhuhoujia2023111413_005:dht11_sensor|temperature[5]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[5]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.003      ;
; 0.722 ; zhuhoujia2023111413_005:dht11_sensor|temperature[3]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.014      ;
; 0.745 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]            ; zhuhoujia2023111413_005:dht11_sensor|timer[3]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.038      ;
; 0.746 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.058      ;
; 0.754 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 1.065      ;
; 0.755 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.048      ;
; 0.756 ; zhuhoujia2023111413_005:dht11_sensor|data_valid          ; zhuhoujia2023111413_006:lcd_display|state.IDLE         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.049      ;
; 0.761 ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.054      ;
; 0.762 ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.055      ;
; 0.762 ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.055      ;
; 0.762 ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.055      ;
; 0.762 ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.055      ;
; 0.763 ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.056      ;
; 0.763 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[0]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.056      ;
; 0.763 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.056      ;
; 0.763 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.574      ; 1.549      ;
; 0.764 ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.057      ;
; 0.764 ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.057      ;
; 0.764 ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.057      ;
; 0.764 ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.057      ;
; 0.764 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.057      ;
; 0.765 ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.058      ;
; 0.765 ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.058      ;
; 0.766 ; zhuhoujia2023111413_006:lcd_display|delay_counter[12]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[12]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.059      ;
; 0.766 ; zhuhoujia2023111413_006:lcd_display|delay_counter[10]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[10]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.059      ;
; 0.766 ; zhuhoujia2023111413_006:lcd_display|delay_counter[8]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[8]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.059      ;
; 0.769 ; zhuhoujia2023111413_005:dht11_sensor|timer[11]           ; zhuhoujia2023111413_005:dht11_sensor|timer[11]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.062      ;
; 0.769 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 1.080      ;
; 0.771 ; zhuhoujia2023111413_005:dht11_sensor|timer[10]           ; zhuhoujia2023111413_005:dht11_sensor|timer[10]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.064      ;
; 0.771 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 1.082      ;
; 0.772 ; zhuhoujia2023111413_005:dht11_sensor|timer[12]           ; zhuhoujia2023111413_005:dht11_sensor|timer[12]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.065      ;
; 0.772 ; zhuhoujia2023111413_005:dht11_sensor|timer[16]           ; zhuhoujia2023111413_005:dht11_sensor|timer[16]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.065      ;
; 0.772 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 1.083      ;
; 0.773 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.066      ;
; 0.774 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.067      ;
; 0.775 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]            ; zhuhoujia2023111413_005:dht11_sensor|timer[7]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.068      ;
; 0.775 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]            ; zhuhoujia2023111413_005:dht11_sensor|timer[9]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.068      ;
; 0.775 ; zhuhoujia2023111413_005:dht11_sensor|timer[18]           ; zhuhoujia2023111413_005:dht11_sensor|timer[18]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.068      ;
; 0.776 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 1.087      ;
; 0.778 ; zhuhoujia2023111413_005:dht11_sensor|timer[15]           ; zhuhoujia2023111413_005:dht11_sensor|timer[15]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.071      ;
; 0.779 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 1.090      ;
; 0.782 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.075      ;
; 0.783 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.077      ;
; 0.783 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.077      ;
; 0.783 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.076      ;
; 0.785 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.079      ;
; 0.786 ; zhuhoujia2023111413_006:lcd_display|delay_counter[0]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[0]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.079      ;
; 0.787 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.081      ;
; 0.787 ; zhuhoujia2023111413_006:lcd_display|state.INIT           ; zhuhoujia2023111413_006:lcd_display|lcd_en             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.080      ;
; 0.797 ; zhuhoujia2023111413_005:dht11_sensor|timer[14]           ; zhuhoujia2023111413_005:dht11_sensor|timer[14]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.090      ;
; 0.800 ; zhuhoujia2023111413_006:lcd_display|state.IDLE           ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.093      ;
; 0.808 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.101      ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+


-----------------------------------------------
; Slow 1200mV 85C Model Metastability Summary ;
-----------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------+
; Slow 1200mV 0C Model Fmax Summary                ;
+------------+-----------------+------------+------+
; Fmax       ; Restricted Fmax ; Clock Name ; Note ;
+------------+-----------------+------------+------+
; 204.25 MHz ; 204.25 MHz      ; clk_50MHz  ;      ;
+------------+-----------------+------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+------------------------------------+
; Slow 1200mV 0C Model Setup Summary ;
+-----------+--------+---------------+
; Clock     ; Slack  ; End Point TNS ;
+-----------+--------+---------------+
; clk_50MHz ; -3.896 ; -303.221      ;
+-----------+--------+---------------+


+-----------------------------------+
; Slow 1200mV 0C Model Hold Summary ;
+-----------+-------+---------------+
; Clock     ; Slack ; End Point TNS ;
+-----------+-------+---------------+
; clk_50MHz ; 0.383 ; 0.000         ;
+-----------+-------+---------------+


-----------------------------------------
; Slow 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Slow 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width Summary ;
+-----------+--------+-----------------------------+
; Clock     ; Slack  ; End Point TNS               ;
+-----------+--------+-----------------------------+
; clk_50MHz ; -3.000 ; -194.823                    ;
+-----------+--------+-----------------------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'clk_50MHz'                                                                                                                                       ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node                                      ; To Node                                       ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+
; -3.896 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.359      ;
; -3.896 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.359      ;
; -3.896 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.359      ;
; -3.896 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.359      ;
; -3.896 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.359      ;
; -3.896 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.359      ;
; -3.896 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.359      ;
; -3.896 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.359      ;
; -3.896 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.359      ;
; -3.826 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.746      ;
; -3.826 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.746      ;
; -3.826 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.746      ;
; -3.826 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.746      ;
; -3.826 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.746      ;
; -3.826 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.746      ;
; -3.826 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.746      ;
; -3.826 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.746      ;
; -3.826 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.746      ;
; -3.723 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.652      ;
; -3.723 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.652      ;
; -3.723 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.652      ;
; -3.723 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.652      ;
; -3.723 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.652      ;
; -3.723 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.652      ;
; -3.723 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.652      ;
; -3.723 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.652      ;
; -3.723 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.652      ;
; -3.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.646      ;
; -3.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.646      ;
; -3.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.646      ;
; -3.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.646      ;
; -3.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.646      ;
; -3.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.646      ;
; -3.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.646      ;
; -3.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.646      ;
; -3.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.646      ;
; -3.687 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.607      ;
; -3.687 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.607      ;
; -3.687 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.607      ;
; -3.687 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.607      ;
; -3.687 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.607      ;
; -3.687 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.607      ;
; -3.687 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.607      ;
; -3.687 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.607      ;
; -3.687 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.607      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.601      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.601      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.601      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.601      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.601      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.601      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.601      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.601      ;
; -3.672 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.601      ;
; -3.667 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.587      ;
; -3.667 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.587      ;
; -3.667 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.587      ;
; -3.667 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.587      ;
; -3.667 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.587      ;
; -3.667 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.587      ;
; -3.667 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.587      ;
; -3.667 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.587      ;
; -3.667 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.587      ;
; -3.663 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.592      ;
; -3.663 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.592      ;
; -3.663 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.592      ;
; -3.663 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.592      ;
; -3.663 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.592      ;
; -3.663 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.592      ;
; -3.663 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.592      ;
; -3.663 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.592      ;
; -3.663 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.592      ;
; -3.654 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.574      ;
; -3.654 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.574      ;
; -3.654 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.574      ;
; -3.654 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.574      ;
; -3.654 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.574      ;
; -3.654 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.574      ;
; -3.654 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.574      ;
; -3.654 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.574      ;
; -3.654 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.574      ;
; -3.645 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.574      ;
; -3.645 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.574      ;
; -3.645 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.574      ;
; -3.645 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.574      ;
; -3.645 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.574      ;
; -3.645 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.574      ;
; -3.645 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.574      ;
; -3.645 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.574      ;
; -3.645 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.574      ;
; -3.644 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.573      ;
; -3.644 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.573      ;
; -3.644 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.573      ;
; -3.644 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.573      ;
; -3.644 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.573      ;
; -3.644 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.573      ;
; -3.644 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.573      ;
; -3.644 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.573      ;
; -3.644 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.573      ;
; -3.628 ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.082     ; 4.548      ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'clk_50MHz'                                                                                                                                                          ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node                                                ; To Node                                                ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[31]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[31] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[15]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[15] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[30]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[30] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[14]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[14] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[29]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[29] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[12]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[12] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[27]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[27] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[11]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[11] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[26]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[26] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[10]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[10] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]    ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[25]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[25] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.383 ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]    ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.669      ;
; 0.398 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.684      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|received_data[13]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[13] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|received_data[28]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[28] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|received_data[24]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[24] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_006:lcd_display|lcd_rst              ; zhuhoujia2023111413_006:lcd_display|lcd_rst            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE      ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE          ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|state.START         ; zhuhoujia2023111413_005:dht11_sensor|state.START       ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE       ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_006:lcd_display|lcd_en               ; zhuhoujia2023111413_006:lcd_display|lcd_en             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_006:lcd_display|state.INIT           ; zhuhoujia2023111413_006:lcd_display|state.INIT         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP   ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_006:lcd_display|state.RESET          ; zhuhoujia2023111413_006:lcd_display|state.RESET        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM    ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.416 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.684      ;
; 0.417 ; zhuhoujia2023111413_006:lcd_display|state.IDLE           ; zhuhoujia2023111413_006:lcd_display|state.IDLE         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.684      ;
; 0.460 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[2] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.728      ;
; 0.463 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.731      ;
; 0.471 ; zhuhoujia2023111413_005:dht11_sensor|temperature[4]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[4]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.738      ;
; 0.472 ; zhuhoujia2023111413_005:dht11_sensor|temperature[7]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[7]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.739      ;
; 0.486 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.754      ;
; 0.489 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[2] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.757      ;
; 0.496 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.764      ;
; 0.499 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.767      ;
; 0.518 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.786      ;
; 0.552 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.539      ; 1.286      ;
; 0.599 ; zhuhoujia2023111413_005:dht11_sensor|humidity[5]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[5]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.866      ;
; 0.599 ; zhuhoujia2023111413_005:dht11_sensor|humidity[3]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.866      ;
; 0.599 ; zhuhoujia2023111413_005:dht11_sensor|humidity[7]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[7]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.866      ;
; 0.600 ; zhuhoujia2023111413_005:dht11_sensor|humidity[4]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[4]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.867      ;
; 0.600 ; zhuhoujia2023111413_001:line_follow|control_signal[0]    ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.868      ;
; 0.603 ; zhuhoujia2023111413_001:line_follow|control_signal[1]    ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.871      ;
; 0.620 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.888      ;
; 0.626 ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.894      ;
; 0.628 ; zhuhoujia2023111413_005:dht11_sensor|temperature[5]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[5]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.895      ;
; 0.629 ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.897      ;
; 0.642 ; zhuhoujia2023111413_005:dht11_sensor|temperature[3]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.909      ;
; 0.678 ; zhuhoujia2023111413_005:dht11_sensor|data_valid          ; zhuhoujia2023111413_006:lcd_display|state.IDLE         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.945      ;
; 0.685 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.539      ; 1.419      ;
; 0.693 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.979      ;
; 0.694 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.963      ;
; 0.694 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.963      ;
; 0.694 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]            ; zhuhoujia2023111413_005:dht11_sensor|timer[3]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.962      ;
; 0.695 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.964      ;
; 0.698 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.967      ;
; 0.700 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.089      ; 0.984      ;
; 0.703 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.971      ;
; 0.705 ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.706 ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.974      ;
; 0.706 ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.974      ;
; 0.706 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.974      ;
; 0.707 ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.975      ;
; 0.707 ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.975      ;
; 0.708 ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.976      ;
; 0.708 ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.976      ;
; 0.708 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.976      ;
; 0.710 ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.978      ;
; 0.711 ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.979      ;
; 0.711 ; zhuhoujia2023111413_006:lcd_display|delay_counter[12]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[12]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.979      ;
; 0.711 ; zhuhoujia2023111413_006:lcd_display|delay_counter[10]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[10]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.979      ;
; 0.711 ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.979      ;
; 0.712 ; zhuhoujia2023111413_006:lcd_display|delay_counter[8]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[8]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.980      ;
; 0.714 ; zhuhoujia2023111413_005:dht11_sensor|timer[11]           ; zhuhoujia2023111413_005:dht11_sensor|timer[11]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.982      ;
; 0.716 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[0]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.984      ;
; 0.716 ; zhuhoujia2023111413_005:dht11_sensor|timer[10]           ; zhuhoujia2023111413_005:dht11_sensor|timer[10]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.984      ;
; 0.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[12]           ; zhuhoujia2023111413_005:dht11_sensor|timer[12]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.985      ;
; 0.717 ; zhuhoujia2023111413_005:dht11_sensor|timer[16]           ; zhuhoujia2023111413_005:dht11_sensor|timer[16]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.985      ;
; 0.717 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.089      ; 1.001      ;
; 0.718 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.089      ; 1.002      ;
; 0.719 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]            ; zhuhoujia2023111413_005:dht11_sensor|timer[7]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.987      ;
; 0.719 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]            ; zhuhoujia2023111413_005:dht11_sensor|timer[9]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.987      ;
; 0.719 ; zhuhoujia2023111413_005:dht11_sensor|timer[18]           ; zhuhoujia2023111413_005:dht11_sensor|timer[18]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.987      ;
; 0.720 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.089      ; 1.004      ;
; 0.720 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.988      ;
; 0.721 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.089      ; 1.005      ;
; 0.723 ; zhuhoujia2023111413_005:dht11_sensor|timer[15]           ; zhuhoujia2023111413_005:dht11_sensor|timer[15]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.991      ;
; 0.725 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.993      ;
; 0.728 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.089      ; 1.012      ;
; 0.730 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.998      ;
; 0.731 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.999      ;
; 0.733 ; zhuhoujia2023111413_006:lcd_display|delay_counter[0]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[0]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 1.001      ;
; 0.736 ; zhuhoujia2023111413_006:lcd_display|state.INIT           ; zhuhoujia2023111413_006:lcd_display|lcd_en             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 1.003      ;
; 0.740 ; zhuhoujia2023111413_005:dht11_sensor|timer[14]           ; zhuhoujia2023111413_005:dht11_sensor|timer[14]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 1.008      ;
; 0.752 ; zhuhoujia2023111413_006:lcd_display|state.IDLE           ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 1.019      ;
; 0.756 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 1.024      ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+


----------------------------------------------
; Slow 1200mV 0C Model Metastability Summary ;
----------------------------------------------
No synchronizer chains to report.


+------------------------------------+
; Fast 1200mV 0C Model Setup Summary ;
+-----------+--------+---------------+
; Clock     ; Slack  ; End Point TNS ;
+-----------+--------+---------------+
; clk_50MHz ; -1.176 ; -74.773       ;
+-----------+--------+---------------+


+-----------------------------------+
; Fast 1200mV 0C Model Hold Summary ;
+-----------+-------+---------------+
; Clock     ; Slack ; End Point TNS ;
+-----------+-------+---------------+
; clk_50MHz ; 0.178 ; 0.000         ;
+-----------+-------+---------------+


-----------------------------------------
; Fast 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Fast 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width Summary ;
+-----------+--------+-----------------------------+
; Clock     ; Slack  ; End Point TNS               ;
+-----------+--------+-----------------------------+
; clk_50MHz ; -3.000 ; -140.681                    ;
+-----------+--------+-----------------------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'clk_50MHz'                                                                                                                                       ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node                                      ; To Node                                       ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+
; -1.176 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.927      ;
; -1.176 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.927      ;
; -1.176 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.927      ;
; -1.176 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.927      ;
; -1.176 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.927      ;
; -1.176 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.927      ;
; -1.176 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.927      ;
; -1.176 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.927      ;
; -1.176 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.927      ;
; -1.158 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.104      ;
; -1.158 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.104      ;
; -1.158 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.104      ;
; -1.158 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.104      ;
; -1.158 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.104      ;
; -1.158 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.104      ;
; -1.158 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.104      ;
; -1.158 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.104      ;
; -1.158 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.104      ;
; -1.124 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.074      ;
; -1.124 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.074      ;
; -1.124 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.074      ;
; -1.124 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.074      ;
; -1.124 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.074      ;
; -1.124 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.074      ;
; -1.124 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.074      ;
; -1.124 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.074      ;
; -1.124 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.074      ;
; -1.118 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.068      ;
; -1.118 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.068      ;
; -1.118 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.068      ;
; -1.118 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.068      ;
; -1.118 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.068      ;
; -1.118 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.068      ;
; -1.118 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.068      ;
; -1.118 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.068      ;
; -1.118 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.068      ;
; -1.111 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.061      ;
; -1.111 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.061      ;
; -1.111 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.061      ;
; -1.111 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.061      ;
; -1.111 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.061      ;
; -1.111 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.061      ;
; -1.111 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.061      ;
; -1.111 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.061      ;
; -1.111 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.061      ;
; -1.103 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.049      ;
; -1.103 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.049      ;
; -1.103 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.049      ;
; -1.103 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.049      ;
; -1.103 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.049      ;
; -1.103 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.049      ;
; -1.103 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.049      ;
; -1.103 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.049      ;
; -1.103 ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.049      ;
; -1.086 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.032      ;
; -1.086 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.032      ;
; -1.086 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.032      ;
; -1.086 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.032      ;
; -1.086 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.032      ;
; -1.086 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.032      ;
; -1.086 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.032      ;
; -1.086 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.032      ;
; -1.086 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.032      ;
; -1.085 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.035      ;
; -1.085 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.035      ;
; -1.085 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.035      ;
; -1.085 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.035      ;
; -1.085 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.035      ;
; -1.085 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.035      ;
; -1.085 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.035      ;
; -1.085 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.035      ;
; -1.085 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.035      ;
; -1.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.034      ;
; -1.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.034      ;
; -1.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.034      ;
; -1.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.034      ;
; -1.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.034      ;
; -1.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.034      ;
; -1.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.034      ;
; -1.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.034      ;
; -1.084 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.034      ;
; -1.083 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.029      ;
; -1.083 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.029      ;
; -1.083 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.029      ;
; -1.083 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.029      ;
; -1.083 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.029      ;
; -1.083 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.029      ;
; -1.083 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.029      ;
; -1.083 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.029      ;
; -1.083 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.029      ;
; -1.078 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.028      ;
; -1.078 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.028      ;
; -1.078 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.028      ;
; -1.078 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.028      ;
; -1.078 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.028      ;
; -1.078 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.028      ;
; -1.078 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.028      ;
; -1.078 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.028      ;
; -1.078 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.028      ;
; -1.057 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.037     ; 2.007      ;
+--------+------------------------------------------------+-----------------------------------------------+--------------+-------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'clk_50MHz'                                                                                                                                                          ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node                                                ; To Node                                                ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[31]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[31] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[15]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[15] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[30]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[30] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[14]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[14] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[29]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[29] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[12]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[12] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[27]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[27] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[11]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[11] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[26]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[26] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[10]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[10] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]    ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[25]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[25] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.178 ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]    ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.307      ;
; 0.185 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.314      ;
; 0.186 ; zhuhoujia2023111413_005:dht11_sensor|received_data[13]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[13] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_005:dht11_sensor|received_data[28]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[28] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_005:dht11_sensor|received_data[24]   ; zhuhoujia2023111413_005:dht11_sensor|received_data[24] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_006:lcd_display|lcd_en               ; zhuhoujia2023111413_006:lcd_display|lcd_en             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_006:lcd_display|lcd_rst              ; zhuhoujia2023111413_006:lcd_display|lcd_rst            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_006:lcd_display|state.INIT           ; zhuhoujia2023111413_006:lcd_display|state.INIT         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP   ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_006:lcd_display|state.RESET          ; zhuhoujia2023111413_006:lcd_display|state.RESET        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM    ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE      ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE          ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_005:dht11_sensor|state.START         ; zhuhoujia2023111413_005:dht11_sensor|state.START       ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE       ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.193 ; zhuhoujia2023111413_005:dht11_sensor|temperature[4]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[4]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.314      ;
; 0.194 ; zhuhoujia2023111413_006:lcd_display|state.IDLE           ; zhuhoujia2023111413_006:lcd_display|state.IDLE         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.314      ;
; 0.194 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.314      ;
; 0.195 ; zhuhoujia2023111413_005:dht11_sensor|temperature[7]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[7]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.316      ;
; 0.201 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.322      ;
; 0.202 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.322      ;
; 0.203 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[2] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.323      ;
; 0.206 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[2] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.326      ;
; 0.220 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.340      ;
; 0.224 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.344      ;
; 0.226 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.346      ;
; 0.248 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.236      ; 0.568      ;
; 0.252 ; zhuhoujia2023111413_005:dht11_sensor|humidity[5]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[5]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.373      ;
; 0.252 ; zhuhoujia2023111413_005:dht11_sensor|humidity[3]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.373      ;
; 0.252 ; zhuhoujia2023111413_005:dht11_sensor|humidity[7]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[7]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.373      ;
; 0.253 ; zhuhoujia2023111413_005:dht11_sensor|humidity[4]         ; zhuhoujia2023111413_006:lcd_display|lcd_data[4]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.374      ;
; 0.253 ; zhuhoujia2023111413_001:line_follow|control_signal[0]    ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.373      ;
; 0.256 ; zhuhoujia2023111413_001:line_follow|control_signal[1]    ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.376      ;
; 0.265 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.385      ;
; 0.267 ; zhuhoujia2023111413_005:dht11_sensor|temperature[5]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[5]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.388      ;
; 0.268 ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.388      ;
; 0.270 ; zhuhoujia2023111413_005:dht11_sensor|temperature[3]      ; zhuhoujia2023111413_006:lcd_display|lcd_data[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.391      ;
; 0.271 ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]      ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.391      ;
; 0.290 ; zhuhoujia2023111413_005:dht11_sensor|data_valid          ; zhuhoujia2023111413_006:lcd_display|state.IDLE         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.410      ;
; 0.298 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]            ; zhuhoujia2023111413_005:dht11_sensor|timer[3]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.299 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.427      ;
; 0.301 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.422      ;
; 0.302 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.423      ;
; 0.302 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.423      ;
; 0.302 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.430      ;
; 0.302 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.423      ;
; 0.303 ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.424      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]      ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.306 ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.427      ;
; 0.306 ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.427      ;
; 0.306 ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.427      ;
; 0.306 ; zhuhoujia2023111413_006:lcd_display|delay_counter[8]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[8]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.427      ;
; 0.306 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.427      ;
; 0.307 ; zhuhoujia2023111413_006:lcd_display|delay_counter[12]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[12]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.428      ;
; 0.307 ; zhuhoujia2023111413_006:lcd_display|delay_counter[10]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[10]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.428      ;
; 0.307 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[0]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.428      ;
; 0.309 ; zhuhoujia2023111413_005:dht11_sensor|timer[10]           ; zhuhoujia2023111413_005:dht11_sensor|timer[10]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.429      ;
; 0.309 ; zhuhoujia2023111413_005:dht11_sensor|timer[11]           ; zhuhoujia2023111413_005:dht11_sensor|timer[11]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.429      ;
; 0.311 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]            ; zhuhoujia2023111413_005:dht11_sensor|timer[9]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.432      ;
; 0.311 ; zhuhoujia2023111413_005:dht11_sensor|timer[12]           ; zhuhoujia2023111413_005:dht11_sensor|timer[12]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.431      ;
; 0.311 ; zhuhoujia2023111413_005:dht11_sensor|timer[18]           ; zhuhoujia2023111413_005:dht11_sensor|timer[18]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.431      ;
; 0.311 ; zhuhoujia2023111413_005:dht11_sensor|timer[16]           ; zhuhoujia2023111413_005:dht11_sensor|timer[16]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.431      ;
; 0.311 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.439      ;
; 0.311 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.432      ;
; 0.311 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]           ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.432      ;
; 0.312 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]            ; zhuhoujia2023111413_005:dht11_sensor|timer[7]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.433      ;
; 0.312 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.440      ;
; 0.313 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.441      ;
; 0.313 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[1] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.433      ;
; 0.314 ; zhuhoujia2023111413_006:lcd_display|state.INIT           ; zhuhoujia2023111413_006:lcd_display|lcd_en             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.434      ;
; 0.315 ; zhuhoujia2023111413_005:dht11_sensor|timer[15]           ; zhuhoujia2023111413_005:dht11_sensor|timer[15]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.435      ;
; 0.315 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.443      ;
; 0.316 ; zhuhoujia2023111413_006:lcd_display|delay_counter[0]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[0]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.437      ;
; 0.316 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.444      ;
; 0.316 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.436      ;
; 0.323 ; zhuhoujia2023111413_005:dht11_sensor|timer[14]           ; zhuhoujia2023111413_005:dht11_sensor|timer[14]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.443      ;
; 0.326 ; zhuhoujia2023111413_006:lcd_display|state.IDLE           ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.446      ;
; 0.326 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.236      ; 0.646      ;
; 0.330 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.450      ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+


----------------------------------------------
; Fast 1200mV 0C Model Metastability Summary ;
----------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------+
; Multicorner Timing Analysis Summary                                            ;
+------------------+----------+-------+----------+---------+---------------------+
; Clock            ; Setup    ; Hold  ; Recovery ; Removal ; Minimum Pulse Width ;
+------------------+----------+-------+----------+---------+---------------------+
; Worst-case Slack ; -4.133   ; 0.178 ; N/A      ; N/A     ; -3.000              ;
;  clk_50MHz       ; -4.133   ; 0.178 ; N/A      ; N/A     ; -3.000              ;
; Design-wide TNS  ; -330.271 ; 0.0   ; 0.0      ; 0.0     ; -194.823            ;
;  clk_50MHz       ; -330.271 ; 0.000 ; N/A      ; N/A     ; -194.823            ;
+------------------+----------+-------+----------+---------+---------------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Board Trace Model Assignments                                                                                                                                                                                                                                                                                                                                                                                    ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; Pin           ; I/O Standard ; Near Tline Length ; Near Tline L per Length ; Near Tline C per Length ; Near Series R ; Near Differential R ; Near Pull-up R ; Near Pull-down R ; Near C ; Far Tline Length ; Far Tline L per Length ; Far Tline C per Length ; Far Series R ; Far Pull-up R ; Far Pull-down R ; Far C ; Termination Voltage ; Far Differential R ; EBD File Name ; EBD Signal Name ; EBD Far-end ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; motor_dir[0]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[1]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[2]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[3]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm[0]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm[1]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm[2]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm[3]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_rst       ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_rw        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_en        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[0]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[1]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[2]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[3]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[4]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[5]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[6]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[7]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; dht11_data    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+


+----------------------------------------------------------------------------+
; Input Transition Times                                                     ;
+-------------------------+--------------+-----------------+-----------------+
; Pin                     ; I/O Standard ; 10-90 Rise Time ; 90-10 Fall Time ;
+-------------------------+--------------+-----------------+-----------------+
; dht11_data              ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; clk_50MHz               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; rst_n                   ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; obstacle_sensor[1]      ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[0]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[1]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[2]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[3]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; obstacle_sensor[0]      ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_ASDO_DATA1~     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_FLASH_nCE_nCSO~ ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_DATA0~          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
+-------------------------+--------------+-----------------+-----------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; motor_dir[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.33 V              ; -0.00425 V          ; 0.168 V                              ; 0.058 V                              ; 3.12e-09 s                  ; 2.87e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.33 V             ; -0.00425 V         ; 0.168 V                             ; 0.058 V                             ; 3.12e-09 s                 ; 2.87e-09 s                 ; Yes                       ; Yes                       ;
; motor_pwm[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rst       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rw        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_en        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; lcd_data[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.33 V              ; -0.00421 V          ; 0.165 V                              ; 0.078 V                              ; 3.11e-09 s                  ; 2.85e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.33 V             ; -0.00421 V         ; 0.165 V                             ; 0.078 V                             ; 3.11e-09 s                 ; 2.85e-09 s                 ; Yes                       ; Yes                       ;
; dht11_data    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.74e-09 V                   ; 2.37 V              ; -0.0346 V           ; 0.198 V                              ; 0.094 V                              ; 3.14e-10 s                  ; 2.92e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.74e-09 V                  ; 2.37 V             ; -0.0346 V          ; 0.198 V                             ; 0.094 V                             ; 3.14e-10 s                 ; 2.92e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.57e-09 V                   ; 2.37 V              ; -0.00683 V          ; 0.171 V                              ; 0.018 V                              ; 4.97e-10 s                  ; 6.66e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.57e-09 V                  ; 2.37 V             ; -0.00683 V         ; 0.171 V                             ; 0.018 V                             ; 4.97e-10 s                 ; 6.66e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 85c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; motor_dir[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.33 V              ; -0.00229 V          ; 0.111 V                              ; 0.057 V                              ; 3.78e-09 s                  ; 3.5e-09 s                   ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.33 V             ; -0.00229 V         ; 0.111 V                             ; 0.057 V                             ; 3.78e-09 s                 ; 3.5e-09 s                  ; Yes                       ; Yes                       ;
; motor_pwm[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rst       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rw        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_en        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.33 V              ; -0.0022 V           ; 0.088 V                              ; 0.056 V                              ; 3.76e-09 s                  ; 3.48e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.33 V             ; -0.0022 V          ; 0.088 V                             ; 0.056 V                             ; 3.76e-09 s                 ; 3.48e-09 s                 ; Yes                       ; Yes                       ;
; dht11_data    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.55e-07 V                   ; 2.35 V              ; -0.00221 V          ; 0.097 V                              ; 0.005 V                              ; 4.49e-10 s                  ; 3.85e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.55e-07 V                  ; 2.35 V             ; -0.00221 V         ; 0.097 V                             ; 0.005 V                             ; 4.49e-10 s                 ; 3.85e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.54e-07 V                   ; 2.34 V              ; -0.00774 V          ; 0.109 V                              ; 0.026 V                              ; 6.58e-10 s                  ; 8.24e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.54e-07 V                  ; 2.34 V             ; -0.00774 V         ; 0.109 V                             ; 0.026 V                             ; 6.58e-10 s                 ; 8.24e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Fast 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; motor_dir[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; motor_dir[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.64 V              ; -0.0113 V           ; 0.208 V                              ; 0.179 V                              ; 2.38e-09 s                  ; 2.23e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.64 V             ; -0.0113 V          ; 0.208 V                             ; 0.179 V                             ; 2.38e-09 s                 ; 2.23e-09 s                 ; No                        ; Yes                       ;
; motor_pwm[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rst       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rw        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_en        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; lcd_data[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; lcd_data[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; lcd_data[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; lcd_data[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.64 V              ; -0.0117 V           ; 0.202 V                              ; 0.176 V                              ; 2.38e-09 s                  ; 2.22e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.64 V             ; -0.0117 V          ; 0.202 V                             ; 0.176 V                             ; 2.38e-09 s                 ; 2.22e-09 s                 ; No                        ; Yes                       ;
; dht11_data    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.22e-08 V                   ; 2.74 V              ; -0.06 V             ; 0.158 V                              ; 0.08 V                               ; 2.68e-10 s                  ; 2.19e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.22e-08 V                  ; 2.74 V             ; -0.06 V            ; 0.158 V                             ; 0.08 V                              ; 2.68e-10 s                 ; 2.19e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 3.54e-08 V                   ; 2.7 V               ; -0.00943 V          ; 0.276 V                              ; 0.035 V                              ; 3.19e-10 s                  ; 4.99e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 3.54e-08 V                  ; 2.7 V              ; -0.00943 V         ; 0.276 V                             ; 0.035 V                             ; 3.19e-10 s                 ; 4.99e-10 s                 ; No                        ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------+
; Setup Transfers                                                    ;
+------------+-----------+----------+----------+----------+----------+
; From Clock ; To Clock  ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+------------+-----------+----------+----------+----------+----------+
; clk_50MHz  ; clk_50MHz ; 3986     ; 0        ; 0        ; 0        ;
+------------+-----------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


+--------------------------------------------------------------------+
; Hold Transfers                                                     ;
+------------+-----------+----------+----------+----------+----------+
; From Clock ; To Clock  ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+------------+-----------+----------+----------+----------+----------+
; clk_50MHz  ; clk_50MHz ; 3986     ; 0        ; 0        ; 0        ;
+------------+-----------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


---------------
; Report TCCS ;
---------------
No dedicated SERDES Transmitter circuitry present in device or used in design


---------------
; Report RSKM ;
---------------
No non-DPA dedicated SERDES Receiver circuitry present in device or used in design


+------------------------------------------------+
; Unconstrained Paths Summary                    ;
+---------------------------------+-------+------+
; Property                        ; Setup ; Hold ;
+---------------------------------+-------+------+
; Illegal Clocks                  ; 0     ; 0    ;
; Unconstrained Clocks            ; 0     ; 0    ;
; Unconstrained Input Ports       ; 8     ; 8    ;
; Unconstrained Input Port Paths  ; 192   ; 192  ;
; Unconstrained Output Ports      ; 19    ; 19   ;
; Unconstrained Output Port Paths ; 20    ; 20   ;
+---------------------------------+-------+------+


+--------------------------------------------+
; Clock Status Summary                       ;
+-----------+-----------+------+-------------+
; Target    ; Clock     ; Type ; Status      ;
+-----------+-----------+------+-------------+
; clk_50MHz ; clk_50MHz ; Base ; Constrained ;
+-----------+-----------+------+-------------+


+-----------------------------------------------------------------------------------------------------------+
; Unconstrained Input Ports                                                                                 ;
+--------------------+--------------------------------------------------------------------------------------+
; Input Port         ; Comment                                                                              ;
+--------------------+--------------------------------------------------------------------------------------+
; dht11_data         ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[0]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[1]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[2]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[3]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[0] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[1] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; rst_n              ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------------+--------------------------------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------+
; Unconstrained Output Ports                                                                           ;
+--------------+---------------------------------------------------------------------------------------+
; Output Port  ; Comment                                                                               ;
+--------------+---------------------------------------------------------------------------------------+
; dht11_data   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[0]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[1]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[2]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[3]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[4]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[5]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[6]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[7]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_en       ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_rst      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------+---------------------------------------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------+
; Unconstrained Input Ports                                                                                 ;
+--------------------+--------------------------------------------------------------------------------------+
; Input Port         ; Comment                                                                              ;
+--------------------+--------------------------------------------------------------------------------------+
; dht11_data         ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[0]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[1]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[2]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[3]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[0] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[1] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; rst_n              ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------------+--------------------------------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------+
; Unconstrained Output Ports                                                                           ;
+--------------+---------------------------------------------------------------------------------------+
; Output Port  ; Comment                                                                               ;
+--------------+---------------------------------------------------------------------------------------+
; dht11_data   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[0]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[1]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[2]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[3]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[4]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[5]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[6]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[7]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_en       ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_rst      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------+---------------------------------------------------------------------------------------+


+--------------------------+
; Timing Analyzer Messages ;
+--------------------------+
Info: *******************************************************************
Info: Running Quartus Prime Timing Analyzer
    Info: Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
    Info: Processing started: Thu Jul 17 19:51:21 2025
Info: Command: quartus_sta 11 -c 11
Info: qsta_default_script.tcl version: #1
Warning (18236): Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance.
Info (20030): Parallel compilation is enabled and will use 14 of the 14 processors detected
Info (21077): Low junction temperature is 0 degrees C
Info (21077): High junction temperature is 85 degrees C
Critical Warning (332012): Synopsys Design Constraints File file not found: '11.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design.
Info (332142): No user constrained base clocks found in the design. Calling "derive_clocks -period 1.0"
Info (332105): Deriving Clocks
    Info (332105): create_clock -period 1.000 -name clk_50MHz clk_50MHz
Info (332143): No user constrained clock uncertainty found in the design. Calling "derive_clock_uncertainty"
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Info: Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON
Info: Analyzing Slow 1200mV 85C Model
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -4.133
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -4.133            -330.271 clk_50MHz 
Info (332146): Worst-case hold slack is 0.433
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.433               0.000 clk_50MHz 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000            -194.823 clk_50MHz 
Info: Analyzing Slow 1200mV 0C Model
Info (334003): Started post-fitting delay annotation
Info (334004): Delay annotation completed successfully
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -3.896
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.896            -303.221 clk_50MHz 
Info (332146): Worst-case hold slack is 0.383
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.383               0.000 clk_50MHz 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000            -194.823 clk_50MHz 
Info: Analyzing Fast 1200mV 0C Model
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -1.176
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -1.176             -74.773 clk_50MHz 
Info (332146): Worst-case hold slack is 0.178
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.178               0.000 clk_50MHz 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000            -140.681 clk_50MHz 
Info (332102): Design is not fully constrained for setup requirements
Info (332102): Design is not fully constrained for hold requirements
Info: Quartus Prime Timing Analyzer was successful. 0 errors, 5 warnings
    Info: Peak virtual memory: 4875 megabytes
    Info: Processing ended: Thu Jul 17 19:51:23 2025
    Info: Elapsed time: 00:00:02
    Info: Total CPU time (on all processors): 00:00:02


