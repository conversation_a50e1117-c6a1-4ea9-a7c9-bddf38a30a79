Timing Analyzer report for 11
Thu Jul 17 20:12:44 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Timing Analyzer Summary
  3. Parallel Compilation
  4. Clocks
  5. Slow 1200mV 85C Model Fmax Summary
  6. Timing Closure Recommendations
  7. Slow 1200mV 85C Model Setup Summary
  8. Slow 1200mV 85C Model Hold Summary
  9. Slow 1200mV 85C Model Recovery Summary
 10. Slow 1200mV 85C Model Removal Summary
 11. Slow 1200mV 85C Model Minimum Pulse Width Summary
 12. Slow 1200mV 85C Model Setup: 'clk_50MHz'
 13. Slow 1200mV 85C Model Setup: 'zhuhoujia2023111413_004:pwm_gen|pwm_clk'
 14. Slow 1200mV 85C Model Hold: 'clk_50MHz'
 15. Slow 1200mV 85C Model Hold: 'zhuhoujia2023111413_004:pwm_gen|pwm_clk'
 16. Slow 1200mV 85C Model Metastability Summary
 17. Slow 1200mV 0C Model Fmax Summary
 18. Slow 1200mV 0C Model Setup Summary
 19. Slow 1200mV 0C Model Hold Summary
 20. Slow 1200mV 0C Model Recovery Summary
 21. Slow 1200mV 0C Model Removal Summary
 22. Slow 1200mV 0C Model Minimum Pulse Width Summary
 23. Slow 1200mV 0C Model Setup: 'clk_50MHz'
 24. Slow 1200mV 0C Model Setup: 'zhuhoujia2023111413_004:pwm_gen|pwm_clk'
 25. Slow 1200mV 0C Model Hold: 'clk_50MHz'
 26. Slow 1200mV 0C Model Hold: 'zhuhoujia2023111413_004:pwm_gen|pwm_clk'
 27. Slow 1200mV 0C Model Metastability Summary
 28. Fast 1200mV 0C Model Setup Summary
 29. Fast 1200mV 0C Model Hold Summary
 30. Fast 1200mV 0C Model Recovery Summary
 31. Fast 1200mV 0C Model Removal Summary
 32. Fast 1200mV 0C Model Minimum Pulse Width Summary
 33. Fast 1200mV 0C Model Setup: 'clk_50MHz'
 34. Fast 1200mV 0C Model Setup: 'zhuhoujia2023111413_004:pwm_gen|pwm_clk'
 35. Fast 1200mV 0C Model Hold: 'clk_50MHz'
 36. Fast 1200mV 0C Model Hold: 'zhuhoujia2023111413_004:pwm_gen|pwm_clk'
 37. Fast 1200mV 0C Model Metastability Summary
 38. Multicorner Timing Analysis Summary
 39. Board Trace Model Assignments
 40. Input Transition Times
 41. Signal Integrity Metrics (Slow 1200mv 0c Model)
 42. Signal Integrity Metrics (Slow 1200mv 85c Model)
 43. Signal Integrity Metrics (Fast 1200mv 0c Model)
 44. Setup Transfers
 45. Hold Transfers
 46. Report TCCS
 47. Report RSKM
 48. Unconstrained Paths Summary
 49. Clock Status Summary
 50. Unconstrained Input Ports
 51. Unconstrained Output Ports
 52. Unconstrained Input Ports
 53. Unconstrained Output Ports
 54. Timing Analyzer Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+-----------------------------------------------------------------------------+
; Timing Analyzer Summary                                                     ;
+-----------------------+-----------------------------------------------------+
; Quartus Prime Version ; Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition ;
; Timing Analyzer       ; Legacy Timing Analyzer                              ;
; Revision Name         ; 11                                                  ;
; Device Family         ; Cyclone IV E                                        ;
; Device Name           ; EP4CE6E22C8                                         ;
; Timing Models         ; Final                                               ;
; Delay Model           ; Combined                                            ;
; Rise/Fall Delays      ; Enabled                                             ;
+-----------------------+-----------------------------------------------------+


+------------------------------------------+
; Parallel Compilation                     ;
+----------------------------+-------------+
; Processors                 ; Number      ;
+----------------------------+-------------+
; Number detected on machine ; 20          ;
; Maximum allowed            ; 14          ;
;                            ;             ;
; Average used               ; 1.14        ;
; Maximum used               ; 14          ;
;                            ;             ;
; Usage by Processor         ; % Time Used ;
;     Processor 1            ; 100.0%      ;
;     Processor 2            ;   1.7%      ;
;     Processors 3-14        ;   1.1%      ;
+----------------------------+-------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clocks                                                                                                                                                                                                                                           ;
+-----------------------------------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------------------------------------------+
; Clock Name                              ; Type ; Period ; Frequency  ; Rise  ; Fall  ; Duty Cycle ; Divide by ; Multiply by ; Phase ; Offset ; Edge List ; Edge Shift ; Inverted ; Master ; Source ; Targets                                     ;
+-----------------------------------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------------------------------------------+
; clk_50MHz                               ; Base ; 1.000  ; 1000.0 MHz ; 0.000 ; 0.500 ;            ;           ;             ;       ;        ;           ;            ;          ;        ;        ; { clk_50MHz }                               ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; Base ; 1.000  ; 1000.0 MHz ; 0.000 ; 0.500 ;            ;           ;             ;       ;        ;           ;            ;          ;        ;        ; { zhuhoujia2023111413_004:pwm_gen|pwm_clk } ;
+-----------------------------------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------------------------------------------+


+-------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Fmax Summary                                            ;
+------------+-----------------+-----------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                              ; Note ;
+------------+-----------------+-----------------------------------------+------+
; 159.8 MHz  ; 159.8 MHz       ; clk_50MHz                               ;      ;
; 191.39 MHz ; 191.39 MHz      ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ;      ;
+------------+-----------------+-----------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


----------------------------------
; Timing Closure Recommendations ;
----------------------------------
HTML report is unavailable in plain text report export.


+------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup Summary                              ;
+-----------------------------------------+--------+---------------+
; Clock                                   ; Slack  ; End Point TNS ;
+-----------------------------------------+--------+---------------+
; clk_50MHz                               ; -5.258 ; -576.966      ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; -4.262 ; -41.998       ;
+-----------------------------------------+--------+---------------+


+-----------------------------------------------------------------+
; Slow 1200mV 85C Model Hold Summary                              ;
+-----------------------------------------+-------+---------------+
; Clock                                   ; Slack ; End Point TNS ;
+-----------------------------------------+-------+---------------+
; clk_50MHz                               ; 0.435 ; 0.000         ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.465 ; 0.000         ;
+-----------------------------------------+-------+---------------+


------------------------------------------
; Slow 1200mV 85C Model Recovery Summary ;
------------------------------------------
No paths to report.


-----------------------------------------
; Slow 1200mV 85C Model Removal Summary ;
-----------------------------------------
No paths to report.


+------------------------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width Summary                ;
+-----------------------------------------+--------+---------------+
; Clock                                   ; Slack  ; End Point TNS ;
+-----------------------------------------+--------+---------------+
; clk_50MHz                               ; -3.000 ; -304.861      ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; -1.487 ; -23.792       ;
+-----------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'clk_50MHz'                                                                                                                                                  ;
+--------+-------------------------------------------------------+----------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node                                             ; To Node                                            ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+-------------------------------------------------------+----------------------------------------------------+--------------+-------------+--------------+------------+------------+
; -5.258 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 6.179      ;
; -5.258 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 6.179      ;
; -5.258 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 6.179      ;
; -5.258 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 6.179      ;
; -5.258 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 6.179      ;
; -5.258 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 6.179      ;
; -5.258 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 6.179      ;
; -5.258 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 6.179      ;
; -5.258 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 6.179      ;
; -5.258 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 6.179      ;
; -5.203 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.627      ;
; -5.203 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.627      ;
; -5.203 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.627      ;
; -5.203 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.627      ;
; -5.203 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.627      ;
; -5.203 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.627      ;
; -5.203 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.627      ;
; -5.203 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.627      ;
; -5.203 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.627      ;
; -5.203 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.627      ;
; -4.995 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.916      ;
; -4.995 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.916      ;
; -4.995 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.916      ;
; -4.995 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.916      ;
; -4.995 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.916      ;
; -4.995 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.916      ;
; -4.995 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.916      ;
; -4.995 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.916      ;
; -4.995 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.916      ;
; -4.995 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.916      ;
; -4.979 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.900      ;
; -4.979 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.900      ;
; -4.979 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.900      ;
; -4.979 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.900      ;
; -4.979 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.900      ;
; -4.979 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.900      ;
; -4.979 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.900      ;
; -4.979 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.900      ;
; -4.979 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.900      ;
; -4.979 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.900      ;
; -4.962 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.063     ; 5.900      ;
; -4.954 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.875      ;
; -4.954 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.875      ;
; -4.954 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.875      ;
; -4.954 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.875      ;
; -4.954 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.875      ;
; -4.954 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.875      ;
; -4.954 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.875      ;
; -4.954 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.875      ;
; -4.954 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.875      ;
; -4.954 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.875      ;
; -4.933 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.854      ;
; -4.933 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.854      ;
; -4.933 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.854      ;
; -4.933 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.854      ;
; -4.933 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.854      ;
; -4.933 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.854      ;
; -4.933 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.854      ;
; -4.933 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.854      ;
; -4.933 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.854      ;
; -4.933 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.854      ;
; -4.907 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.560     ; 5.348      ;
; -4.863 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.784      ;
; -4.863 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.784      ;
; -4.863 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.784      ;
; -4.863 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.784      ;
; -4.863 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.784      ;
; -4.863 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.784      ;
; -4.863 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.784      ;
; -4.863 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.784      ;
; -4.863 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.784      ;
; -4.863 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.784      ;
; -4.790 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.107     ; 5.684      ;
; -4.790 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.107     ; 5.684      ;
; -4.790 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.107     ; 5.684      ;
; -4.790 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.107     ; 5.684      ;
; -4.790 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.107     ; 5.684      ;
; -4.790 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.107     ; 5.684      ;
; -4.790 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.107     ; 5.684      ;
; -4.790 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.107     ; 5.684      ;
; -4.790 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.107     ; 5.684      ;
; -4.790 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.107     ; 5.684      ;
; -4.781 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.397      ; 6.179      ;
; -4.781 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.397      ; 6.179      ;
; -4.781 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.397      ; 6.179      ;
; -4.781 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[14] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.397      ; 6.179      ;
; -4.762 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]    ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.186      ;
; -4.762 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]    ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.186      ;
; -4.762 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]    ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.186      ;
; -4.762 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]    ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.186      ;
; -4.762 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]    ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.186      ;
; -4.762 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]    ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.186      ;
; -4.762 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]    ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.186      ;
; -4.762 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]    ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.186      ;
; -4.762 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]    ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.186      ;
; -4.762 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]    ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.577     ; 5.186      ;
; -4.733 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.654      ;
; -4.733 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.654      ;
; -4.733 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.654      ;
; -4.733 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.654      ;
+--------+-------------------------------------------------------+----------------------------------------------------+--------------+-------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'zhuhoujia2023111413_004:pwm_gen|pwm_clk'                                                                                                                                                                   ;
+--------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                         ; To Node                                        ; Launch Clock                            ; Latch Clock                             ; Relationship ; Clock Skew ; Data Delay ;
+--------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+
; -4.262 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.951      ;
; -4.260 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.949      ;
; -4.225 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 5.145      ;
; -4.223 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 5.143      ;
; -4.216 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 5.136      ;
; -4.214 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 5.134      ;
; -4.103 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.792      ;
; -4.101 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.790      ;
; -4.068 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.757      ;
; -4.066 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.755      ;
; -3.944 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.633      ;
; -3.944 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.633      ;
; -3.943 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.632      ;
; -3.942 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.631      ;
; -3.942 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.631      ;
; -3.941 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.630      ;
; -3.929 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.849      ;
; -3.927 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.847      ;
; -3.907 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.827      ;
; -3.907 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.827      ;
; -3.906 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.826      ;
; -3.905 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.825      ;
; -3.905 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.825      ;
; -3.904 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.824      ;
; -3.898 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.818      ;
; -3.898 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.818      ;
; -3.897 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.817      ;
; -3.896 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.816      ;
; -3.896 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.816      ;
; -3.895 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.815      ;
; -3.790 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.708      ;
; -3.788 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.706      ;
; -3.785 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.474      ;
; -3.785 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.474      ;
; -3.784 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.473      ;
; -3.783 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.472      ;
; -3.783 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.472      ;
; -3.782 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.471      ;
; -3.750 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.439      ;
; -3.750 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.439      ;
; -3.749 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.438      ;
; -3.748 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.437      ;
; -3.748 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.437      ;
; -3.747 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.436      ;
; -3.611 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.531      ;
; -3.611 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.531      ;
; -3.610 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.530      ;
; -3.609 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.529      ;
; -3.609 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.529      ;
; -3.608 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.081     ; 4.528      ;
; -3.608 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.297      ;
; -3.606 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 4.295      ;
; -3.584 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.502      ;
; -3.582 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.500      ;
; -3.472 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.390      ;
; -3.472 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.390      ;
; -3.471 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.389      ;
; -3.470 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.388      ;
; -3.470 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.388      ;
; -3.469 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.387      ;
; -3.290 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.979      ;
; -3.290 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.979      ;
; -3.289 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.978      ;
; -3.288 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.977      ;
; -3.288 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.977      ;
; -3.287 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.976      ;
; -3.266 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.184      ;
; -3.266 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.184      ;
; -3.265 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.183      ;
; -3.264 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.182      ;
; -3.264 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.182      ;
; -3.263 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 4.181      ;
; -3.099 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.798     ; 3.292      ;
; -3.097 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.798     ; 3.290      ;
; -2.923 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.612      ;
; -2.921 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.610      ;
; -2.781 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.798     ; 2.974      ;
; -2.781 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.798     ; 2.974      ;
; -2.780 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.798     ; 2.973      ;
; -2.779 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.798     ; 2.972      ;
; -2.779 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.798     ; 2.972      ;
; -2.778 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.798     ; 2.971      ;
; -2.745 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 3.663      ;
; -2.743 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 3.661      ;
; -2.613 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 3.531      ;
; -2.611 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 3.529      ;
; -2.545 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.234      ;
; -2.543 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.232      ;
; -2.427 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 3.345      ;
; -2.427 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 3.345      ;
; -2.426 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 3.344      ;
; -2.425 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 3.343      ;
; -2.425 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 3.343      ;
; -2.424 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.083     ; 3.342      ;
; -2.317 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.006      ;
; -2.316 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.005      ;
; -2.316 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.005      ;
; -2.315 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.004      ;
; -2.315 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.004      ;
; -2.314 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.302     ; 3.003      ;
+--------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'clk_50MHz'                                                                                                                                                                               ;
+-------+--------------------------------------------------------------------+--------------------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node                                                          ; To Node                                                            ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+--------------------------------------------------------------------+--------------------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[15]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[15]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[31]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[31]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[14]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[14]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[30]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[30]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[29]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[29]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[13]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[13]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[28]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[28]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[12]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[12]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[11]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[11]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[27]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[27]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[10]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[10]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[26]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[26]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]              ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[25]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[25]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[24]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[24]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.435 ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]              ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.099      ; 0.746      ;
; 0.452 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_LINE_FOLLOW ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_LINE_FOLLOW ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.746      ;
; 0.452 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_OBSTACLE    ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_OBSTACLE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_006:lcd_display|lcd_en                         ; zhuhoujia2023111413_006:lcd_display|lcd_en                         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_006:lcd_display|lcd_rst                        ; zhuhoujia2023111413_006:lcd_display|lcd_rst                        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_006:lcd_display|state.RESET                    ; zhuhoujia2023111413_006:lcd_display|state.RESET                    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_006:lcd_display|state.INIT                     ; zhuhoujia2023111413_006:lcd_display|state.INIT                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP             ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM              ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.START                   ; zhuhoujia2023111413_005:dht11_sensor|state.START                   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE                    ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE                    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE                ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE                 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.465 ; zhuhoujia2023111413_006:lcd_display|state.IDLE                     ; zhuhoujia2023111413_006:lcd_display|state.IDLE                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.758      ;
; 0.465 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH                  ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.758      ;
; 0.499 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0]           ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.793      ;
; 0.507 ; zhuhoujia2023111413_001:line_follow|sensor_reg1[1]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[1]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.801      ;
; 0.507 ; zhuhoujia2023111413_001:line_follow|sensor_reg1[3]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[3]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.801      ;
; 0.507 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]                ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.800      ;
; 0.508 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[1]                 ; zhuhoujia2023111413_001:line_follow|sensor_filtered[1]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.802      ;
; 0.509 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[0]                 ; zhuhoujia2023111413_001:line_follow|sensor_filtered[0]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.803      ;
; 0.510 ; zhuhoujia2023111413_002:obstacle_avoid|obs_reg3[1]                 ; zhuhoujia2023111413_002:obstacle_avoid|obs_filtered[1]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.804      ;
; 0.516 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[3]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[3]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.810      ;
; 0.517 ; zhuhoujia2023111413_005:dht11_sensor|timer[19]                     ; zhuhoujia2023111413_005:dht11_sensor|timer[19]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.810      ;
; 0.518 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[1]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[1]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.812      ;
; 0.649 ; zhuhoujia2023111413_001:line_follow|sensor_reg1[0]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[0]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.943      ;
; 0.654 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.577      ; 1.443      ;
; 0.655 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[0]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[0]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.949      ;
; 0.659 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[2]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[2]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.953      ;
; 0.662 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12]                 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[14]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.577      ; 1.451      ;
; 0.663 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.577      ; 1.452      ;
; 0.668 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP             ; zhuhoujia2023111413_006:lcd_display|lcd_data[5]                    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.961      ;
; 0.682 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP             ; zhuhoujia2023111413_006:lcd_display|lcd_data[7]                    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.975      ;
; 0.692 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_OBSTACLE    ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.986      ;
; 0.736 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[1]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[1]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.030      ;
; 0.737 ; zhuhoujia2023111413_001:line_follow|debounce_counter[1]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[1]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.030      ;
; 0.739 ; zhuhoujia2023111413_001:line_follow|debounce_counter[2]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[2]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.032      ;
; 0.741 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[1]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[1]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 1.054      ;
; 0.743 ; zhuhoujia2023111413_001:line_follow|sensor_filtered[0]             ; zhuhoujia2023111413_001:line_follow|control_signal[1]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.036      ;
; 0.744 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[7]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[7]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.037      ;
; 0.744 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[5]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.036      ;
; 0.746 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[5]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[5]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.039      ;
; 0.746 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[3]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[3]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.039      ;
; 0.746 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[2]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[2]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.039      ;
; 0.746 ; zhuhoujia2023111413_001:line_follow|sensor_filtered[0]             ; zhuhoujia2023111413_001:line_follow|control_signal[0]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.039      ;
; 0.746 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[3]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.038      ;
; 0.746 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10]                 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.058      ;
; 0.747 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[8]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[8]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.040      ;
; 0.747 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[6]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[6]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.040      ;
; 0.747 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[1]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.039      ;
; 0.748 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[4]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[4]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.041      ;
; 0.748 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[4]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.040      ;
; 0.749 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[2]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.041      ;
; 0.752 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.577      ; 1.541      ;
; 0.754 ; zhuhoujia2023111413_005:dht11_sensor|timer[10]                     ; zhuhoujia2023111413_005:dht11_sensor|timer[10]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.047      ;
; 0.758 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[9]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.050      ;
; 0.759 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[7]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.051      ;
; 0.760 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[0]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[0]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.054      ;
; 0.760 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[3]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[3]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.054      ;
; 0.761 ; zhuhoujia2023111413_001:line_follow|debounce_counter[0]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[0]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; zhuhoujia2023111413_001:line_follow|debounce_counter[3]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[3]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[11]    ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[11]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[5]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[5]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[13]    ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[13]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.054      ;
; 0.761 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.577      ; 1.550      ;
; 0.762 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.054      ;
; 0.762 ; zhuhoujia2023111413_001:line_follow|debounce_counter[11]           ; zhuhoujia2023111413_001:line_follow|debounce_counter[11]           ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.055      ;
; 0.762 ; zhuhoujia2023111413_001:line_follow|debounce_counter[5]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[5]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.055      ;
; 0.762 ; zhuhoujia2023111413_001:line_follow|debounce_counter[13]           ; zhuhoujia2023111413_001:line_follow|debounce_counter[13]           ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.055      ;
; 0.762 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[15]    ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[15]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.056      ;
; 0.762 ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.055      ;
; 0.762 ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.055      ;
; 0.762 ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]              ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.055      ;
; 0.762 ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]              ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.055      ;
; 0.762 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[8]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.054      ;
; 0.763 ; zhuhoujia2023111413_001:line_follow|debounce_counter[15]           ; zhuhoujia2023111413_001:line_follow|debounce_counter[15]           ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.056      ;
; 0.763 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[2]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[2]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.763 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[6]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[6]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.763 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[7]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[7]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.763 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[9]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[9]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.763 ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]              ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.056      ;
; 0.764 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.056      ;
; 0.764 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.056      ;
; 0.764 ; zhuhoujia2023111413_001:line_follow|debounce_counter[6]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[6]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.057      ;
+-------+--------------------------------------------------------------------+--------------------------------------------------------------------+--------------+-------------+--------------+------------+------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'zhuhoujia2023111413_004:pwm_gen|pwm_clk'                                                                                                                                                                   ;
+-------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+
; Slack ; From Node                                         ; To Node                                        ; Launch Clock                            ; Latch Clock                             ; Relationship ; Clock Skew ; Data Delay ;
+-------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+
; 0.465 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 0.758      ;
; 0.764 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.057      ;
; 0.764 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.057      ;
; 0.765 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.058      ;
; 0.765 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.058      ;
; 0.766 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.059      ;
; 1.118 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.411      ;
; 1.118 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.411      ;
; 1.126 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.419      ;
; 1.135 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.428      ;
; 1.135 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.428      ;
; 1.258 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.551      ;
; 1.266 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.559      ;
; 1.275 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.568      ;
; 1.333 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.628      ;
; 1.349 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.644      ;
; 1.375 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.670      ;
; 1.388 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.683      ;
; 1.389 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.682      ;
; 1.398 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.691      ;
; 1.398 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.693      ;
; 1.443 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.738      ;
; 1.515 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.810      ;
; 1.528 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.823      ;
; 1.536 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.829      ;
; 1.538 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.833      ;
; 1.546 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.839      ;
; 1.576 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 1.869      ;
; 1.613 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.908      ;
; 1.629 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.924      ;
; 1.655 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 1.335      ;
; 1.657 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 1.337      ;
; 1.668 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.963      ;
; 1.678 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.083      ; 1.973      ;
; 1.688 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 1.979      ;
; 1.792 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 1.472      ;
; 1.793 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 1.473      ;
; 1.811 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.102      ;
; 1.813 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 1.493      ;
; 1.814 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 1.494      ;
; 1.949 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.106      ;
; 1.950 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.107      ;
; 1.950 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.107      ;
; 1.951 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.108      ;
; 1.952 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.109      ;
; 1.952 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.109      ;
; 2.060 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 2.353      ;
; 2.076 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.081      ; 2.369      ;
; 2.245 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.536      ;
; 2.246 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.537      ;
; 2.247 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.538      ;
; 2.249 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.540      ;
; 2.249 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.540      ;
; 2.250 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.541      ;
; 2.259 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.416      ;
; 2.261 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.418      ;
; 2.261 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.418      ;
; 2.262 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.419      ;
; 2.262 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.419      ;
; 2.263 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.420      ;
; 2.272 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.429      ;
; 2.273 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.430      ;
; 2.311 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.468      ;
; 2.312 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.469      ;
; 2.313 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.470      ;
; 2.315 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.472      ;
; 2.315 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.472      ;
; 2.316 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.473      ;
; 2.419 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 2.099      ;
; 2.421 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 2.101      ;
; 2.498 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.655      ;
; 2.499 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.656      ;
; 2.507 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.664      ;
; 2.509 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.666      ;
; 2.527 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.684      ;
; 2.528 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.685      ;
; 2.603 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.894      ;
; 2.604 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.895      ;
; 2.628 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.919      ;
; 2.629 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.920      ;
; 2.630 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.921      ;
; 2.632 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.923      ;
; 2.632 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.923      ;
; 2.633 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.924      ;
; 2.700 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.857      ;
; 2.702 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.859      ;
; 2.707 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.998      ;
; 2.708 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 2.999      ;
; 2.709 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 3.000      ;
; 2.711 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 3.002      ;
; 2.711 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 3.002      ;
; 2.712 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.079      ; 3.003      ;
; 2.742 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.899      ;
; 2.744 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.085     ; 2.901      ;
; 2.766 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 2.446      ;
; 2.768 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 2.448      ;
; 2.768 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 2.448      ;
; 2.769 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 2.449      ;
; 2.785 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 2.465      ;
; 2.786 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.562     ; 2.466      ;
+-------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+


-----------------------------------------------
; Slow 1200mV 85C Model Metastability Summary ;
-----------------------------------------------
No synchronizer chains to report.


+-------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Fmax Summary                                             ;
+------------+-----------------+-----------------------------------------+------+
; Fmax       ; Restricted Fmax ; Clock Name                              ; Note ;
+------------+-----------------+-----------------------------------------+------+
; 168.04 MHz ; 168.04 MHz      ; clk_50MHz                               ;      ;
; 204.42 MHz ; 204.42 MHz      ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ;      ;
+------------+-----------------+-----------------------------------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup Summary                               ;
+-----------------------------------------+--------+---------------+
; Clock                                   ; Slack  ; End Point TNS ;
+-----------------------------------------+--------+---------------+
; clk_50MHz                               ; -4.951 ; -529.351      ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; -3.961 ; -38.475       ;
+-----------------------------------------+--------+---------------+


+-----------------------------------------------------------------+
; Slow 1200mV 0C Model Hold Summary                               ;
+-----------------------------------------+-------+---------------+
; Clock                                   ; Slack ; End Point TNS ;
+-----------------------------------------+-------+---------------+
; clk_50MHz                               ; 0.384 ; 0.000         ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.417 ; 0.000         ;
+-----------------------------------------+-------+---------------+


-----------------------------------------
; Slow 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Slow 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+------------------------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width Summary                 ;
+-----------------------------------------+--------+---------------+
; Clock                                   ; Slack  ; End Point TNS ;
+-----------------------------------------+--------+---------------+
; clk_50MHz                               ; -3.000 ; -304.861      ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; -1.487 ; -23.792       ;
+-----------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'clk_50MHz'                                                                                                                                                   ;
+--------+-------------------------------------------------------+----------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node                                             ; To Node                                            ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+-------------------------------------------------------+----------------------------------------------------+--------------+-------------+--------------+------------+------------+
; -4.951 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.881      ;
; -4.951 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.881      ;
; -4.951 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.881      ;
; -4.951 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.881      ;
; -4.951 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.881      ;
; -4.951 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.881      ;
; -4.951 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.881      ;
; -4.951 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.881      ;
; -4.951 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.881      ;
; -4.951 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.881      ;
; -4.892 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.540     ; 5.354      ;
; -4.892 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.540     ; 5.354      ;
; -4.892 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.540     ; 5.354      ;
; -4.892 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.540     ; 5.354      ;
; -4.892 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.540     ; 5.354      ;
; -4.892 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.540     ; 5.354      ;
; -4.892 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.540     ; 5.354      ;
; -4.892 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.540     ; 5.354      ;
; -4.892 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.540     ; 5.354      ;
; -4.892 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.540     ; 5.354      ;
; -4.682 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.612      ;
; -4.682 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.612      ;
; -4.682 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.612      ;
; -4.682 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.612      ;
; -4.682 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.612      ;
; -4.682 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.612      ;
; -4.682 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.612      ;
; -4.682 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.612      ;
; -4.682 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.612      ;
; -4.682 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.612      ;
; -4.662 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.056     ; 5.608      ;
; -4.654 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.584      ;
; -4.654 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.584      ;
; -4.654 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.584      ;
; -4.654 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.584      ;
; -4.654 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.584      ;
; -4.654 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.584      ;
; -4.654 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.584      ;
; -4.654 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.584      ;
; -4.654 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.584      ;
; -4.654 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.584      ;
; -4.652 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.582      ;
; -4.652 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.582      ;
; -4.652 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.582      ;
; -4.652 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.582      ;
; -4.652 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.582      ;
; -4.652 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.582      ;
; -4.652 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.582      ;
; -4.652 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.582      ;
; -4.652 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.582      ;
; -4.652 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.582      ;
; -4.631 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.561      ;
; -4.631 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.561      ;
; -4.631 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.561      ;
; -4.631 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.561      ;
; -4.631 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.561      ;
; -4.631 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.561      ;
; -4.631 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.561      ;
; -4.631 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.561      ;
; -4.631 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.561      ;
; -4.631 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.561      ;
; -4.603 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.524     ; 5.081      ;
; -4.567 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.497      ;
; -4.567 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.497      ;
; -4.567 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.497      ;
; -4.567 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.497      ;
; -4.567 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.497      ;
; -4.567 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.497      ;
; -4.567 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.497      ;
; -4.567 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.497      ;
; -4.567 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.497      ;
; -4.567 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.497      ;
; -4.502 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.377      ; 5.881      ;
; -4.502 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.377      ; 5.881      ;
; -4.502 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.377      ; 5.881      ;
; -4.502 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[14] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.377      ; 5.881      ;
; -4.460 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.096     ; 5.366      ;
; -4.460 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.096     ; 5.366      ;
; -4.460 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.096     ; 5.366      ;
; -4.460 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.096     ; 5.366      ;
; -4.460 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.096     ; 5.366      ;
; -4.460 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.096     ; 5.366      ;
; -4.460 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.096     ; 5.366      ;
; -4.460 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.096     ; 5.366      ;
; -4.460 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.096     ; 5.366      ;
; -4.460 ; zhuhoujia2023111413_001:line_follow|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.096     ; 5.366      ;
; -4.446 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.376      ;
; -4.446 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.376      ;
; -4.446 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.376      ;
; -4.446 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.376      ;
; -4.446 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.376      ;
; -4.446 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.376      ;
; -4.446 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.376      ;
; -4.446 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.376      ;
; -4.446 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.376      ;
; -4.446 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.376      ;
; -4.443 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.091     ; 5.354      ;
; -4.443 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.091     ; 5.354      ;
; -4.443 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.091     ; 5.354      ;
; -4.443 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]     ; zhuhoujia2023111413_003:motor_ctrl|state_timer[14] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.091     ; 5.354      ;
+--------+-------------------------------------------------------+----------------------------------------------------+--------------+-------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'zhuhoujia2023111413_004:pwm_gen|pwm_clk'                                                                                                                                                                    ;
+--------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                         ; To Node                                        ; Launch Clock                            ; Latch Clock                             ; Relationship ; Clock Skew ; Data Delay ;
+--------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+
; -3.961 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.655      ;
; -3.959 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.653      ;
; -3.892 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.822      ;
; -3.890 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.820      ;
; -3.884 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.814      ;
; -3.882 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.812      ;
; -3.785 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.479      ;
; -3.783 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.477      ;
; -3.758 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.452      ;
; -3.756 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.450      ;
; -3.672 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.366      ;
; -3.672 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.366      ;
; -3.671 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.365      ;
; -3.670 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.364      ;
; -3.669 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.363      ;
; -3.668 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.362      ;
; -3.631 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.561      ;
; -3.629 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.559      ;
; -3.603 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.533      ;
; -3.603 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.533      ;
; -3.602 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.532      ;
; -3.601 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.531      ;
; -3.600 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.530      ;
; -3.599 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.529      ;
; -3.595 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.525      ;
; -3.595 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.525      ;
; -3.594 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.524      ;
; -3.593 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.523      ;
; -3.592 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.522      ;
; -3.591 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.521      ;
; -3.520 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.450      ;
; -3.518 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.448      ;
; -3.496 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.190      ;
; -3.496 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.190      ;
; -3.495 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.189      ;
; -3.494 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.188      ;
; -3.493 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.187      ;
; -3.492 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.186      ;
; -3.469 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.163      ;
; -3.469 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.163      ;
; -3.468 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.162      ;
; -3.467 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.161      ;
; -3.466 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.160      ;
; -3.465 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.159      ;
; -3.347 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.041      ;
; -3.345 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 4.039      ;
; -3.342 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.272      ;
; -3.342 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.272      ;
; -3.341 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.271      ;
; -3.340 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.270      ;
; -3.339 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.269      ;
; -3.338 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.268      ;
; -3.276 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.206      ;
; -3.274 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.204      ;
; -3.231 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.161      ;
; -3.231 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.161      ;
; -3.230 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.160      ;
; -3.229 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.159      ;
; -3.228 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.158      ;
; -3.227 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 4.157      ;
; -3.058 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 3.752      ;
; -3.058 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 3.752      ;
; -3.057 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 3.751      ;
; -3.056 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 3.750      ;
; -3.055 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 3.749      ;
; -3.054 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 3.748      ;
; -2.987 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.917      ;
; -2.987 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.917      ;
; -2.986 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.916      ;
; -2.985 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.915      ;
; -2.984 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.914      ;
; -2.983 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.913      ;
; -2.882 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.764     ; 3.110      ;
; -2.880 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.764     ; 3.108      ;
; -2.765 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 3.459      ;
; -2.763 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 3.457      ;
; -2.593 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.764     ; 2.821      ;
; -2.593 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.764     ; 2.821      ;
; -2.592 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.764     ; 2.820      ;
; -2.591 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.764     ; 2.819      ;
; -2.590 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.764     ; 2.818      ;
; -2.589 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.764     ; 2.817      ;
; -2.470 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.400      ;
; -2.468 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.398      ;
; -2.406 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 3.100      ;
; -2.404 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 3.098      ;
; -2.338 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.268      ;
; -2.336 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.266      ;
; -2.181 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.111      ;
; -2.181 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.111      ;
; -2.180 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.110      ;
; -2.179 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.109      ;
; -2.178 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.108      ;
; -2.177 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.072     ; 3.107      ;
; -2.149 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 2.843      ;
; -2.149 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 2.843      ;
; -2.147 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 2.841      ;
; -2.146 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 2.840      ;
; -2.129 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 2.823      ;
; -2.128 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.298     ; 2.822      ;
+--------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'clk_50MHz'                                                                                                                                                                                ;
+-------+--------------------------------------------------------------------+--------------------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node                                                          ; To Node                                                            ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+--------------------------------------------------------------------+--------------------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[15]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[15]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[31]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[31]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[14]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[14]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[30]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[30]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[29]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[29]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[13]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[13]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[28]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[28]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[12]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[12]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[11]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[11]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[27]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[27]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[10]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[10]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[26]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[26]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]              ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[25]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[25]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[24]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[24]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.384 ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]              ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_LINE_FOLLOW ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_LINE_FOLLOW ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_OBSTACLE    ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_OBSTACLE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM              ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|state.START                   ; zhuhoujia2023111413_005:dht11_sensor|state.START                   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE                    ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE                    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE                ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE                 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_006:lcd_display|lcd_en                         ; zhuhoujia2023111413_006:lcd_display|lcd_en                         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_006:lcd_display|lcd_rst                        ; zhuhoujia2023111413_006:lcd_display|lcd_rst                        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_006:lcd_display|state.RESET                    ; zhuhoujia2023111413_006:lcd_display|state.RESET                    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_006:lcd_display|state.INIT                     ; zhuhoujia2023111413_006:lcd_display|state.INIT                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP             ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.416 ; zhuhoujia2023111413_006:lcd_display|state.IDLE                     ; zhuhoujia2023111413_006:lcd_display|state.IDLE                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.684      ;
; 0.416 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH                  ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.684      ;
; 0.463 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0]           ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.731      ;
; 0.471 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]                ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.738      ;
; 0.475 ; zhuhoujia2023111413_001:line_follow|sensor_reg1[1]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[1]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.744      ;
; 0.476 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[1]                 ; zhuhoujia2023111413_001:line_follow|sensor_filtered[1]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.745      ;
; 0.477 ; zhuhoujia2023111413_001:line_follow|sensor_reg1[3]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[3]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.745      ;
; 0.477 ; zhuhoujia2023111413_002:obstacle_avoid|obs_reg3[1]                 ; zhuhoujia2023111413_002:obstacle_avoid|obs_filtered[1]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.746      ;
; 0.477 ; zhuhoujia2023111413_005:dht11_sensor|timer[19]                     ; zhuhoujia2023111413_005:dht11_sensor|timer[19]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.744      ;
; 0.479 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[0]                 ; zhuhoujia2023111413_001:line_follow|sensor_filtered[0]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.748      ;
; 0.485 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[3]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[3]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.753      ;
; 0.489 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[1]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[1]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.758      ;
; 0.584 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.540      ; 1.319      ;
; 0.599 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12]                 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[14]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.540      ; 1.334      ;
; 0.600 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.540      ; 1.335      ;
; 0.606 ; zhuhoujia2023111413_001:line_follow|sensor_reg1[0]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[0]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.875      ;
; 0.612 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[0]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[0]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.881      ;
; 0.617 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[2]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[2]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.885      ;
; 0.620 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP             ; zhuhoujia2023111413_006:lcd_display|lcd_data[5]                    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.887      ;
; 0.637 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP             ; zhuhoujia2023111413_006:lcd_display|lcd_data[7]                    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.904      ;
; 0.646 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_OBSTACLE    ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.914      ;
; 0.659 ; zhuhoujia2023111413_001:line_follow|sensor_filtered[0]             ; zhuhoujia2023111413_001:line_follow|control_signal[1]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.071      ; 0.925      ;
; 0.660 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.540      ; 1.395      ;
; 0.681 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.540      ; 1.416      ;
; 0.685 ; zhuhoujia2023111413_001:line_follow|debounce_counter[1]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[1]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.953      ;
; 0.685 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[1]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[1]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.953      ;
; 0.686 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[1]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[1]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.092      ; 0.973      ;
; 0.687 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.540      ; 1.422      ;
; 0.688 ; zhuhoujia2023111413_001:line_follow|sensor_filtered[0]             ; zhuhoujia2023111413_001:line_follow|control_signal[0]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.071      ; 0.954      ;
; 0.689 ; zhuhoujia2023111413_001:line_follow|debounce_counter[2]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[2]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.957      ;
; 0.691 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[7]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[7]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.959      ;
; 0.691 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[5]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.958      ;
; 0.693 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10]                 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.979      ;
; 0.694 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[5]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[5]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.962      ;
; 0.694 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[2]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[2]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.962      ;
; 0.695 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[8]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[8]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.963      ;
; 0.695 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[3]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[3]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.963      ;
; 0.696 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[1]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.963      ;
; 0.696 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[3]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.963      ;
; 0.696 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.540      ; 1.431      ;
; 0.697 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[6]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[6]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.965      ;
; 0.698 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[4]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[4]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.966      ;
; 0.698 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[2]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.965      ;
; 0.698 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[4]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.965      ;
; 0.702 ; zhuhoujia2023111413_005:dht11_sensor|timer[10]                     ; zhuhoujia2023111413_005:dht11_sensor|timer[10]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.969      ;
; 0.705 ; zhuhoujia2023111413_001:line_follow|debounce_counter[3]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[3]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_001:line_follow|debounce_counter[5]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[5]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_001:line_follow|debounce_counter[13]           ; zhuhoujia2023111413_001:line_follow|debounce_counter[13]           ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[3]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[3]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[5]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[5]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[13]    ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[13]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]              ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[9]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.972      ;
; 0.706 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.540      ; 1.441      ;
; 0.706 ; zhuhoujia2023111413_001:line_follow|debounce_counter[11]           ; zhuhoujia2023111413_001:line_follow|debounce_counter[11]           ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.974      ;
; 0.706 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[11]    ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[11]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.974      ;
; 0.706 ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.974      ;
; 0.706 ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]              ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.974      ;
; 0.707 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.974      ;
; 0.707 ; zhuhoujia2023111413_001:line_follow|debounce_counter[15]           ; zhuhoujia2023111413_001:line_follow|debounce_counter[15]           ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.975      ;
; 0.707 ; zhuhoujia2023111413_001:line_follow|debounce_counter[6]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[6]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.975      ;
; 0.707 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[15]    ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[15]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.975      ;
; 0.707 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[6]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[6]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.975      ;
; 0.707 ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.975      ;
; 0.707 ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]              ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.975      ;
; 0.708 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.975      ;
; 0.708 ; zhuhoujia2023111413_001:line_follow|debounce_counter[7]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[7]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.976      ;
; 0.708 ; zhuhoujia2023111413_001:line_follow|debounce_counter[9]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[9]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.976      ;
; 0.708 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[7]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[7]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.976      ;
; 0.708 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[9]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[9]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.976      ;
+-------+--------------------------------------------------------------------+--------------------------------------------------------------------+--------------+-------------+--------------+------------+------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'zhuhoujia2023111413_004:pwm_gen|pwm_clk'                                                                                                                                                                    ;
+-------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+
; Slack ; From Node                                         ; To Node                                        ; Launch Clock                            ; Latch Clock                             ; Relationship ; Clock Skew ; Data Delay ;
+-------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+
; 0.417 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 0.684      ;
; 0.709 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 0.976      ;
; 0.709 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 0.976      ;
; 0.710 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 0.977      ;
; 0.710 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 0.977      ;
; 0.712 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 0.979      ;
; 1.028 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.295      ;
; 1.033 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.300      ;
; 1.034 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.301      ;
; 1.043 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.310      ;
; 1.046 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.313      ;
; 1.152 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.419      ;
; 1.155 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.422      ;
; 1.168 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.435      ;
; 1.195 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.462      ;
; 1.211 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.478      ;
; 1.250 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.517      ;
; 1.277 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.544      ;
; 1.278 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.545      ;
; 1.293 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.560      ;
; 1.302 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.569      ;
; 1.311 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.578      ;
; 1.366 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.633      ;
; 1.374 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.641      ;
; 1.397 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.664      ;
; 1.400 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.667      ;
; 1.424 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.691      ;
; 1.433 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.700      ;
; 1.439 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.706      ;
; 1.455 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.722      ;
; 1.522 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.789      ;
; 1.545 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 1.220      ;
; 1.546 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.813      ;
; 1.548 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 1.223      ;
; 1.555 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.822      ;
; 1.631 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 1.898      ;
; 1.691 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 1.366      ;
; 1.692 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 1.367      ;
; 1.696 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 1.371      ;
; 1.698 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 1.373      ;
; 1.772 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 1.894      ;
; 1.773 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 1.895      ;
; 1.773 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 1.895      ;
; 1.775 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 1.897      ;
; 1.776 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 1.898      ;
; 1.777 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 1.899      ;
; 1.824 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.091      ;
; 1.840 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.107      ;
; 2.048 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.170      ;
; 2.049 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.171      ;
; 2.057 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.179      ;
; 2.060 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.182      ;
; 2.073 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.195      ;
; 2.075 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.197      ;
; 2.088 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.355      ;
; 2.088 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.210      ;
; 2.089 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.356      ;
; 2.089 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.356      ;
; 2.089 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.211      ;
; 2.089 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.211      ;
; 2.091 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.358      ;
; 2.091 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.213      ;
; 2.092 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.359      ;
; 2.092 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.214      ;
; 2.093 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.360      ;
; 2.093 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.215      ;
; 2.146 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.268      ;
; 2.148 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.270      ;
; 2.217 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 1.892      ;
; 2.219 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 1.894      ;
; 2.257 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.379      ;
; 2.258 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.380      ;
; 2.266 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.388      ;
; 2.269 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.391      ;
; 2.282 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.404      ;
; 2.284 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.406      ;
; 2.390 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.657      ;
; 2.392 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.659      ;
; 2.428 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.695      ;
; 2.429 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.696      ;
; 2.429 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.696      ;
; 2.431 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.698      ;
; 2.432 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.699      ;
; 2.433 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.700      ;
; 2.462 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.584      ;
; 2.464 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.586      ;
; 2.472 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.739      ;
; 2.473 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.740      ;
; 2.473 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.740      ;
; 2.475 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.742      ;
; 2.476 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.743      ;
; 2.477 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.072      ; 2.744      ;
; 2.480 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.602      ;
; 2.482 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.604      ;
; 2.567 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 2.242      ;
; 2.568 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 2.243      ;
; 2.576 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 2.251      ;
; 2.579 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 2.254      ;
; 2.592 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.550     ; 2.267      ;
; 2.594 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.103     ; 2.716      ;
+-------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+


----------------------------------------------
; Slow 1200mV 0C Model Metastability Summary ;
----------------------------------------------
No synchronizer chains to report.


+------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup Summary                               ;
+-----------------------------------------+--------+---------------+
; Clock                                   ; Slack  ; End Point TNS ;
+-----------------------------------------+--------+---------------+
; clk_50MHz                               ; -1.679 ; -142.303      ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; -1.235 ; -9.422        ;
+-----------------------------------------+--------+---------------+


+-----------------------------------------------------------------+
; Fast 1200mV 0C Model Hold Summary                               ;
+-----------------------------------------+-------+---------------+
; Clock                                   ; Slack ; End Point TNS ;
+-----------------------------------------+-------+---------------+
; clk_50MHz                               ; 0.179 ; 0.000         ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.193 ; 0.000         ;
+-----------------------------------------+-------+---------------+


-----------------------------------------
; Fast 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Fast 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+------------------------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width Summary                 ;
+-----------------------------------------+--------+---------------+
; Clock                                   ; Slack  ; End Point TNS ;
+-----------------------------------------+--------+---------------+
; clk_50MHz                               ; -3.000 ; -219.562      ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; -1.000 ; -16.000       ;
+-----------------------------------------+--------+---------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'clk_50MHz'                                                                                                                                                         ;
+--------+-------------------------------------------------------------+----------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node                                                   ; To Node                                            ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+-------------------------------------------------------------+----------------------------------------------------+--------------+-------------+--------------+------------+------------+
; -1.679 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.630      ;
; -1.679 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.630      ;
; -1.679 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.630      ;
; -1.679 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.630      ;
; -1.679 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.630      ;
; -1.679 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.630      ;
; -1.679 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.630      ;
; -1.679 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.630      ;
; -1.679 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.630      ;
; -1.679 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.630      ;
; -1.627 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.378      ;
; -1.627 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.378      ;
; -1.627 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.378      ;
; -1.627 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.378      ;
; -1.627 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.378      ;
; -1.627 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.378      ;
; -1.627 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.378      ;
; -1.627 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.378      ;
; -1.627 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.378      ;
; -1.627 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.378      ;
; -1.588 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.539      ;
; -1.588 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.539      ;
; -1.588 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.539      ;
; -1.588 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.539      ;
; -1.588 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.539      ;
; -1.588 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.539      ;
; -1.588 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.539      ;
; -1.588 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.539      ;
; -1.588 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.539      ;
; -1.588 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.539      ;
; -1.569 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.520      ;
; -1.569 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.520      ;
; -1.569 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.520      ;
; -1.569 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.520      ;
; -1.569 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.520      ;
; -1.569 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.520      ;
; -1.569 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.520      ;
; -1.569 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.520      ;
; -1.569 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.520      ;
; -1.569 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.520      ;
; -1.555 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.506      ;
; -1.555 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.506      ;
; -1.555 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.506      ;
; -1.555 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.506      ;
; -1.555 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.506      ;
; -1.555 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.506      ;
; -1.555 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.506      ;
; -1.555 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.506      ;
; -1.555 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.506      ;
; -1.555 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.506      ;
; -1.540 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.491      ;
; -1.540 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.491      ;
; -1.540 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.491      ;
; -1.540 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.491      ;
; -1.540 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.491      ;
; -1.540 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.491      ;
; -1.540 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.491      ;
; -1.540 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.491      ;
; -1.540 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.491      ;
; -1.540 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.491      ;
; -1.529 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.025     ; 2.491      ;
; -1.503 ; zhuhoujia2023111413_001:line_follow|control_signal[0]       ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.055     ; 2.435      ;
; -1.503 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.454      ;
; -1.503 ; zhuhoujia2023111413_001:line_follow|control_signal[0]       ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.055     ; 2.435      ;
; -1.503 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.454      ;
; -1.503 ; zhuhoujia2023111413_001:line_follow|control_signal[0]       ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.055     ; 2.435      ;
; -1.503 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.454      ;
; -1.503 ; zhuhoujia2023111413_001:line_follow|control_signal[0]       ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.055     ; 2.435      ;
; -1.503 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.454      ;
; -1.503 ; zhuhoujia2023111413_001:line_follow|control_signal[0]       ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.055     ; 2.435      ;
; -1.503 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.454      ;
; -1.503 ; zhuhoujia2023111413_001:line_follow|control_signal[0]       ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.055     ; 2.435      ;
; -1.503 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.454      ;
; -1.503 ; zhuhoujia2023111413_001:line_follow|control_signal[0]       ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.055     ; 2.435      ;
; -1.503 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.454      ;
; -1.503 ; zhuhoujia2023111413_001:line_follow|control_signal[0]       ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.055     ; 2.435      ;
; -1.503 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.454      ;
; -1.503 ; zhuhoujia2023111413_001:line_follow|control_signal[0]       ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.055     ; 2.435      ;
; -1.503 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.454      ;
; -1.503 ; zhuhoujia2023111413_001:line_follow|control_signal[0]       ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.055     ; 2.435      ;
; -1.503 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.454      ;
; -1.501 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_STOP ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.047     ; 2.441      ;
; -1.501 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_STOP ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.047     ; 2.441      ;
; -1.501 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_STOP ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.047     ; 2.441      ;
; -1.501 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_STOP ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.047     ; 2.441      ;
; -1.501 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_STOP ; zhuhoujia2023111413_003:motor_ctrl|state_timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.047     ; 2.441      ;
; -1.501 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_STOP ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.047     ; 2.441      ;
; -1.501 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_STOP ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.047     ; 2.441      ;
; -1.501 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_STOP ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.047     ; 2.441      ;
; -1.501 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_STOP ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.047     ; 2.441      ;
; -1.501 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_STOP ; zhuhoujia2023111413_003:motor_ctrl|state_timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.047     ; 2.441      ;
; -1.487 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.156      ; 2.630      ;
; -1.487 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.156      ; 2.630      ;
; -1.487 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.156      ; 2.630      ;
; -1.487 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[14] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.156      ; 2.630      ;
; -1.477 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]           ; zhuhoujia2023111413_003:motor_ctrl|state_timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.225     ; 2.239      ;
; -1.469 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]          ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.220      ;
; -1.469 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]          ; zhuhoujia2023111413_003:motor_ctrl|state_timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.220      ;
; -1.469 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]          ; zhuhoujia2023111413_003:motor_ctrl|state_timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.220      ;
; -1.469 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[11]          ; zhuhoujia2023111413_003:motor_ctrl|state_timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.220      ;
+--------+-------------------------------------------------------------+----------------------------------------------------+--------------+-------------+--------------+------------+------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'zhuhoujia2023111413_004:pwm_gen|pwm_clk'                                                                                                                                                                    ;
+--------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+
; Slack  ; From Node                                         ; To Node                                        ; Launch Clock                            ; Latch Clock                             ; Relationship ; Clock Skew ; Data Delay ;
+--------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+
; -1.235 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 2.065      ;
; -1.233 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 2.063      ;
; -1.182 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 2.132      ;
; -1.180 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 2.130      ;
; -1.178 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 2.128      ;
; -1.176 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 2.126      ;
; -1.156 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.986      ;
; -1.154 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.984      ;
; -1.147 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.977      ;
; -1.145 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.975      ;
; -1.106 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.936      ;
; -1.105 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.935      ;
; -1.105 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.935      ;
; -1.103 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.933      ;
; -1.102 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.932      ;
; -1.101 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[0] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.931      ;
; -1.057 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 2.007      ;
; -1.055 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 2.005      ;
; -1.053 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 2.003      ;
; -1.052 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 2.002      ;
; -1.052 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 2.002      ;
; -1.050 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 2.000      ;
; -1.049 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.999      ;
; -1.049 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.999      ;
; -1.048 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.998      ;
; -1.048 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.998      ;
; -1.048 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.998      ;
; -1.046 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.996      ;
; -1.045 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.995      ;
; -1.044 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.994      ;
; -1.027 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.857      ;
; -1.026 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.856      ;
; -1.026 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.856      ;
; -1.024 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.854      ;
; -1.023 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.853      ;
; -1.022 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.852      ;
; -1.021 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.971      ;
; -1.019 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.969      ;
; -1.018 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.848      ;
; -1.017 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.847      ;
; -1.017 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.847      ;
; -1.015 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.845      ;
; -1.014 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.844      ;
; -1.013 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[2] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.843      ;
; -0.966 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.796      ;
; -0.964 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.794      ;
; -0.943 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.893      ;
; -0.941 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.891      ;
; -0.928 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.878      ;
; -0.927 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.877      ;
; -0.927 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.877      ;
; -0.925 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.875      ;
; -0.924 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.874      ;
; -0.923 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.873      ;
; -0.892 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.842      ;
; -0.891 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.841      ;
; -0.891 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.841      ;
; -0.889 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.839      ;
; -0.888 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.838      ;
; -0.887 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.837      ;
; -0.837 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.667      ;
; -0.836 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.666      ;
; -0.836 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.666      ;
; -0.834 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.664      ;
; -0.833 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.663      ;
; -0.832 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.662      ;
; -0.814 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.764      ;
; -0.813 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.763      ;
; -0.813 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.763      ;
; -0.811 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.761      ;
; -0.810 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.760      ;
; -0.809 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.759      ;
; -0.758 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.348     ; 1.387      ;
; -0.756 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.348     ; 1.385      ;
; -0.666 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.496      ;
; -0.664 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.494      ;
; -0.629 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.348     ; 1.258      ;
; -0.628 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.348     ; 1.257      ;
; -0.628 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.348     ; 1.257      ;
; -0.626 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.348     ; 1.255      ;
; -0.625 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.348     ; 1.254      ;
; -0.624 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.348     ; 1.253      ;
; -0.543 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.493      ;
; -0.541 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.491      ;
; -0.498 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.328      ;
; -0.496 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.326      ;
; -0.491 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.441      ;
; -0.489 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.439      ;
; -0.484 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.314      ;
; -0.483 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.313      ;
; -0.483 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.313      ;
; -0.481 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.311      ;
; -0.461 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.291      ;
; -0.460 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.147     ; 1.290      ;
; -0.414 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.364      ;
; -0.413 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.363      ;
; -0.413 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.363      ;
; -0.411 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.361      ;
; -0.410 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.360      ;
; -0.409 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 1.000        ; -0.037     ; 1.359      ;
+--------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'clk_50MHz'                                                                                                                                                                                ;
+-------+--------------------------------------------------------------------+--------------------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node                                                          ; To Node                                                            ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+--------------------------------------------------------------------+--------------------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[15]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[15]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[31]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[31]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[14]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[14]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[30]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[30]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[29]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[29]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[13]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[13]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[28]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[28]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[12]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[12]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[11]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[11]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[27]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[27]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[10]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[10]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[26]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[26]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]              ; zhuhoujia2023111413_005:dht11_sensor|received_data[9]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[25]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[25]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[24]             ; zhuhoujia2023111413_005:dht11_sensor|received_data[24]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.179 ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]              ; zhuhoujia2023111413_005:dht11_sensor|received_data[8]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_OBSTACLE    ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_OBSTACLE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_005:dht11_sensor|state.START                   ; zhuhoujia2023111413_005:dht11_sensor|state.START                   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE                    ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE                    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE                ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE                 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_006:lcd_display|lcd_en                         ; zhuhoujia2023111413_006:lcd_display|lcd_en                         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_006:lcd_display|lcd_rst                        ; zhuhoujia2023111413_006:lcd_display|lcd_rst                        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_LINE_FOLLOW ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_LINE_FOLLOW ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_006:lcd_display|state.RESET                    ; zhuhoujia2023111413_006:lcd_display|state.RESET                    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_006:lcd_display|state.INIT                     ; zhuhoujia2023111413_006:lcd_display|state.INIT                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP             ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM              ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_HUM              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.193 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH                  ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.314      ;
; 0.194 ; zhuhoujia2023111413_006:lcd_display|state.IDLE                     ; zhuhoujia2023111413_006:lcd_display|state.IDLE                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.314      ;
; 0.196 ; zhuhoujia2023111413_001:line_follow|sensor_reg1[1]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[1]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.317      ;
; 0.198 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[1]                 ; zhuhoujia2023111413_001:line_follow|sensor_filtered[1]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.319      ;
; 0.198 ; zhuhoujia2023111413_001:line_follow|sensor_reg1[3]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[3]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.318      ;
; 0.200 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[0]                 ; zhuhoujia2023111413_001:line_follow|sensor_filtered[0]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.321      ;
; 0.200 ; zhuhoujia2023111413_002:obstacle_avoid|obs_reg3[1]                 ; zhuhoujia2023111413_002:obstacle_avoid|obs_filtered[1]             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.320      ;
; 0.202 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[3]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[3]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.322      ;
; 0.205 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0]           ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.326      ;
; 0.205 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[1]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[1]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.326      ;
; 0.205 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]                ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.326      ;
; 0.209 ; zhuhoujia2023111413_005:dht11_sensor|timer[19]                     ; zhuhoujia2023111413_005:dht11_sensor|timer[19]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.329      ;
; 0.259 ; zhuhoujia2023111413_001:line_follow|sensor_reg1[0]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[0]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.380      ;
; 0.260 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[0]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[0]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.381      ;
; 0.266 ; zhuhoujia2023111413_001:line_follow|sensor_reg2[2]                 ; zhuhoujia2023111413_001:line_follow|sensor_reg3[2]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.386      ;
; 0.275 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP             ; zhuhoujia2023111413_006:lcd_display|lcd_data[7]                    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.395      ;
; 0.276 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_TEMP             ; zhuhoujia2023111413_006:lcd_display|lcd_data[5]                    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.396      ;
; 0.276 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[9]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.236      ; 0.596      ;
; 0.277 ; zhuhoujia2023111413_003:motor_ctrl|current_state.STATE_OBSTACLE    ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.398      ;
; 0.279 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[8]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.236      ; 0.599      ;
; 0.280 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[12]                 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[14]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.236      ; 0.600      ;
; 0.283 ; zhuhoujia2023111413_001:line_follow|sensor_filtered[0]             ; zhuhoujia2023111413_001:line_follow|control_signal[1]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.403      ;
; 0.289 ; zhuhoujia2023111413_001:line_follow|sensor_filtered[0]             ; zhuhoujia2023111413_001:line_follow|control_signal[0]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.409      ;
; 0.293 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[1]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[1]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.414      ;
; 0.294 ; zhuhoujia2023111413_001:line_follow|debounce_counter[1]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[1]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.414      ;
; 0.295 ; zhuhoujia2023111413_001:line_follow|debounce_counter[2]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[2]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.415      ;
; 0.296 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[1]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[1]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.425      ;
; 0.297 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[7]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[7]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.418      ;
; 0.297 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[5]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.418      ;
; 0.298 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[8]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[8]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.298 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[5]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[5]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.298 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[3]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[3]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.298 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[2]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[2]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.298 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[1]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.298 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[2]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.298 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[3]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.299 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[6]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[6]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.420      ;
; 0.299 ; zhuhoujia2023111413_004:pwm_gen|clk_divider[4]                     ; zhuhoujia2023111413_004:pwm_gen|clk_divider[4]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.420      ;
; 0.299 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[4]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.420      ;
; 0.299 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10]                 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[10]                 ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.427      ;
; 0.301 ; zhuhoujia2023111413_005:dht11_sensor|timer[10]                     ; zhuhoujia2023111413_005:dht11_sensor|timer[10]                     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.421      ;
; 0.303 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[15]    ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[15]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]              ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[9]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.424      ;
; 0.304 ; zhuhoujia2023111413_001:line_follow|debounce_counter[15]           ; zhuhoujia2023111413_001:line_follow|debounce_counter[15]           ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[11]    ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[11]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[0]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[0]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[3]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[3]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[5]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[5]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[13]    ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[13]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]              ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]              ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]              ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[1]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[6]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_001:line_follow|debounce_counter[11]           ; zhuhoujia2023111413_001:line_follow|debounce_counter[11]           ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_001:line_follow|debounce_counter[0]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[0]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_001:line_follow|debounce_counter[3]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[3]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_001:line_follow|debounce_counter[5]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[5]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_001:line_follow|debounce_counter[13]           ; zhuhoujia2023111413_001:line_follow|debounce_counter[13]           ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[6]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[6]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[7]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[7]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[9]     ; zhuhoujia2023111413_002:obstacle_avoid|obs_debounce_counter[9]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]               ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]               ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]                      ; zhuhoujia2023111413_005:dht11_sensor|timer[8]                      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]                ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]                ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.306 ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]                  ; zhuhoujia2023111413_003:motor_ctrl|state_timer[7]                  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.426      ;
; 0.306 ; zhuhoujia2023111413_001:line_follow|debounce_counter[6]            ; zhuhoujia2023111413_001:line_follow|debounce_counter[6]            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.426      ;
+-------+--------------------------------------------------------------------+--------------------------------------------------------------------+--------------+-------------+--------------+------------+------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'zhuhoujia2023111413_004:pwm_gen|pwm_clk'                                                                                                                                                                    ;
+-------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+
; Slack ; From Node                                         ; To Node                                        ; Launch Clock                            ; Latch Clock                             ; Relationship ; Clock Skew ; Data Delay ;
+-------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+
; 0.193 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.314      ;
; 0.305 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.426      ;
; 0.306 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.427      ;
; 0.306 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.427      ;
; 0.454 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.575      ;
; 0.455 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.576      ;
; 0.463 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.584      ;
; 0.466 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.587      ;
; 0.467 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.588      ;
; 0.520 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.641      ;
; 0.530 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.651      ;
; 0.533 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.654      ;
; 0.538 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.659      ;
; 0.548 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.669      ;
; 0.549 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.670      ;
; 0.551 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.672      ;
; 0.552 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.673      ;
; 0.583 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.704      ;
; 0.586 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.707      ;
; 0.601 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.722      ;
; 0.604 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.725      ;
; 0.616 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.737      ;
; 0.616 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.737      ;
; 0.617 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.738      ;
; 0.618 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.739      ;
; 0.618 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.739      ;
; 0.680 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.801      ;
; 0.681 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.802      ;
; 0.682 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.241     ; 0.555      ;
; 0.683 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.804      ;
; 0.684 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.805      ;
; 0.684 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.241     ; 0.557      ;
; 0.691 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[3]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.812      ;
; 0.716 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.241     ; 0.589      ;
; 0.717 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.241     ; 0.590      ;
; 0.744 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[2]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.865      ;
; 0.749 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.241     ; 0.622      ;
; 0.750 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[3]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.241     ; 0.623      ;
; 0.756 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 0.822      ;
; 0.756 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 0.822      ;
; 0.756 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 0.822      ;
; 0.758 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 0.824      ;
; 0.758 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 0.824      ;
; 0.759 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 0.825      ;
; 0.841 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[1]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.962      ;
; 0.842 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[0]    ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[4] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 0.963      ;
; 0.881 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.047     ; 0.948      ;
; 0.882 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[7] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.047     ; 0.949      ;
; 0.890 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 0.956      ;
; 0.892 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 0.958      ;
; 0.893 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 0.959      ;
; 0.894 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 0.960      ;
; 0.895 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 0.961      ;
; 0.895 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 0.961      ;
; 0.925 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.045      ;
; 0.926 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.046      ;
; 0.926 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.046      ;
; 0.928 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.048      ;
; 0.928 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.048      ;
; 0.929 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.049      ;
; 0.942 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 1.008      ;
; 0.942 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 1.008      ;
; 0.942 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 1.008      ;
; 0.944 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 1.010      ;
; 0.944 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 1.010      ;
; 0.944 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.240     ; 0.818      ;
; 0.945 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 1.011      ;
; 0.945 ; zhuhoujia2023111413_003:motor_ctrl|motor_dir[1]   ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.240     ; 0.819      ;
; 0.991 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 1.057      ;
; 0.993 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 1.059      ;
; 0.994 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 1.060      ;
; 0.995 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 1.061      ;
; 0.996 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 1.062      ;
; 0.996 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[3] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.048     ; 1.062      ;
; 1.061 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 1.182      ;
; 1.063 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[7]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.037      ; 1.184      ;
; 1.067 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.047     ; 1.134      ;
; 1.068 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[5] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.047     ; 1.135      ;
; 1.085 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.205      ;
; 1.086 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.206      ;
; 1.086 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.206      ;
; 1.088 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.208      ;
; 1.088 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.208      ;
; 1.089 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[6]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.209      ;
; 1.090 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.047     ; 1.157      ;
; 1.091 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.047     ; 1.158      ;
; 1.096 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.241     ; 0.969      ;
; 1.098 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.241     ; 0.971      ;
; 1.099 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.241     ; 0.972      ;
; 1.100 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.241     ; 0.973      ;
; 1.101 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.241     ; 0.974      ;
; 1.101 ; zhuhoujia2023111413_003:motor_ctrl|motor_speed[6] ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; -0.241     ; 0.974      ;
; 1.131 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[0] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.251      ;
; 1.131 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.251      ;
; 1.131 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.251      ;
; 1.133 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_a[1] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.253      ;
; 1.133 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[3] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.253      ;
; 1.134 ; zhuhoujia2023111413_004:pwm_gen|pwm_counter[5]    ; zhuhoujia2023111413_004:pwm_gen|motor_pwm_b[2] ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 0.000        ; 0.036      ; 1.254      ;
+-------+---------------------------------------------------+------------------------------------------------+-----------------------------------------+-----------------------------------------+--------------+------------+------------+


----------------------------------------------
; Fast 1200mV 0C Model Metastability Summary ;
----------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------------------------------+
; Multicorner Timing Analysis Summary                                                                    ;
+------------------------------------------+----------+-------+----------+---------+---------------------+
; Clock                                    ; Setup    ; Hold  ; Recovery ; Removal ; Minimum Pulse Width ;
+------------------------------------------+----------+-------+----------+---------+---------------------+
; Worst-case Slack                         ; -5.258   ; 0.179 ; N/A      ; N/A     ; -3.000              ;
;  clk_50MHz                               ; -5.258   ; 0.179 ; N/A      ; N/A     ; -3.000              ;
;  zhuhoujia2023111413_004:pwm_gen|pwm_clk ; -4.262   ; 0.193 ; N/A      ; N/A     ; -1.487              ;
; Design-wide TNS                          ; -618.964 ; 0.0   ; 0.0      ; 0.0     ; -328.653            ;
;  clk_50MHz                               ; -576.966 ; 0.000 ; N/A      ; N/A     ; -304.861            ;
;  zhuhoujia2023111413_004:pwm_gen|pwm_clk ; -41.998  ; 0.000 ; N/A      ; N/A     ; -23.792             ;
+------------------------------------------+----------+-------+----------+---------+---------------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Board Trace Model Assignments                                                                                                                                                                                                                                                                                                                                                                                     ;
+----------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; Pin            ; I/O Standard ; Near Tline Length ; Near Tline L per Length ; Near Tline C per Length ; Near Series R ; Near Differential R ; Near Pull-up R ; Near Pull-down R ; Near C ; Far Tline Length ; Far Tline L per Length ; Far Tline C per Length ; Far Series R ; Far Pull-up R ; Far Pull-down R ; Far C ; Termination Voltage ; Far Differential R ; EBD File Name ; EBD Signal Name ; EBD Far-end ;
+----------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; motor_dir[0]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[1]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[2]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[3]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm_a[0] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm_a[1] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm_a[2] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm_a[3] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm_b[0] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm_b[1] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm_b[2] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm_b[3] ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_rst        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_rw         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_en         ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[0]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[1]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[2]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[3]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[4]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[5]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[6]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[7]    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; dht11_data     ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_DCLK~  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_nCEO~  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
+----------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+


+----------------------------------------------------------------------------+
; Input Transition Times                                                     ;
+-------------------------+--------------+-----------------+-----------------+
; Pin                     ; I/O Standard ; 10-90 Rise Time ; 90-10 Fall Time ;
+-------------------------+--------------+-----------------+-----------------+
; dht11_data              ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; clk_50MHz               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; rst_n                   ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; obstacle_sensor[0]      ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; obstacle_sensor[1]      ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[0]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[1]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[2]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[3]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_ASDO_DATA1~     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_FLASH_nCE_nCSO~ ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_DATA0~          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
+-------------------------+--------------+-----------------+-----------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             ;
+----------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin            ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+----------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; motor_dir[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; motor_dir[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; motor_pwm_a[0] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.33 V              ; -0.00421 V          ; 0.165 V                              ; 0.078 V                              ; 3.11e-09 s                  ; 2.85e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.33 V             ; -0.00421 V         ; 0.165 V                             ; 0.078 V                             ; 3.11e-09 s                 ; 2.85e-09 s                 ; Yes                       ; Yes                       ;
; motor_pwm_a[1] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_a[2] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.33 V              ; -0.00425 V          ; 0.168 V                              ; 0.058 V                              ; 3.12e-09 s                  ; 2.87e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.33 V             ; -0.00425 V         ; 0.168 V                             ; 0.058 V                             ; 3.12e-09 s                 ; 2.87e-09 s                 ; Yes                       ; Yes                       ;
; motor_pwm_a[3] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_b[0] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; motor_pwm_b[1] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_b[2] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_b[3] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rst        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rw         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; lcd_en         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[0]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[1]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[2]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[3]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[4]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; lcd_data[5]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; lcd_data[6]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.33 V              ; -0.00421 V          ; 0.165 V                              ; 0.078 V                              ; 3.11e-09 s                  ; 2.85e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.33 V             ; -0.00421 V         ; 0.165 V                             ; 0.078 V                             ; 3.11e-09 s                 ; 2.85e-09 s                 ; Yes                       ; Yes                       ;
; lcd_data[7]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; dht11_data     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.36 V              ; -0.00737 V          ; 0.209 V                              ; 0.012 V                              ; 5.22e-10 s                  ; 5.33e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.36 V             ; -0.00737 V         ; 0.209 V                             ; 0.012 V                             ; 5.22e-10 s                 ; 5.33e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.74e-09 V                   ; 2.37 V              ; -0.0346 V           ; 0.198 V                              ; 0.094 V                              ; 3.14e-10 s                  ; 2.92e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.74e-09 V                  ; 2.37 V             ; -0.0346 V          ; 0.198 V                             ; 0.094 V                             ; 3.14e-10 s                 ; 2.92e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.57e-09 V                   ; 2.37 V              ; -0.00683 V          ; 0.171 V                              ; 0.018 V                              ; 4.97e-10 s                  ; 6.66e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.57e-09 V                  ; 2.37 V             ; -0.00683 V         ; 0.171 V                             ; 0.018 V                             ; 4.97e-10 s                 ; 6.66e-10 s                 ; Yes                       ; Yes                       ;
+----------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 85c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+----------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin            ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+----------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; motor_dir[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_a[0] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.33 V              ; -0.0022 V           ; 0.088 V                              ; 0.056 V                              ; 3.76e-09 s                  ; 3.48e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.33 V             ; -0.0022 V          ; 0.088 V                             ; 0.056 V                             ; 3.76e-09 s                 ; 3.48e-09 s                 ; Yes                       ; Yes                       ;
; motor_pwm_a[1] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_a[2] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.33 V              ; -0.00229 V          ; 0.111 V                              ; 0.057 V                              ; 3.78e-09 s                  ; 3.5e-09 s                   ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.33 V             ; -0.00229 V         ; 0.111 V                             ; 0.057 V                             ; 3.78e-09 s                 ; 3.5e-09 s                  ; Yes                       ; Yes                       ;
; motor_pwm_a[3] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_b[0] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_b[1] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_b[2] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_b[3] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rst        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rw         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; lcd_en         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[0]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[1]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[2]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[3]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[4]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[5]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[6]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.33 V              ; -0.0022 V           ; 0.088 V                              ; 0.056 V                              ; 3.76e-09 s                  ; 3.48e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.33 V             ; -0.0022 V          ; 0.088 V                             ; 0.056 V                             ; 3.76e-09 s                 ; 3.48e-09 s                 ; Yes                       ; Yes                       ;
; lcd_data[7]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; dht11_data     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.34 V              ; -0.00869 V          ; 0.14 V                               ; 0.046 V                              ; 6.89e-10 s                  ; 6.56e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.34 V             ; -0.00869 V         ; 0.14 V                              ; 0.046 V                             ; 6.89e-10 s                 ; 6.56e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.55e-07 V                   ; 2.35 V              ; -0.00221 V          ; 0.097 V                              ; 0.005 V                              ; 4.49e-10 s                  ; 3.85e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.55e-07 V                  ; 2.35 V             ; -0.00221 V         ; 0.097 V                             ; 0.005 V                             ; 4.49e-10 s                 ; 3.85e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.54e-07 V                   ; 2.34 V              ; -0.00774 V          ; 0.109 V                              ; 0.026 V                              ; 6.58e-10 s                  ; 8.24e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.54e-07 V                  ; 2.34 V             ; -0.00774 V         ; 0.109 V                             ; 0.026 V                             ; 6.58e-10 s                 ; 8.24e-10 s                 ; Yes                       ; Yes                       ;
+----------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Fast 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             ;
+----------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin            ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+----------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; motor_dir[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_a[0] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.64 V              ; -0.0117 V           ; 0.202 V                              ; 0.176 V                              ; 2.38e-09 s                  ; 2.22e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.64 V             ; -0.0117 V          ; 0.202 V                             ; 0.176 V                             ; 2.38e-09 s                 ; 2.22e-09 s                 ; No                        ; Yes                       ;
; motor_pwm_a[1] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_a[2] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.64 V              ; -0.0113 V           ; 0.208 V                              ; 0.179 V                              ; 2.38e-09 s                  ; 2.23e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.64 V             ; -0.0113 V          ; 0.208 V                             ; 0.179 V                             ; 2.38e-09 s                 ; 2.23e-09 s                 ; No                        ; Yes                       ;
; motor_pwm_a[3] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; motor_pwm_b[0] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_b[1] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; motor_pwm_b[2] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm_b[3] ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rst        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; lcd_rw         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; lcd_en         ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[0]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[1]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; lcd_data[2]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[3]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; lcd_data[4]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[5]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[6]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.64 V              ; -0.0117 V           ; 0.202 V                              ; 0.176 V                              ; 2.38e-09 s                  ; 2.22e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.64 V             ; -0.0117 V          ; 0.202 V                             ; 0.176 V                             ; 2.38e-09 s                 ; 2.22e-09 s                 ; No                        ; Yes                       ;
; lcd_data[7]    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; dht11_data     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.71 V              ; -0.0317 V           ; 0.148 V                              ; 0.064 V                              ; 4.51e-10 s                  ; 4.15e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.71 V             ; -0.0317 V          ; 0.148 V                             ; 0.064 V                             ; 4.51e-10 s                 ; 4.15e-10 s                 ; No                        ; Yes                       ;
; ~ALTERA_DCLK~  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.22e-08 V                   ; 2.74 V              ; -0.06 V             ; 0.158 V                              ; 0.08 V                               ; 2.68e-10 s                  ; 2.19e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.22e-08 V                  ; 2.74 V             ; -0.06 V            ; 0.158 V                             ; 0.08 V                              ; 2.68e-10 s                 ; 2.19e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 3.54e-08 V                   ; 2.7 V               ; -0.00943 V          ; 0.276 V                              ; 0.035 V                              ; 3.19e-10 s                  ; 4.99e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 3.54e-08 V                  ; 2.7 V              ; -0.00943 V         ; 0.276 V                             ; 0.035 V                             ; 3.19e-10 s                 ; 4.99e-10 s                 ; No                        ; Yes                       ;
+----------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+-------------------------------------------------------------------------------------------------------------------------------+
; Setup Transfers                                                                                                               ;
+-----------------------------------------+-----------------------------------------+----------+----------+----------+----------+
; From Clock                              ; To Clock                                ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-----------------------------------------+-----------------------------------------+----------+----------+----------+----------+
; clk_50MHz                               ; clk_50MHz                               ; 7946     ; 0        ; 0        ; 0        ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; clk_50MHz                               ; 1        ; 1        ; 0        ; 0        ;
; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 128      ; 0        ; 0        ; 0        ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 116      ; 0        ; 0        ; 0        ;
+-----------------------------------------+-----------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


+-------------------------------------------------------------------------------------------------------------------------------+
; Hold Transfers                                                                                                                ;
+-----------------------------------------+-----------------------------------------+----------+----------+----------+----------+
; From Clock                              ; To Clock                                ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+-----------------------------------------+-----------------------------------------+----------+----------+----------+----------+
; clk_50MHz                               ; clk_50MHz                               ; 7946     ; 0        ; 0        ; 0        ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; clk_50MHz                               ; 1        ; 1        ; 0        ; 0        ;
; clk_50MHz                               ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 128      ; 0        ; 0        ; 0        ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; 116      ; 0        ; 0        ; 0        ;
+-----------------------------------------+-----------------------------------------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


---------------
; Report TCCS ;
---------------
No dedicated SERDES Transmitter circuitry present in device or used in design


---------------
; Report RSKM ;
---------------
No non-DPA dedicated SERDES Receiver circuitry present in device or used in design


+------------------------------------------------+
; Unconstrained Paths Summary                    ;
+---------------------------------+-------+------+
; Property                        ; Setup ; Hold ;
+---------------------------------+-------+------+
; Illegal Clocks                  ; 0     ; 0    ;
; Unconstrained Clocks            ; 0     ; 0    ;
; Unconstrained Input Ports       ; 8     ; 8    ;
; Unconstrained Input Port Paths  ; 272   ; 272  ;
; Unconstrained Output Ports      ; 23    ; 23   ;
; Unconstrained Output Port Paths ; 24    ; 24   ;
+---------------------------------+-------+------+


+--------------------------------------------------------------------------------------------------------+
; Clock Status Summary                                                                                   ;
+-----------------------------------------+-----------------------------------------+------+-------------+
; Target                                  ; Clock                                   ; Type ; Status      ;
+-----------------------------------------+-----------------------------------------+------+-------------+
; clk_50MHz                               ; clk_50MHz                               ; Base ; Constrained ;
; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; zhuhoujia2023111413_004:pwm_gen|pwm_clk ; Base ; Constrained ;
+-----------------------------------------+-----------------------------------------+------+-------------+


+-----------------------------------------------------------------------------------------------------------+
; Unconstrained Input Ports                                                                                 ;
+--------------------+--------------------------------------------------------------------------------------+
; Input Port         ; Comment                                                                              ;
+--------------------+--------------------------------------------------------------------------------------+
; dht11_data         ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[0]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[1]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[2]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[3]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[0] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[1] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; rst_n              ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------------+--------------------------------------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------+
; Unconstrained Output Ports                                                                             ;
+----------------+---------------------------------------------------------------------------------------+
; Output Port    ; Comment                                                                               ;
+----------------+---------------------------------------------------------------------------------------+
; dht11_data     ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[0]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[1]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[2]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[3]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[4]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[5]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[6]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[7]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_en         ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_rst        ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[0]   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[1]   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[2]   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[3]   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_a[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_a[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_a[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_a[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_b[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_b[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_b[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_b[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
+----------------+---------------------------------------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------+
; Unconstrained Input Ports                                                                                 ;
+--------------------+--------------------------------------------------------------------------------------+
; Input Port         ; Comment                                                                              ;
+--------------------+--------------------------------------------------------------------------------------+
; dht11_data         ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[0]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[1]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[2]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[3]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[0] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[1] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; rst_n              ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------------+--------------------------------------------------------------------------------------+


+--------------------------------------------------------------------------------------------------------+
; Unconstrained Output Ports                                                                             ;
+----------------+---------------------------------------------------------------------------------------+
; Output Port    ; Comment                                                                               ;
+----------------+---------------------------------------------------------------------------------------+
; dht11_data     ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[0]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[1]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[2]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[3]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[4]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[5]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[6]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[7]    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_en         ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_rst        ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[0]   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[1]   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[2]   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[3]   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_a[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_a[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_a[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_a[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_b[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_b[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_b[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm_b[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
+----------------+---------------------------------------------------------------------------------------+


+--------------------------+
; Timing Analyzer Messages ;
+--------------------------+
Info: *******************************************************************
Info: Running Quartus Prime Timing Analyzer
    Info: Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
    Info: Processing started: Thu Jul 17 20:12:42 2025
Info: Command: quartus_sta 11 -c 11
Info: qsta_default_script.tcl version: #1
Warning (18236): Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance.
Info (20030): Parallel compilation is enabled and will use 14 of the 14 processors detected
Info (21077): Low junction temperature is 0 degrees C
Info (21077): High junction temperature is 85 degrees C
Critical Warning (332012): Synopsys Design Constraints File file not found: '11.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design.
Info (332142): No user constrained base clocks found in the design. Calling "derive_clocks -period 1.0"
Info (332105): Deriving Clocks
    Info (332105): create_clock -period 1.000 -name zhuhoujia2023111413_004:pwm_gen|pwm_clk zhuhoujia2023111413_004:pwm_gen|pwm_clk
    Info (332105): create_clock -period 1.000 -name clk_50MHz clk_50MHz
Info (332143): No user constrained clock uncertainty found in the design. Calling "derive_clock_uncertainty"
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Info: Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON
Info: Analyzing Slow 1200mV 85C Model
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -5.258
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -5.258            -576.966 clk_50MHz 
    Info (332119):    -4.262             -41.998 zhuhoujia2023111413_004:pwm_gen|pwm_clk 
Info (332146): Worst-case hold slack is 0.435
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.435               0.000 clk_50MHz 
    Info (332119):     0.465               0.000 zhuhoujia2023111413_004:pwm_gen|pwm_clk 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000            -304.861 clk_50MHz 
    Info (332119):    -1.487             -23.792 zhuhoujia2023111413_004:pwm_gen|pwm_clk 
Info: Analyzing Slow 1200mV 0C Model
Info (334003): Started post-fitting delay annotation
Info (334004): Delay annotation completed successfully
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -4.951
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -4.951            -529.351 clk_50MHz 
    Info (332119):    -3.961             -38.475 zhuhoujia2023111413_004:pwm_gen|pwm_clk 
Info (332146): Worst-case hold slack is 0.384
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.384               0.000 clk_50MHz 
    Info (332119):     0.417               0.000 zhuhoujia2023111413_004:pwm_gen|pwm_clk 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000            -304.861 clk_50MHz 
    Info (332119):    -1.487             -23.792 zhuhoujia2023111413_004:pwm_gen|pwm_clk 
Info: Analyzing Fast 1200mV 0C Model
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -1.679
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -1.679            -142.303 clk_50MHz 
    Info (332119):    -1.235              -9.422 zhuhoujia2023111413_004:pwm_gen|pwm_clk 
Info (332146): Worst-case hold slack is 0.179
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.179               0.000 clk_50MHz 
    Info (332119):     0.193               0.000 zhuhoujia2023111413_004:pwm_gen|pwm_clk 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000            -219.562 clk_50MHz 
    Info (332119):    -1.000             -16.000 zhuhoujia2023111413_004:pwm_gen|pwm_clk 
Info (332102): Design is not fully constrained for setup requirements
Info (332102): Design is not fully constrained for hold requirements
Info: Quartus Prime Timing Analyzer was successful. 0 errors, 5 warnings
    Info: Peak virtual memory: 4875 megabytes
    Info: Processing ended: Thu Jul 17 20:12:44 2025
    Info: Elapsed time: 00:00:02
    Info: Total CPU time (on all processors): 00:00:02


