Timing Analyzer report for 11
Fri Jul 18 11:33:12 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Timing Analyzer Summary
  3. Parallel Compilation
  4. Clocks
  5. Slow 1200mV 85C Model Fmax Summary
  6. Timing Closure Recommendations
  7. Slow 1200mV 85C Model Setup Summary
  8. Slow 1200mV 85C Model Hold Summary
  9. Slow 1200mV 85C Model Recovery Summary
 10. Slow 1200mV 85C Model Removal Summary
 11. Slow 1200mV 85C Model Minimum Pulse Width Summary
 12. Slow 1200mV 85C Model Setup: 'clk_50MHz'
 13. Slow 1200mV 85C Model Hold: 'clk_50MHz'
 14. Slow 1200mV 85C Model Metastability Summary
 15. Slow 1200mV 0C Model Fmax Summary
 16. Slow 1200mV 0C Model Setup Summary
 17. Slow 1200mV 0C Model Hold Summary
 18. Slow 1200mV 0C Model Recovery Summary
 19. Slow 1200mV 0C Model Removal Summary
 20. Slow 1200mV 0C Model Minimum Pulse Width Summary
 21. Slow 1200mV 0C Model Setup: 'clk_50MHz'
 22. Slow 1200mV 0C Model Hold: 'clk_50MHz'
 23. Slow 1200mV 0C Model Metastability Summary
 24. Fast 1200mV 0C Model Setup Summary
 25. Fast 1200mV 0C Model Hold Summary
 26. Fast 1200mV 0C Model Recovery Summary
 27. Fast 1200mV 0C Model Removal Summary
 28. Fast 1200mV 0C Model Minimum Pulse Width Summary
 29. Fast 1200mV 0C Model Setup: 'clk_50MHz'
 30. Fast 1200mV 0C Model Hold: 'clk_50MHz'
 31. Fast 1200mV 0C Model Metastability Summary
 32. Multicorner Timing Analysis Summary
 33. Board Trace Model Assignments
 34. Input Transition Times
 35. Signal Integrity Metrics (Slow 1200mv 0c Model)
 36. Signal Integrity Metrics (Slow 1200mv 85c Model)
 37. Signal Integrity Metrics (Fast 1200mv 0c Model)
 38. Setup Transfers
 39. Hold Transfers
 40. Report TCCS
 41. Report RSKM
 42. Unconstrained Paths Summary
 43. Clock Status Summary
 44. Unconstrained Input Ports
 45. Unconstrained Output Ports
 46. Unconstrained Input Ports
 47. Unconstrained Output Ports
 48. Timing Analyzer Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+-----------------------------------------------------------------------------+
; Timing Analyzer Summary                                                     ;
+-----------------------+-----------------------------------------------------+
; Quartus Prime Version ; Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition ;
; Timing Analyzer       ; Legacy Timing Analyzer                              ;
; Revision Name         ; 11                                                  ;
; Device Family         ; Cyclone IV E                                        ;
; Device Name           ; EP4CE6E22C8                                         ;
; Timing Models         ; Final                                               ;
; Delay Model           ; Combined                                            ;
; Rise/Fall Delays      ; Enabled                                             ;
+-----------------------+-----------------------------------------------------+


+------------------------------------------+
; Parallel Compilation                     ;
+----------------------------+-------------+
; Processors                 ; Number      ;
+----------------------------+-------------+
; Number detected on machine ; 20          ;
; Maximum allowed            ; 14          ;
;                            ;             ;
; Average used               ; 1.08        ;
; Maximum used               ; 14          ;
;                            ;             ;
; Usage by Processor         ; % Time Used ;
;     Processor 1            ; 100.0%      ;
;     Processor 2            ;   1.4%      ;
;     Processors 3-14        ;   0.6%      ;
+----------------------------+-------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Clocks                                                                                                                                                                                ;
+------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------------+
; Clock Name ; Type ; Period ; Frequency  ; Rise  ; Fall  ; Duty Cycle ; Divide by ; Multiply by ; Phase ; Offset ; Edge List ; Edge Shift ; Inverted ; Master ; Source ; Targets       ;
+------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------------+
; clk_50MHz  ; Base ; 1.000  ; 1000.0 MHz ; 0.000 ; 0.500 ;            ;           ;             ;       ;        ;           ;            ;          ;        ;        ; { clk_50MHz } ;
+------------+------+--------+------------+-------+-------+------------+-----------+-------------+-------+--------+-----------+------------+----------+--------+--------+---------------+


+--------------------------------------------------+
; Slow 1200mV 85C Model Fmax Summary               ;
+------------+-----------------+------------+------+
; Fmax       ; Restricted Fmax ; Clock Name ; Note ;
+------------+-----------------+------------+------+
; 172.56 MHz ; 172.56 MHz      ; clk_50MHz  ;      ;
+------------+-----------------+------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


----------------------------------
; Timing Closure Recommendations ;
----------------------------------
HTML report is unavailable in plain text report export.


+-------------------------------------+
; Slow 1200mV 85C Model Setup Summary ;
+-----------+--------+----------------+
; Clock     ; Slack  ; End Point TNS  ;
+-----------+--------+----------------+
; clk_50MHz ; -4.795 ; -430.729       ;
+-----------+--------+----------------+


+------------------------------------+
; Slow 1200mV 85C Model Hold Summary ;
+-----------+-------+----------------+
; Clock     ; Slack ; End Point TNS  ;
+-----------+-------+----------------+
; clk_50MHz ; 0.445 ; 0.000          ;
+-----------+-------+----------------+


------------------------------------------
; Slow 1200mV 85C Model Recovery Summary ;
------------------------------------------
No paths to report.


-----------------------------------------
; Slow 1200mV 85C Model Removal Summary ;
-----------------------------------------
No paths to report.


+---------------------------------------------------+
; Slow 1200mV 85C Model Minimum Pulse Width Summary ;
+-----------+--------+------------------------------+
; Clock     ; Slack  ; End Point TNS                ;
+-----------+--------+------------------------------+
; clk_50MHz ; -3.000 ; -215.641                     ;
+-----------+--------+------------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Setup: 'clk_50MHz'                                                                                                                                       ;
+--------+------------------------------------------------+------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node                                      ; To Node                                        ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------------------------+------------------------------------------------+--------------+-------------+--------------+------------+------------+
; -4.795 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.704      ;
; -4.795 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.704      ;
; -4.795 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.704      ;
; -4.795 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.704      ;
; -4.794 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.703      ;
; -4.794 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.703      ;
; -4.794 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.703      ;
; -4.794 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.703      ;
; -4.606 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 5.035      ;
; -4.606 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 5.035      ;
; -4.606 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 5.035      ;
; -4.606 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 5.035      ;
; -4.567 ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.476      ;
; -4.567 ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.476      ;
; -4.567 ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.476      ;
; -4.567 ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.476      ;
; -4.499 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.928      ;
; -4.499 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.928      ;
; -4.499 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.928      ;
; -4.499 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.928      ;
; -4.478 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.907      ;
; -4.478 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.907      ;
; -4.478 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.907      ;
; -4.478 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.907      ;
; -4.467 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.388      ;
; -4.467 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.388      ;
; -4.467 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.388      ;
; -4.467 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.388      ;
; -4.447 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.876      ;
; -4.447 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.876      ;
; -4.447 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.876      ;
; -4.447 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.876      ;
; -4.411 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.320      ;
; -4.411 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.320      ;
; -4.411 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.320      ;
; -4.411 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.320      ;
; -4.401 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.322      ;
; -4.401 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.322      ;
; -4.401 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.322      ;
; -4.401 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.322      ;
; -4.323 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.380      ; 5.704      ;
; -4.323 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.380      ; 5.704      ;
; -4.323 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.380      ; 5.704      ;
; -4.323 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.380      ; 5.704      ;
; -4.323 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.380      ; 5.704      ;
; -4.323 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.380      ; 5.704      ;
; -4.322 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.380      ; 5.703      ;
; -4.322 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.380      ; 5.703      ;
; -4.322 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.380      ; 5.703      ;
; -4.322 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.380      ; 5.703      ;
; -4.322 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.380      ; 5.703      ;
; -4.322 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.380      ; 5.703      ;
; -4.296 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.589     ; 4.708      ;
; -4.296 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.589     ; 4.708      ;
; -4.296 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.589     ; 4.708      ;
; -4.296 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.589     ; 4.708      ;
; -4.283 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.712      ;
; -4.283 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.712      ;
; -4.283 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.712      ;
; -4.283 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.712      ;
; -4.278 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.199      ;
; -4.278 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.199      ;
; -4.278 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.199      ;
; -4.278 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.199      ;
; -4.254 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.163      ;
; -4.254 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.163      ;
; -4.254 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.163      ;
; -4.254 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.092     ; 5.163      ;
; -4.208 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.128      ;
; -4.208 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.128      ;
; -4.208 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[10] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.128      ;
; -4.208 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.128      ;
; -4.208 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.128      ;
; -4.208 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.128      ;
; -4.208 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.128      ;
; -4.208 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.128      ;
; -4.208 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.128      ;
; -4.207 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.127      ;
; -4.207 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.127      ;
; -4.207 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[10] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.127      ;
; -4.207 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.127      ;
; -4.207 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.127      ;
; -4.207 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.127      ;
; -4.207 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.127      ;
; -4.207 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.127      ;
; -4.207 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.081     ; 5.127      ;
; -4.196 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.625      ;
; -4.196 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.625      ;
; -4.196 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.625      ;
; -4.196 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.572     ; 4.625      ;
; -4.154 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.075      ;
; -4.154 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.075      ;
; -4.154 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.075      ;
; -4.154 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.080     ; 5.075      ;
; -4.134 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.100     ; 5.035      ;
; -4.134 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.100     ; 5.035      ;
; -4.134 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.100     ; 5.035      ;
; -4.134 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.100     ; 5.035      ;
; -4.134 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.100     ; 5.035      ;
; -4.134 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.100     ; 5.035      ;
+--------+------------------------------------------------+------------------------------------------------+--------------+-------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 85C Model Hold: 'clk_50MHz'                                                                                                                                                         ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node                                                ; To Node                                                ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; 0.445 ; zhuhoujia2023111413_004:motor_driver|pwm_counter[0]      ; zhuhoujia2023111413_004:motor_driver|pwm_counter[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 0.758      ;
; 0.452 ; zhuhoujia2023111413_006:lcd_display|char_index[1]        ; zhuhoujia2023111413_006:lcd_display|char_index[1]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.746      ;
; 0.452 ; zhuhoujia2023111413_006:lcd_display|lcd_en               ; zhuhoujia2023111413_006:lcd_display|lcd_en             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.746      ;
; 0.452 ; zhuhoujia2023111413_006:lcd_display|lcd_rst              ; zhuhoujia2023111413_006:lcd_display|lcd_rst            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.746      ;
; 0.452 ; zhuhoujia2023111413_004:motor_driver|clk_100hz           ; zhuhoujia2023111413_004:motor_driver|clk_100hz         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.746      ;
; 0.452 ; zhuhoujia2023111413_006:lcd_display|state.INIT           ; zhuhoujia2023111413_006:lcd_display|state.INIT         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.START         ; zhuhoujia2023111413_005:dht11_sensor|state.START       ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE          ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE       ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.453 ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE      ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.746      ;
; 0.454 ; zhuhoujia2023111413_004:motor_driver|clk_1m              ; zhuhoujia2023111413_004:motor_driver|clk_1m            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.746      ;
; 0.464 ; zhuhoujia2023111413_006:lcd_display|char_index[0]        ; zhuhoujia2023111413_006:lcd_display|char_index[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.758      ;
; 0.465 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.758      ;
; 0.500 ; zhuhoujia2023111413_005:dht11_sensor|timer[19]           ; zhuhoujia2023111413_005:dht11_sensor|timer[19]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.793      ;
; 0.504 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.796      ;
; 0.510 ; zhuhoujia2023111413_004:motor_driver|counter_1m[24]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[24]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.802      ;
; 0.515 ; zhuhoujia2023111413_004:motor_driver|pwm_counter[7]      ; zhuhoujia2023111413_004:motor_driver|pwm_counter[7]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.808      ;
; 0.517 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[19]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[19] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.811      ;
; 0.517 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[19]   ; zhuhoujia2023111413_004:motor_driver|clk_100hz         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.811      ;
; 0.518 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.810      ;
; 0.520 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.812      ;
; 0.632 ; zhuhoujia2023111413_005:dht11_sensor|state.START         ; zhuhoujia2023111413_005:dht11_sensor|dht11_oe          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 0.925      ;
; 0.636 ; zhuhoujia2023111413_005:dht11_sensor|timer[12]           ; zhuhoujia2023111413_005:dht11_sensor|timer[13]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.578      ; 1.426      ;
; 0.639 ; zhuhoujia2023111413_006:lcd_display|char_index[0]        ; zhuhoujia2023111413_006:lcd_display|lcd_data[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.933      ;
; 0.645 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[2] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.937      ;
; 0.645 ; zhuhoujia2023111413_001:line_follow|control_signal[0]    ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.937      ;
; 0.656 ; zhuhoujia2023111413_006:lcd_display|char_index[0]        ; zhuhoujia2023111413_006:lcd_display|lcd_data[0]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 0.950      ;
; 0.658 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[2] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 0.950      ;
; 0.718 ; zhuhoujia2023111413_003:motor_ctrl|motor_control[1]      ; zhuhoujia2023111413_004:motor_driver|motor_dir[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.010      ;
; 0.718 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.102      ; 1.032      ;
; 0.720 ; zhuhoujia2023111413_003:motor_ctrl|motor_control[1]      ; zhuhoujia2023111413_004:motor_driver|motor_dir[1]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.012      ;
; 0.720 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.102      ; 1.034      ;
; 0.721 ; zhuhoujia2023111413_003:motor_ctrl|motor_control[1]      ; zhuhoujia2023111413_004:motor_driver|motor_dir[2]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.013      ;
; 0.728 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.040      ;
; 0.729 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]            ; zhuhoujia2023111413_005:dht11_sensor|timer[2]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.041      ;
; 0.733 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.102      ; 1.047      ;
; 0.734 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.102      ; 1.048      ;
; 0.735 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.102      ; 1.049      ;
; 0.736 ; zhuhoujia2023111413_003:motor_ctrl|motor_control[1]      ; zhuhoujia2023111413_004:motor_driver|motor_dir[3]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.028      ;
; 0.736 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.102      ; 1.050      ;
; 0.737 ; zhuhoujia2023111413_004:motor_driver|counter_1m[1]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[1]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.029      ;
; 0.738 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]            ; zhuhoujia2023111413_005:dht11_sensor|timer[9]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.050      ;
; 0.739 ; zhuhoujia2023111413_004:motor_driver|counter_1m[2]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[2]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.031      ;
; 0.739 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_2023   ; zhuhoujia2023111413_006:lcd_display|lcd_data[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.033      ;
; 0.739 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]            ; zhuhoujia2023111413_005:dht11_sensor|timer[7]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.051      ;
; 0.742 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]            ; zhuhoujia2023111413_005:dht11_sensor|timer[8]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.054      ;
; 0.742 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[9]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[9]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.035      ;
; 0.743 ; zhuhoujia2023111413_004:motor_driver|counter_1m[0]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[0]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.055      ;
; 0.743 ; zhuhoujia2023111413_004:motor_driver|counter_1m[11]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[11]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.035      ;
; 0.743 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]            ; zhuhoujia2023111413_005:dht11_sensor|timer[6]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.100      ; 1.055      ;
; 0.743 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[5]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[5]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.036      ;
; 0.743 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[7]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[7]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.036      ;
; 0.744 ; zhuhoujia2023111413_004:motor_driver|counter_1m[15]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[15]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.036      ;
; 0.744 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[15]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[15] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.038      ;
; 0.745 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[3]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[3]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.038      ;
; 0.745 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[12]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[12] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.039      ;
; 0.746 ; zhuhoujia2023111413_004:motor_driver|counter_1m[17]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[17]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.038      ;
; 0.746 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[1]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[1]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.039      ;
; 0.746 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[14]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[14] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.040      ;
; 0.746 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[17]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[17] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.040      ;
; 0.746 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[16]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[16] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.040      ;
; 0.747 ; zhuhoujia2023111413_004:motor_driver|counter_1m[3]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[3]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.039      ;
; 0.747 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[4]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[4]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.040      ;
; 0.747 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[6]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[6]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.040      ;
; 0.748 ; zhuhoujia2023111413_004:motor_driver|counter_1m[6]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[6]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.040      ;
; 0.748 ; zhuhoujia2023111413_004:motor_driver|counter_1m[16]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[16]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.040      ;
; 0.748 ; zhuhoujia2023111413_004:motor_driver|counter_1m[19]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[19]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.040      ;
; 0.748 ; zhuhoujia2023111413_004:motor_driver|counter_1m[21]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[21]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.040      ;
; 0.748 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[2]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[2]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.041      ;
; 0.749 ; zhuhoujia2023111413_004:motor_driver|counter_1m[4]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[4]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.041      ;
; 0.749 ; zhuhoujia2023111413_005:dht11_sensor|timer[13]           ; zhuhoujia2023111413_005:dht11_sensor|timer[13]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.101      ; 1.062      ;
; 0.750 ; zhuhoujia2023111413_004:motor_driver|counter_1m[20]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[20]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.042      ;
; 0.751 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_2023   ; zhuhoujia2023111413_006:lcd_display|char_index[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.045      ;
; 0.752 ; zhuhoujia2023111413_006:lcd_display|char_index[1]        ; zhuhoujia2023111413_006:lcd_display|lcd_data[0]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.046      ;
; 0.752 ; zhuhoujia2023111413_006:lcd_display|char_index[1]        ; zhuhoujia2023111413_006:lcd_display|lcd_data[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.046      ;
; 0.756 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[18]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[18] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.050      ;
; 0.757 ; zhuhoujia2023111413_006:lcd_display|char_index[0]        ; zhuhoujia2023111413_006:lcd_display|char_index[1]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.051      ;
; 0.757 ; zhuhoujia2023111413_005:dht11_sensor|timer[11]           ; zhuhoujia2023111413_005:dht11_sensor|timer[13]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.578      ; 1.547      ;
; 0.760 ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.054      ;
; 0.761 ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[11]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[11] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.761 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[13]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[13] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.055      ;
; 0.762 ; zhuhoujia2023111413_004:motor_driver|counter_1m[7]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[7]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.054      ;
; 0.762 ; zhuhoujia2023111413_004:motor_driver|counter_1m[9]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[9]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.054      ;
; 0.762 ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.056      ;
; 0.763 ; zhuhoujia2023111413_004:motor_driver|counter_1m[13]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[13]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.055      ;
; 0.763 ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.763 ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.763 ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.763 ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.763 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[0]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[0]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.056      ;
; 0.763 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[10]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[10] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.057      ;
; 0.764 ; zhuhoujia2023111413_004:motor_driver|counter_1m[5]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[5]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.056      ;
; 0.764 ; zhuhoujia2023111413_004:motor_driver|counter_1m[23]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[23]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.080      ; 1.056      ;
; 0.764 ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.058      ;
; 0.764 ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[14]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.082      ; 1.058      ;
; 0.764 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[8]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[8]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.081      ; 1.057      ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+


-----------------------------------------------
; Slow 1200mV 85C Model Metastability Summary ;
-----------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------+
; Slow 1200mV 0C Model Fmax Summary                ;
+------------+-----------------+------------+------+
; Fmax       ; Restricted Fmax ; Clock Name ; Note ;
+------------+-----------------+------------+------+
; 183.39 MHz ; 183.39 MHz      ; clk_50MHz  ;      ;
+------------+-----------------+------------+------+
This panel reports FMAX for every clock in the design, regardless of the user-specified clock periods.  FMAX is only computed for paths where the source and destination registers or ports are driven by the same clock.  Paths of different clocks, including generated clocks, are ignored.  For paths between a clock and its inversion, FMAX is computed as if the rising and falling edges are scaled along with FMAX, such that the duty cycle (in terms of a percentage) is maintained. Altera recommends that you always use clock constraints and other slack reports for sign-off analysis.


+------------------------------------+
; Slow 1200mV 0C Model Setup Summary ;
+-----------+--------+---------------+
; Clock     ; Slack  ; End Point TNS ;
+-----------+--------+---------------+
; clk_50MHz ; -4.453 ; -393.850      ;
+-----------+--------+---------------+


+-----------------------------------+
; Slow 1200mV 0C Model Hold Summary ;
+-----------+-------+---------------+
; Clock     ; Slack ; End Point TNS ;
+-----------+-------+---------------+
; clk_50MHz ; 0.399 ; 0.000         ;
+-----------+-------+---------------+


-----------------------------------------
; Slow 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Slow 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------+
; Slow 1200mV 0C Model Minimum Pulse Width Summary ;
+-----------+--------+-----------------------------+
; Clock     ; Slack  ; End Point TNS               ;
+-----------+--------+-----------------------------+
; clk_50MHz ; -3.000 ; -215.641                    ;
+-----------+--------+-----------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Setup: 'clk_50MHz'                                                                                                                                        ;
+--------+------------------------------------------------+------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node                                      ; To Node                                        ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------------------------+------------------------------------------------+--------------+-------------+--------------+------------+------------+
; -4.453 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.371      ;
; -4.453 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.371      ;
; -4.453 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.371      ;
; -4.453 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.371      ;
; -4.453 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.371      ;
; -4.453 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.371      ;
; -4.453 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.371      ;
; -4.453 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.371      ;
; -4.342 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.805      ;
; -4.342 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.805      ;
; -4.342 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.805      ;
; -4.342 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.805      ;
; -4.239 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.702      ;
; -4.239 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.702      ;
; -4.239 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.702      ;
; -4.239 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.702      ;
; -4.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.155      ;
; -4.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.155      ;
; -4.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.155      ;
; -4.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.155      ;
; -4.199 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.129      ;
; -4.199 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.129      ;
; -4.199 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.129      ;
; -4.199 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.129      ;
; -4.196 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.659      ;
; -4.196 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.659      ;
; -4.196 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.659      ;
; -4.196 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.659      ;
; -4.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.654      ;
; -4.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.654      ;
; -4.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.654      ;
; -4.191 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.654      ;
; -4.139 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.069      ;
; -4.139 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.069      ;
; -4.139 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.069      ;
; -4.139 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 5.069      ;
; -4.096 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.014      ;
; -4.096 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.014      ;
; -4.096 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.014      ;
; -4.096 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 5.014      ;
; -4.020 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 4.950      ;
; -4.020 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 4.950      ;
; -4.020 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 4.950      ;
; -4.020 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 4.950      ;
; -4.020 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.483      ;
; -4.020 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.483      ;
; -4.020 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.483      ;
; -4.020 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.483      ;
; -4.018 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 4.936      ;
; -4.018 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 4.936      ;
; -4.018 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 4.936      ;
; -4.018 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.084     ; 4.936      ;
; -4.005 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.364      ; 5.371      ;
; -4.005 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.364      ; 5.371      ;
; -4.005 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.364      ; 5.371      ;
; -4.005 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.364      ; 5.371      ;
; -4.005 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.364      ; 5.371      ;
; -4.005 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.364      ; 5.371      ;
; -4.005 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.364      ; 5.371      ;
; -4.005 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.364      ; 5.371      ;
; -4.005 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.364      ; 5.371      ;
; -4.005 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.364      ; 5.371      ;
; -4.005 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.364      ; 5.371      ;
; -4.005 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.364      ; 5.371      ;
; -3.972 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.553     ; 4.421      ;
; -3.972 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.553     ; 4.421      ;
; -3.972 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.553     ; 4.421      ;
; -3.972 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.553     ; 4.421      ;
; -3.951 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.414      ;
; -3.951 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.414      ;
; -3.951 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.414      ;
; -3.951 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.539     ; 4.414      ;
; -3.901 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 4.831      ;
; -3.901 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 4.831      ;
; -3.901 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 4.831      ;
; -3.901 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.072     ; 4.831      ;
; -3.894 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.091     ; 4.805      ;
; -3.894 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.091     ; 4.805      ;
; -3.894 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.091     ; 4.805      ;
; -3.894 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.091     ; 4.805      ;
; -3.894 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.091     ; 4.805      ;
; -3.894 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.091     ; 4.805      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[10] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[10] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
; -3.878 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.073     ; 4.807      ;
+--------+------------------------------------------------+------------------------------------------------+--------------+-------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Slow 1200mV 0C Model Hold: 'clk_50MHz'                                                                                                                                                          ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node                                                ; To Node                                                ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; 0.399 ; zhuhoujia2023111413_004:motor_driver|pwm_counter[0]      ; zhuhoujia2023111413_004:motor_driver|pwm_counter[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.090      ; 0.684      ;
; 0.400 ; zhuhoujia2023111413_006:lcd_display|char_index[1]        ; zhuhoujia2023111413_006:lcd_display|char_index[1]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.669      ;
; 0.400 ; zhuhoujia2023111413_006:lcd_display|lcd_rst              ; zhuhoujia2023111413_006:lcd_display|lcd_rst            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.669      ;
; 0.400 ; zhuhoujia2023111413_004:motor_driver|clk_100hz           ; zhuhoujia2023111413_004:motor_driver|clk_100hz         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.669      ;
; 0.400 ; zhuhoujia2023111413_006:lcd_display|state.INIT           ; zhuhoujia2023111413_006:lcd_display|state.INIT         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.669      ;
; 0.401 ; zhuhoujia2023111413_006:lcd_display|lcd_en               ; zhuhoujia2023111413_006:lcd_display|lcd_en             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_004:motor_driver|clk_1m              ; zhuhoujia2023111413_004:motor_driver|clk_1m            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_005:dht11_sensor|state.START         ; zhuhoujia2023111413_005:dht11_sensor|state.START       ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE          ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE       ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.402 ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE      ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.669      ;
; 0.415 ; zhuhoujia2023111413_006:lcd_display|char_index[0]        ; zhuhoujia2023111413_006:lcd_display|char_index[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.684      ;
; 0.417 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.684      ;
; 0.463 ; zhuhoujia2023111413_005:dht11_sensor|timer[19]           ; zhuhoujia2023111413_005:dht11_sensor|timer[19]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.731      ;
; 0.465 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.732      ;
; 0.470 ; zhuhoujia2023111413_004:motor_driver|counter_1m[24]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[24]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.737      ;
; 0.475 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[19]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[19] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.744      ;
; 0.475 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[19]   ; zhuhoujia2023111413_004:motor_driver|clk_100hz         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.744      ;
; 0.476 ; zhuhoujia2023111413_004:motor_driver|pwm_counter[7]      ; zhuhoujia2023111413_004:motor_driver|pwm_counter[7]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.743      ;
; 0.484 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.751      ;
; 0.486 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.753      ;
; 0.567 ; zhuhoujia2023111413_005:dht11_sensor|timer[12]           ; zhuhoujia2023111413_005:dht11_sensor|timer[13]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.542      ; 1.304      ;
; 0.586 ; zhuhoujia2023111413_005:dht11_sensor|state.START         ; zhuhoujia2023111413_005:dht11_sensor|dht11_oe          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.853      ;
; 0.593 ; zhuhoujia2023111413_006:lcd_display|char_index[0]        ; zhuhoujia2023111413_006:lcd_display|lcd_data[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.862      ;
; 0.600 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[2] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.867      ;
; 0.601 ; zhuhoujia2023111413_001:line_follow|control_signal[0]    ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.868      ;
; 0.613 ; zhuhoujia2023111413_006:lcd_display|char_index[0]        ; zhuhoujia2023111413_006:lcd_display|lcd_data[0]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.882      ;
; 0.614 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[2] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.881      ;
; 0.667 ; zhuhoujia2023111413_005:dht11_sensor|timer[11]           ; zhuhoujia2023111413_005:dht11_sensor|timer[13]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.542      ; 1.404      ;
; 0.668 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.093      ; 0.956      ;
; 0.671 ; zhuhoujia2023111413_003:motor_ctrl|motor_control[1]      ; zhuhoujia2023111413_004:motor_driver|motor_dir[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.938      ;
; 0.671 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.093      ; 0.959      ;
; 0.673 ; zhuhoujia2023111413_003:motor_ctrl|motor_control[1]      ; zhuhoujia2023111413_004:motor_driver|motor_dir[1]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.940      ;
; 0.674 ; zhuhoujia2023111413_003:motor_ctrl|motor_control[1]      ; zhuhoujia2023111413_004:motor_driver|motor_dir[2]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.941      ;
; 0.677 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_2023   ; zhuhoujia2023111413_006:lcd_display|char_index[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.946      ;
; 0.678 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]            ; zhuhoujia2023111413_005:dht11_sensor|timer[2]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.964      ;
; 0.679 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.965      ;
; 0.682 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.093      ; 0.970      ;
; 0.684 ; zhuhoujia2023111413_004:motor_driver|counter_1m[1]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[1]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.951      ;
; 0.684 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.093      ; 0.972      ;
; 0.685 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.093      ; 0.973      ;
; 0.686 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]            ; zhuhoujia2023111413_005:dht11_sensor|timer[9]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.972      ;
; 0.687 ; zhuhoujia2023111413_004:motor_driver|counter_1m[2]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[2]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.954      ;
; 0.688 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.093      ; 0.976      ;
; 0.688 ; zhuhoujia2023111413_005:dht11_sensor|timer[10]           ; zhuhoujia2023111413_005:dht11_sensor|timer[13]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.542      ; 1.425      ;
; 0.689 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[9]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[9]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.957      ;
; 0.690 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]            ; zhuhoujia2023111413_005:dht11_sensor|timer[7]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.976      ;
; 0.690 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[7]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[7]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.958      ;
; 0.690 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[15]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[15] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.959      ;
; 0.691 ; zhuhoujia2023111413_004:motor_driver|counter_1m[11]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[11]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.958      ;
; 0.691 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]            ; zhuhoujia2023111413_005:dht11_sensor|timer[8]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.977      ;
; 0.691 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[5]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[5]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.959      ;
; 0.692 ; zhuhoujia2023111413_003:motor_ctrl|motor_control[1]      ; zhuhoujia2023111413_004:motor_driver|motor_dir[3]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.959      ;
; 0.692 ; zhuhoujia2023111413_004:motor_driver|counter_1m[17]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[17]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.959      ;
; 0.692 ; zhuhoujia2023111413_004:motor_driver|counter_1m[15]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[15]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.959      ;
; 0.693 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]            ; zhuhoujia2023111413_005:dht11_sensor|timer[6]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.979      ;
; 0.694 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[1]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[1]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.962      ;
; 0.694 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[3]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[3]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.962      ;
; 0.694 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[16]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[16] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.963      ;
; 0.694 ; zhuhoujia2023111413_005:dht11_sensor|timer[13]           ; zhuhoujia2023111413_005:dht11_sensor|timer[13]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.092      ; 0.981      ;
; 0.695 ; zhuhoujia2023111413_004:motor_driver|counter_1m[3]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[3]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.962      ;
; 0.695 ; zhuhoujia2023111413_004:motor_driver|counter_1m[0]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[0]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.091      ; 0.981      ;
; 0.695 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[6]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[6]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.963      ;
; 0.695 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[12]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[12] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.964      ;
; 0.695 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[17]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[17] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.964      ;
; 0.696 ; zhuhoujia2023111413_004:motor_driver|counter_1m[19]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[19]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.963      ;
; 0.696 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[2]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[2]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.964      ;
; 0.696 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[14]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[14] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.965      ;
; 0.697 ; zhuhoujia2023111413_004:motor_driver|counter_1m[4]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[4]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.964      ;
; 0.697 ; zhuhoujia2023111413_004:motor_driver|counter_1m[21]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[21]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.964      ;
; 0.697 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[4]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[4]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.965      ;
; 0.698 ; zhuhoujia2023111413_004:motor_driver|counter_1m[6]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[6]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.965      ;
; 0.698 ; zhuhoujia2023111413_004:motor_driver|counter_1m[16]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[16]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.965      ;
; 0.699 ; zhuhoujia2023111413_004:motor_driver|counter_1m[20]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[20]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.966      ;
; 0.702 ; zhuhoujia2023111413_006:lcd_display|char_index[1]        ; zhuhoujia2023111413_006:lcd_display|lcd_data[0]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.971      ;
; 0.702 ; zhuhoujia2023111413_006:lcd_display|char_index[1]        ; zhuhoujia2023111413_006:lcd_display|lcd_data[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.971      ;
; 0.704 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_2023   ; zhuhoujia2023111413_006:lcd_display|lcd_data[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.973      ;
; 0.704 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[13]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[13] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_004:motor_driver|counter_1m[9]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[9]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.972      ;
; 0.705 ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.973      ;
; 0.705 ; zhuhoujia2023111413_006:lcd_display|char_index[0]        ; zhuhoujia2023111413_006:lcd_display|char_index[1]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.974      ;
; 0.706 ; zhuhoujia2023111413_004:motor_driver|counter_1m[7]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[7]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.973      ;
; 0.706 ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.974      ;
; 0.706 ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.974      ;
; 0.706 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[11]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[11] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.975      ;
; 0.707 ; zhuhoujia2023111413_004:motor_driver|counter_1m[23]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[23]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.974      ;
; 0.707 ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.975      ;
; 0.707 ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.975      ;
; 0.707 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[18]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[18] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.976      ;
; 0.708 ; zhuhoujia2023111413_004:motor_driver|counter_1m[13]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[13]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.975      ;
; 0.708 ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.976      ;
; 0.708 ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.976      ;
; 0.709 ; zhuhoujia2023111413_004:motor_driver|counter_1m[5]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[5]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.976      ;
; 0.709 ; zhuhoujia2023111413_004:motor_driver|counter_1m[18]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[18]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.976      ;
; 0.709 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[10]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[10] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.074      ; 0.978      ;
; 0.710 ; zhuhoujia2023111413_004:motor_driver|counter_1m[12]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[12]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.072      ; 0.977      ;
; 0.710 ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.978      ;
; 0.710 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[8]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[8]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.073      ; 0.978      ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+


----------------------------------------------
; Slow 1200mV 0C Model Metastability Summary ;
----------------------------------------------
No synchronizer chains to report.


+------------------------------------+
; Fast 1200mV 0C Model Setup Summary ;
+-----------+--------+---------------+
; Clock     ; Slack  ; End Point TNS ;
+-----------+--------+---------------+
; clk_50MHz ; -1.450 ; -107.701      ;
+-----------+--------+---------------+


+-----------------------------------+
; Fast 1200mV 0C Model Hold Summary ;
+-----------+-------+---------------+
; Clock     ; Slack ; End Point TNS ;
+-----------+-------+---------------+
; clk_50MHz ; 0.186 ; 0.000         ;
+-----------+-------+---------------+


-----------------------------------------
; Fast 1200mV 0C Model Recovery Summary ;
-----------------------------------------
No paths to report.


----------------------------------------
; Fast 1200mV 0C Model Removal Summary ;
----------------------------------------
No paths to report.


+--------------------------------------------------+
; Fast 1200mV 0C Model Minimum Pulse Width Summary ;
+-----------+--------+-----------------------------+
; Clock     ; Slack  ; End Point TNS               ;
+-----------+--------+-----------------------------+
; clk_50MHz ; -3.000 ; -155.652                    ;
+-----------+--------+-----------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Setup: 'clk_50MHz'                                                                                                                                        ;
+--------+------------------------------------------------+------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack  ; From Node                                      ; To Node                                        ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+--------+------------------------------------------------+------------------------------------------------+--------------+-------------+--------------+------------+------------+
; -1.450 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.396      ;
; -1.450 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.396      ;
; -1.450 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.396      ;
; -1.450 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.396      ;
; -1.443 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.389      ;
; -1.443 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.389      ;
; -1.443 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.389      ;
; -1.443 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.389      ;
; -1.373 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.124      ;
; -1.373 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.124      ;
; -1.373 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.124      ;
; -1.373 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.124      ;
; -1.347 ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.293      ;
; -1.347 ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.293      ;
; -1.347 ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.293      ;
; -1.347 ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.293      ;
; -1.347 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.298      ;
; -1.347 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.298      ;
; -1.347 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.298      ;
; -1.347 ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.298      ;
; -1.342 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.093      ;
; -1.342 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.093      ;
; -1.342 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.093      ;
; -1.342 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.093      ;
; -1.341 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.092      ;
; -1.341 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.092      ;
; -1.341 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.092      ;
; -1.341 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.092      ;
; -1.333 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.084      ;
; -1.333 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.084      ;
; -1.333 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.084      ;
; -1.333 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.084      ;
; -1.300 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.251      ;
; -1.300 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.251      ;
; -1.300 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.251      ;
; -1.300 ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.251      ;
; -1.295 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.241      ;
; -1.295 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.241      ;
; -1.295 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.241      ;
; -1.295 ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.241      ;
; -1.258 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.151      ; 2.396      ;
; -1.258 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.151      ; 2.396      ;
; -1.258 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.151      ; 2.396      ;
; -1.258 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.151      ; 2.396      ;
; -1.258 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.151      ; 2.396      ;
; -1.258 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.151      ; 2.396      ;
; -1.257 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.008      ;
; -1.257 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.008      ;
; -1.257 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.008      ;
; -1.257 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 2.008      ;
; -1.251 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.202      ;
; -1.251 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.202      ;
; -1.251 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.202      ;
; -1.251 ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.202      ;
; -1.251 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.151      ; 2.389      ;
; -1.251 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.151      ; 2.389      ;
; -1.251 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.151      ; 2.389      ;
; -1.251 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.151      ; 2.389      ;
; -1.251 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.151      ; 2.389      ;
; -1.251 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; 0.151      ; 2.389      ;
; -1.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.240     ; 1.984      ;
; -1.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.240     ; 1.984      ;
; -1.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.240     ; 1.984      ;
; -1.237 ; zhuhoujia2023111413_005:dht11_sensor|timer[13] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.240     ; 1.984      ;
; -1.233 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.179      ;
; -1.233 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.179      ;
; -1.233 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.179      ;
; -1.233 ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.041     ; 2.179      ;
; -1.204 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.155      ;
; -1.204 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.155      ;
; -1.204 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.155      ;
; -1.204 ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.155      ;
; -1.200 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[0]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.951      ;
; -1.200 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[1]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.951      ;
; -1.200 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[3]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.951      ;
; -1.200 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; zhuhoujia2023111413_005:dht11_sensor|timer[5]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.236     ; 1.951      ;
; -1.193 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.144      ;
; -1.193 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.144      ;
; -1.193 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[10] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.144      ;
; -1.193 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.144      ;
; -1.193 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.144      ;
; -1.193 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.144      ;
; -1.193 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.144      ;
; -1.193 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.144      ;
; -1.193 ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.144      ;
; -1.186 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[19] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.137      ;
; -1.186 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[14] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.137      ;
; -1.186 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[10] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.137      ;
; -1.186 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[11] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.137      ;
; -1.186 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[12] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.137      ;
; -1.186 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[17] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.137      ;
; -1.186 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[16] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.137      ;
; -1.186 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.137      ;
; -1.186 ; zhuhoujia2023111413_005:dht11_sensor|timer[18] ; zhuhoujia2023111413_005:dht11_sensor|timer[15] ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.036     ; 2.137      ;
; -1.181 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.044     ; 2.124      ;
; -1.181 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[4]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.044     ; 2.124      ;
; -1.181 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[6]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.044     ; 2.124      ;
; -1.181 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[7]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.044     ; 2.124      ;
; -1.181 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[8]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.044     ; 2.124      ;
; -1.181 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]  ; zhuhoujia2023111413_005:dht11_sensor|timer[9]  ; clk_50MHz    ; clk_50MHz   ; 1.000        ; -0.044     ; 2.124      ;
+--------+------------------------------------------------+------------------------------------------------+--------------+-------------+--------------+------------+------------+


+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Fast 1200mV 0C Model Hold: 'clk_50MHz'                                                                                                                                                          ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; Slack ; From Node                                                ; To Node                                                ; Launch Clock ; Latch Clock ; Relationship ; Clock Skew ; Data Delay ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+
; 0.186 ; zhuhoujia2023111413_004:motor_driver|pwm_counter[0]      ; zhuhoujia2023111413_004:motor_driver|pwm_counter[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.314      ;
; 0.186 ; zhuhoujia2023111413_006:lcd_display|char_index[1]        ; zhuhoujia2023111413_006:lcd_display|char_index[1]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_006:lcd_display|lcd_en               ; zhuhoujia2023111413_006:lcd_display|lcd_en             ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_006:lcd_display|lcd_rst              ; zhuhoujia2023111413_006:lcd_display|lcd_rst            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_004:motor_driver|clk_100hz           ; zhuhoujia2023111413_004:motor_driver|clk_100hz         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.186 ; zhuhoujia2023111413_006:lcd_display|state.INIT           ; zhuhoujia2023111413_006:lcd_display|state.INIT         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_004:motor_driver|clk_1m              ; zhuhoujia2023111413_004:motor_driver|clk_1m            ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_005:dht11_sensor|state.START         ; zhuhoujia2023111413_005:dht11_sensor|state.START       ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE          ; zhuhoujia2023111413_005:dht11_sensor|state.IDLE        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE       ; zhuhoujia2023111413_005:dht11_sensor|state.RECEIVE     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.187 ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE      ; zhuhoujia2023111413_005:dht11_sensor|state.RESPONSE    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.307      ;
; 0.193 ; zhuhoujia2023111413_006:lcd_display|char_index[0]        ; zhuhoujia2023111413_006:lcd_display|char_index[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.314      ;
; 0.194 ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH        ; zhuhoujia2023111413_005:dht11_sensor|state.FINISH      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.314      ;
; 0.201 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.321      ;
; 0.202 ; zhuhoujia2023111413_005:dht11_sensor|timer[19]           ; zhuhoujia2023111413_005:dht11_sensor|timer[19]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.322      ;
; 0.203 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.323      ;
; 0.205 ; zhuhoujia2023111413_004:motor_driver|counter_1m[24]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[24]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.325      ;
; 0.207 ; zhuhoujia2023111413_004:motor_driver|pwm_counter[7]      ; zhuhoujia2023111413_004:motor_driver|pwm_counter[7]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.328      ;
; 0.208 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[0] ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.328      ;
; 0.208 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[19]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[19] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.329      ;
; 0.208 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[19]   ; zhuhoujia2023111413_004:motor_driver|clk_100hz         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.329      ;
; 0.253 ; zhuhoujia2023111413_001:line_follow|control_signal[0]    ; zhuhoujia2023111413_003:motor_ctrl|final_control[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.373      ;
; 0.258 ; zhuhoujia2023111413_005:dht11_sensor|state.START         ; zhuhoujia2023111413_005:dht11_sensor|dht11_oe          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.378      ;
; 0.259 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[2] ; zhuhoujia2023111413_003:motor_ctrl|final_control[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.379      ;
; 0.260 ; zhuhoujia2023111413_006:lcd_display|char_index[0]        ; zhuhoujia2023111413_006:lcd_display|lcd_data[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.381      ;
; 0.261 ; zhuhoujia2023111413_006:lcd_display|char_index[0]        ; zhuhoujia2023111413_006:lcd_display|lcd_data[0]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.382      ;
; 0.265 ; zhuhoujia2023111413_002:obstacle_avoid|control_signal[2] ; zhuhoujia2023111413_003:motor_ctrl|final_control[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.385      ;
; 0.270 ; zhuhoujia2023111413_005:dht11_sensor|timer[12]           ; zhuhoujia2023111413_005:dht11_sensor|timer[13]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.235      ; 0.589      ;
; 0.286 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_2023   ; zhuhoujia2023111413_006:lcd_display|char_index[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.407      ;
; 0.287 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[1]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.415      ;
; 0.288 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[2]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.416      ;
; 0.291 ; zhuhoujia2023111413_005:dht11_sensor|timer[2]            ; zhuhoujia2023111413_005:dht11_sensor|timer[2]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.419      ;
; 0.292 ; zhuhoujia2023111413_005:dht11_sensor|timer[4]            ; zhuhoujia2023111413_005:dht11_sensor|timer[4]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.420      ;
; 0.293 ; zhuhoujia2023111413_004:motor_driver|counter_1m[1]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[1]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.413      ;
; 0.294 ; zhuhoujia2023111413_004:motor_driver|counter_1m[2]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[2]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.414      ;
; 0.294 ; zhuhoujia2023111413_006:lcd_display|state.DISPLAY_2023   ; zhuhoujia2023111413_006:lcd_display|lcd_data[3]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.415      ;
; 0.295 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[9]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[9]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.416      ;
; 0.296 ; zhuhoujia2023111413_004:motor_driver|counter_1m[11]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[11]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.416      ;
; 0.296 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[4]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.424      ;
; 0.296 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[5]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.424      ;
; 0.296 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.424      ;
; 0.296 ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]      ; zhuhoujia2023111413_005:dht11_sensor|bit_counter[3]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.424      ;
; 0.296 ; zhuhoujia2023111413_005:dht11_sensor|timer[9]            ; zhuhoujia2023111413_005:dht11_sensor|timer[9]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.424      ;
; 0.296 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[7]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[7]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.417      ;
; 0.297 ; zhuhoujia2023111413_004:motor_driver|counter_1m[0]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[0]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.045      ; 0.426      ;
; 0.297 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[5]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[5]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.418      ;
; 0.297 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[15]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[15] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.418      ;
; 0.298 ; zhuhoujia2023111413_004:motor_driver|counter_1m[17]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[17]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.418      ;
; 0.298 ; zhuhoujia2023111413_004:motor_driver|counter_1m[15]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[15]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.418      ;
; 0.298 ; zhuhoujia2023111413_005:dht11_sensor|timer[8]            ; zhuhoujia2023111413_005:dht11_sensor|timer[8]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.426      ;
; 0.298 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[1]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[1]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.298 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[2]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[2]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.298 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[3]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[3]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.298 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[6]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[6]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.298 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[17]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[17] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.419      ;
; 0.299 ; zhuhoujia2023111413_004:motor_driver|counter_1m[3]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[3]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.419      ;
; 0.299 ; zhuhoujia2023111413_004:motor_driver|counter_1m[4]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[4]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.419      ;
; 0.299 ; zhuhoujia2023111413_004:motor_driver|counter_1m[19]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[19]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.419      ;
; 0.299 ; zhuhoujia2023111413_005:dht11_sensor|timer[7]            ; zhuhoujia2023111413_005:dht11_sensor|timer[7]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.427      ;
; 0.299 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[4]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[4]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.420      ;
; 0.299 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[12]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[12] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.420      ;
; 0.299 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[16]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[16] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.420      ;
; 0.300 ; zhuhoujia2023111413_004:motor_driver|counter_1m[6]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[6]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.420      ;
; 0.300 ; zhuhoujia2023111413_004:motor_driver|counter_1m[16]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[16]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.420      ;
; 0.300 ; zhuhoujia2023111413_004:motor_driver|counter_1m[20]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[20]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.420      ;
; 0.300 ; zhuhoujia2023111413_004:motor_driver|counter_1m[21]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[21]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.420      ;
; 0.300 ; zhuhoujia2023111413_005:dht11_sensor|timer[6]            ; zhuhoujia2023111413_005:dht11_sensor|timer[6]          ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.428      ;
; 0.300 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[14]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[14] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.421      ;
; 0.302 ; zhuhoujia2023111413_003:motor_ctrl|motor_control[1]      ; zhuhoujia2023111413_004:motor_driver|motor_dir[0]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.422      ;
; 0.302 ; zhuhoujia2023111413_005:dht11_sensor|timer[13]           ; zhuhoujia2023111413_005:dht11_sensor|timer[13]         ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.044      ; 0.430      ;
; 0.302 ; zhuhoujia2023111413_006:lcd_display|char_index[1]        ; zhuhoujia2023111413_006:lcd_display|lcd_data[1]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.423      ;
; 0.303 ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[15]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.424      ;
; 0.303 ; zhuhoujia2023111413_006:lcd_display|char_index[1]        ; zhuhoujia2023111413_006:lcd_display|lcd_data[0]        ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.424      ;
; 0.304 ; zhuhoujia2023111413_003:motor_ctrl|motor_control[1]      ; zhuhoujia2023111413_004:motor_driver|motor_dir[3]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; zhuhoujia2023111413_003:motor_ctrl|motor_control[1]      ; zhuhoujia2023111413_004:motor_driver|motor_dir[1]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; zhuhoujia2023111413_004:motor_driver|counter_1m[9]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[9]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.424      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[11]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[1]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[3]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[5]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]    ; zhuhoujia2023111413_006:lcd_display|delay_counter[13]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[11]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[11] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.304 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[13]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[13] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_003:motor_ctrl|motor_control[1]      ; zhuhoujia2023111413_004:motor_driver|motor_dir[2]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_004:motor_driver|counter_1m[13]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[13]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_004:motor_driver|counter_1m[7]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[7]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_004:motor_driver|counter_1m[23]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[23]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.425      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[9]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[6]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[7]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[18]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[18] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[8]    ; zhuhoujia2023111413_004:motor_driver|counter_100hz[8]  ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_004:motor_driver|counter_100hz[10]   ; zhuhoujia2023111413_004:motor_driver|counter_100hz[10] ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.305 ; zhuhoujia2023111413_006:lcd_display|char_index[0]        ; zhuhoujia2023111413_006:lcd_display|char_index[1]      ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.426      ;
; 0.306 ; zhuhoujia2023111413_004:motor_driver|counter_1m[5]       ; zhuhoujia2023111413_004:motor_driver|counter_1m[5]     ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.426      ;
; 0.306 ; zhuhoujia2023111413_004:motor_driver|counter_1m[10]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[10]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.426      ;
; 0.306 ; zhuhoujia2023111413_004:motor_driver|counter_1m[12]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[12]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.426      ;
; 0.306 ; zhuhoujia2023111413_004:motor_driver|counter_1m[18]      ; zhuhoujia2023111413_004:motor_driver|counter_1m[18]    ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.036      ; 0.426      ;
; 0.306 ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[2]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.427      ;
; 0.306 ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]     ; zhuhoujia2023111413_006:lcd_display|delay_counter[4]   ; clk_50MHz    ; clk_50MHz   ; 0.000        ; 0.037      ; 0.427      ;
+-------+----------------------------------------------------------+--------------------------------------------------------+--------------+-------------+--------------+------------+------------+


----------------------------------------------
; Fast 1200mV 0C Model Metastability Summary ;
----------------------------------------------
No synchronizer chains to report.


+--------------------------------------------------------------------------------+
; Multicorner Timing Analysis Summary                                            ;
+------------------+----------+-------+----------+---------+---------------------+
; Clock            ; Setup    ; Hold  ; Recovery ; Removal ; Minimum Pulse Width ;
+------------------+----------+-------+----------+---------+---------------------+
; Worst-case Slack ; -4.795   ; 0.186 ; N/A      ; N/A     ; -3.000              ;
;  clk_50MHz       ; -4.795   ; 0.186 ; N/A      ; N/A     ; -3.000              ;
; Design-wide TNS  ; -430.729 ; 0.0   ; 0.0      ; 0.0     ; -215.641            ;
;  clk_50MHz       ; -430.729 ; 0.000 ; N/A      ; N/A     ; -215.641            ;
+------------------+----------+-------+----------+---------+---------------------+


+------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Board Trace Model Assignments                                                                                                                                                                                                                                                                                                                                                                                    ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; Pin           ; I/O Standard ; Near Tline Length ; Near Tline L per Length ; Near Tline C per Length ; Near Series R ; Near Differential R ; Near Pull-up R ; Near Pull-down R ; Near C ; Far Tline Length ; Far Tline L per Length ; Far Tline C per Length ; Far Series R ; Far Pull-up R ; Far Pull-down R ; Far C ; Termination Voltage ; Far Differential R ; EBD File Name ; EBD Signal Name ; EBD Far-end ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+
; motor_pwm[0]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm[1]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm[2]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_pwm[3]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[0]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[1]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[2]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; motor_dir[3]  ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; clk_1m        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; clk_100hz     ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_rst       ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_rw        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_en        ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[0]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[1]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[2]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[3]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[4]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[5]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[6]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; lcd_data[7]   ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; dht11_data    ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 in              ; 0 H/in                  ; 0 F/in                  ; short         ; -                   ; open           ; open             ; open   ; 0 in             ; 0 H/in                 ; 0 F/in                 ; short        ; open          ; open            ; open  ; 0 V                 ; -                  ; n/a           ; n/a             ; n/a         ;
+---------------+--------------+-------------------+-------------------------+-------------------------+---------------+---------------------+----------------+------------------+--------+------------------+------------------------+------------------------+--------------+---------------+-----------------+-------+---------------------+--------------------+---------------+-----------------+-------------+


+----------------------------------------------------------------------------+
; Input Transition Times                                                     ;
+-------------------------+--------------+-----------------+-----------------+
; Pin                     ; I/O Standard ; 10-90 Rise Time ; 90-10 Fall Time ;
+-------------------------+--------------+-----------------+-----------------+
; dht11_data              ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; clk_50MHz               ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; rst_n                   ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; obstacle_sensor[0]      ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; obstacle_sensor[1]      ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[0]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[1]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[2]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; line_sensor[3]          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_ASDO_DATA1~     ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_FLASH_nCE_nCSO~ ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
; ~ALTERA_DATA0~          ; 2.5 V        ; 2000 ps         ; 2000 ps         ;
+-------------------------+--------------+-----------------+-----------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; motor_pwm[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.33 V              ; -0.00425 V          ; 0.168 V                              ; 0.058 V                              ; 3.12e-09 s                  ; 2.87e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.33 V             ; -0.00425 V         ; 0.168 V                             ; 0.058 V                             ; 3.12e-09 s                 ; 2.87e-09 s                 ; Yes                       ; Yes                       ;
; motor_pwm[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; clk_1m        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.33 V              ; -0.00421 V          ; 0.165 V                              ; 0.078 V                              ; 3.11e-09 s                  ; 2.85e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.33 V             ; -0.00421 V         ; 0.165 V                             ; 0.078 V                             ; 3.11e-09 s                 ; 2.85e-09 s                 ; Yes                       ; Yes                       ;
; clk_100hz     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rst       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rw        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_en        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.33 V              ; -0.00421 V          ; 0.165 V                              ; 0.078 V                              ; 3.11e-09 s                  ; 2.85e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.33 V             ; -0.00421 V         ; 0.165 V                             ; 0.078 V                             ; 3.11e-09 s                 ; 2.85e-09 s                 ; Yes                       ; Yes                       ;
; lcd_data[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; lcd_data[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; lcd_data[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.07e-09 V                   ; 2.38 V              ; -0.0073 V           ; 0.097 V                              ; 0.018 V                              ; 4.24e-10 s                  ; 3.65e-10 s                  ; No                         ; Yes                        ; 2.32 V                      ; 2.07e-09 V                  ; 2.38 V             ; -0.0073 V          ; 0.097 V                             ; 0.018 V                             ; 4.24e-10 s                 ; 3.65e-10 s                 ; No                        ; Yes                       ;
; lcd_data[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.33 V              ; -0.00425 V          ; 0.168 V                              ; 0.058 V                              ; 3.12e-09 s                  ; 2.87e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.33 V             ; -0.00425 V         ; 0.168 V                             ; 0.058 V                             ; 3.12e-09 s                 ; 2.87e-09 s                 ; Yes                       ; Yes                       ;
; dht11_data    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.8e-09 V                    ; 2.37 V              ; -0.00373 V          ; 0.104 V                              ; 0.011 V                              ; 4.34e-10 s                  ; 3.82e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.8e-09 V                   ; 2.37 V             ; -0.00373 V         ; 0.104 V                             ; 0.011 V                             ; 4.34e-10 s                 ; 3.82e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.74e-09 V                   ; 2.37 V              ; -0.0346 V           ; 0.198 V                              ; 0.094 V                              ; 3.14e-10 s                  ; 2.92e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.74e-09 V                  ; 2.37 V             ; -0.0346 V          ; 0.198 V                             ; 0.094 V                             ; 3.14e-10 s                 ; 2.92e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.57e-09 V                   ; 2.37 V              ; -0.00683 V          ; 0.171 V                              ; 0.018 V                              ; 4.97e-10 s                  ; 6.66e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.57e-09 V                  ; 2.37 V             ; -0.00683 V         ; 0.171 V                             ; 0.018 V                             ; 4.97e-10 s                 ; 6.66e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Slow 1200mv 85c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; motor_pwm[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.33 V              ; -0.00229 V          ; 0.111 V                              ; 0.057 V                              ; 3.78e-09 s                  ; 3.5e-09 s                   ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.33 V             ; -0.00229 V         ; 0.111 V                             ; 0.057 V                             ; 3.78e-09 s                 ; 3.5e-09 s                  ; Yes                       ; Yes                       ;
; motor_pwm[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; clk_1m        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.33 V              ; -0.0022 V           ; 0.088 V                              ; 0.056 V                              ; 3.76e-09 s                  ; 3.48e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.33 V             ; -0.0022 V          ; 0.088 V                             ; 0.056 V                             ; 3.76e-09 s                 ; 3.48e-09 s                 ; Yes                       ; Yes                       ;
; clk_100hz     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rst       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rw        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_en        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.33 V              ; -0.0022 V           ; 0.088 V                              ; 0.056 V                              ; 3.76e-09 s                  ; 3.48e-09 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.33 V             ; -0.0022 V          ; 0.088 V                             ; 0.056 V                             ; 3.76e-09 s                 ; 3.48e-09 s                 ; Yes                       ; Yes                       ;
; lcd_data[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.93e-07 V                   ; 2.35 V              ; -0.00834 V          ; 0.127 V                              ; 0.035 V                              ; 4.7e-10 s                   ; 4.64e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.93e-07 V                  ; 2.35 V             ; -0.00834 V         ; 0.127 V                             ; 0.035 V                             ; 4.7e-10 s                  ; 4.64e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.33 V              ; -0.00229 V          ; 0.111 V                              ; 0.057 V                              ; 3.78e-09 s                  ; 3.5e-09 s                   ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.33 V             ; -0.00229 V         ; 0.111 V                             ; 0.057 V                             ; 3.78e-09 s                 ; 3.5e-09 s                  ; Yes                       ; Yes                       ;
; dht11_data    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.85e-07 V                   ; 2.35 V              ; -0.0123 V           ; 0.144 V                              ; 0.042 V                              ; 4.81e-10 s                  ; 4.81e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.85e-07 V                  ; 2.35 V             ; -0.0123 V          ; 0.144 V                             ; 0.042 V                             ; 4.81e-10 s                 ; 4.81e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 1.55e-07 V                   ; 2.35 V              ; -0.00221 V          ; 0.097 V                              ; 0.005 V                              ; 4.49e-10 s                  ; 3.85e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 1.55e-07 V                  ; 2.35 V             ; -0.00221 V         ; 0.097 V                             ; 0.005 V                             ; 4.49e-10 s                 ; 3.85e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.32 V                       ; 2.54e-07 V                   ; 2.34 V              ; -0.00774 V          ; 0.109 V                              ; 0.026 V                              ; 6.58e-10 s                  ; 8.24e-10 s                  ; Yes                        ; Yes                        ; 2.32 V                      ; 2.54e-07 V                  ; 2.34 V             ; -0.00774 V         ; 0.109 V                             ; 0.026 V                             ; 6.58e-10 s                 ; 8.24e-10 s                 ; Yes                       ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Signal Integrity Metrics (Fast 1200mv 0c Model)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; Pin           ; I/O Standard ; Board Delay on Rise ; Board Delay on Fall ; Steady State Voh at FPGA Pin ; Steady State Vol at FPGA Pin ; Voh Max at FPGA Pin ; Vol Min at FPGA Pin ; Ringback Voltage on Rise at FPGA Pin ; Ringback Voltage on Fall at FPGA Pin ; 10-90 Rise Time at FPGA Pin ; 90-10 Fall Time at FPGA Pin ; Monotonic Rise at FPGA Pin ; Monotonic Fall at FPGA Pin ; Steady State Voh at Far-end ; Steady State Vol at Far-end ; Voh Max at Far-end ; Vol Min at Far-end ; Ringback Voltage on Rise at Far-end ; Ringback Voltage on Fall at Far-end ; 10-90 Rise Time at Far-end ; 90-10 Fall Time at Far-end ; Monotonic Rise at Far-end ; Monotonic Fall at Far-end ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+
; motor_pwm[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_pwm[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.64 V              ; -0.0113 V           ; 0.208 V                              ; 0.179 V                              ; 2.38e-09 s                  ; 2.23e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.64 V             ; -0.0113 V          ; 0.208 V                             ; 0.179 V                             ; 2.38e-09 s                 ; 2.23e-09 s                 ; No                        ; Yes                       ;
; motor_pwm[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[0]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[1]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[2]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; motor_dir[3]  ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; clk_1m        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.64 V              ; -0.0117 V           ; 0.202 V                              ; 0.176 V                              ; 2.38e-09 s                  ; 2.22e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.64 V             ; -0.0117 V          ; 0.202 V                             ; 0.176 V                             ; 2.38e-09 s                 ; 2.22e-09 s                 ; No                        ; Yes                       ;
; clk_100hz     ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rst       ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_rw        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_en        ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.64 V              ; -0.0117 V           ; 0.202 V                              ; 0.176 V                              ; 2.38e-09 s                  ; 2.22e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.64 V             ; -0.0117 V          ; 0.202 V                             ; 0.176 V                             ; 2.38e-09 s                 ; 2.22e-09 s                 ; No                        ; Yes                       ;
; lcd_data[0]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[1]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[2]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[3]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[4]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[5]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.74e-08 V                   ; 2.73 V              ; -0.0384 V           ; 0.169 V                              ; 0.089 V                              ; 2.7e-10 s                   ; 2.62e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.74e-08 V                  ; 2.73 V             ; -0.0384 V          ; 0.169 V                             ; 0.089 V                             ; 2.7e-10 s                  ; 2.62e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[6]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; lcd_data[7]   ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.64 V              ; -0.0113 V           ; 0.208 V                              ; 0.179 V                              ; 2.38e-09 s                  ; 2.23e-09 s                  ; No                         ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.64 V             ; -0.0113 V          ; 0.208 V                             ; 0.179 V                             ; 2.38e-09 s                 ; 2.23e-09 s                 ; No                        ; Yes                       ;
; dht11_data    ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 4.05e-08 V                   ; 2.72 V              ; -0.0349 V           ; 0.173 V                              ; 0.1 V                                ; 2.72e-10 s                  ; 2.69e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 4.05e-08 V                  ; 2.72 V             ; -0.0349 V          ; 0.173 V                             ; 0.1 V                               ; 2.72e-10 s                 ; 2.69e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_DCLK~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 2.22e-08 V                   ; 2.74 V              ; -0.06 V             ; 0.158 V                              ; 0.08 V                               ; 2.68e-10 s                  ; 2.19e-10 s                  ; Yes                        ; Yes                        ; 2.62 V                      ; 2.22e-08 V                  ; 2.74 V             ; -0.06 V            ; 0.158 V                             ; 0.08 V                              ; 2.68e-10 s                 ; 2.19e-10 s                 ; Yes                       ; Yes                       ;
; ~ALTERA_nCEO~ ; 2.5 V        ; 0 s                 ; 0 s                 ; 2.62 V                       ; 3.54e-08 V                   ; 2.7 V               ; -0.00943 V          ; 0.276 V                              ; 0.035 V                              ; 3.19e-10 s                  ; 4.99e-10 s                  ; No                         ; Yes                        ; 2.62 V                      ; 3.54e-08 V                  ; 2.7 V              ; -0.00943 V         ; 0.276 V                             ; 0.035 V                             ; 3.19e-10 s                 ; 4.99e-10 s                 ; No                        ; Yes                       ;
+---------------+--------------+---------------------+---------------------+------------------------------+------------------------------+---------------------+---------------------+--------------------------------------+--------------------------------------+-----------------------------+-----------------------------+----------------------------+----------------------------+-----------------------------+-----------------------------+--------------------+--------------------+-------------------------------------+-------------------------------------+----------------------------+----------------------------+---------------------------+---------------------------+


+--------------------------------------------------------------------+
; Setup Transfers                                                    ;
+------------+-----------+----------+----------+----------+----------+
; From Clock ; To Clock  ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+------------+-----------+----------+----------+----------+----------+
; clk_50MHz  ; clk_50MHz ; 4913     ; 0        ; 0        ; 0        ;
+------------+-----------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


+--------------------------------------------------------------------+
; Hold Transfers                                                     ;
+------------+-----------+----------+----------+----------+----------+
; From Clock ; To Clock  ; RR Paths ; FR Paths ; RF Paths ; FF Paths ;
+------------+-----------+----------+----------+----------+----------+
; clk_50MHz  ; clk_50MHz ; 4913     ; 0        ; 0        ; 0        ;
+------------+-----------+----------+----------+----------+----------+
Entries labeled "false path" only account for clock-to-clock false paths and not path-based false paths. As a result, actual path counts may be lower than reported.


---------------
; Report TCCS ;
---------------
No dedicated SERDES Transmitter circuitry present in device or used in design


---------------
; Report RSKM ;
---------------
No non-DPA dedicated SERDES Receiver circuitry present in device or used in design


+------------------------------------------------+
; Unconstrained Paths Summary                    ;
+---------------------------------+-------+------+
; Property                        ; Setup ; Hold ;
+---------------------------------+-------+------+
; Illegal Clocks                  ; 0     ; 0    ;
; Unconstrained Clocks            ; 0     ; 0    ;
; Unconstrained Input Ports       ; 8     ; 8    ;
; Unconstrained Input Port Paths  ; 190   ; 190  ;
; Unconstrained Output Ports      ; 18    ; 18   ;
; Unconstrained Output Port Paths ; 19    ; 19   ;
+---------------------------------+-------+------+


+--------------------------------------------+
; Clock Status Summary                       ;
+-----------+-----------+------+-------------+
; Target    ; Clock     ; Type ; Status      ;
+-----------+-----------+------+-------------+
; clk_50MHz ; clk_50MHz ; Base ; Constrained ;
+-----------+-----------+------+-------------+


+-----------------------------------------------------------------------------------------------------------+
; Unconstrained Input Ports                                                                                 ;
+--------------------+--------------------------------------------------------------------------------------+
; Input Port         ; Comment                                                                              ;
+--------------------+--------------------------------------------------------------------------------------+
; dht11_data         ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[0]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[1]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[2]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[3]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[0] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[1] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; rst_n              ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------------+--------------------------------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------+
; Unconstrained Output Ports                                                                           ;
+--------------+---------------------------------------------------------------------------------------+
; Output Port  ; Comment                                                                               ;
+--------------+---------------------------------------------------------------------------------------+
; clk_1m       ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; clk_100hz    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; dht11_data   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[0]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[1]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[3]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[4]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[5]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_en       ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_rst      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------+---------------------------------------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------+
; Unconstrained Input Ports                                                                                 ;
+--------------------+--------------------------------------------------------------------------------------+
; Input Port         ; Comment                                                                              ;
+--------------------+--------------------------------------------------------------------------------------+
; dht11_data         ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[0]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[1]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[2]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; line_sensor[3]     ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[0] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; obstacle_sensor[1] ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
; rst_n              ; No input delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------------+--------------------------------------------------------------------------------------+


+------------------------------------------------------------------------------------------------------+
; Unconstrained Output Ports                                                                           ;
+--------------+---------------------------------------------------------------------------------------+
; Output Port  ; Comment                                                                               ;
+--------------+---------------------------------------------------------------------------------------+
; clk_1m       ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; clk_100hz    ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; dht11_data   ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[0]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[1]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[3]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[4]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_data[5]  ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_en       ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; lcd_rst      ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_dir[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[0] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[1] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[2] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
; motor_pwm[3] ; No output delay, min/max delays, false-path exceptions, or max skew assignments found ;
+--------------+---------------------------------------------------------------------------------------+


+--------------------------+
; Timing Analyzer Messages ;
+--------------------------+
Info: *******************************************************************
Info: Running Quartus Prime Timing Analyzer
    Info: Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
    Info: Processing started: Fri Jul 18 11:33:10 2025
Info: Command: quartus_sta 11 -c 11
Info: qsta_default_script.tcl version: #1
Warning (18236): Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance.
Info (20030): Parallel compilation is enabled and will use 14 of the 14 processors detected
Info (21077): Low junction temperature is 0 degrees C
Info (21077): High junction temperature is 85 degrees C
Critical Warning (332012): Synopsys Design Constraints File file not found: '11.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design.
Info (332142): No user constrained base clocks found in the design. Calling "derive_clocks -period 1.0"
Info (332105): Deriving Clocks
    Info (332105): create_clock -period 1.000 -name clk_50MHz clk_50MHz
Info (332143): No user constrained clock uncertainty found in the design. Calling "derive_clock_uncertainty"
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Info: Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON
Info: Analyzing Slow 1200mV 85C Model
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -4.795
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -4.795            -430.729 clk_50MHz 
Info (332146): Worst-case hold slack is 0.445
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.445               0.000 clk_50MHz 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000            -215.641 clk_50MHz 
Info: Analyzing Slow 1200mV 0C Model
Info (334003): Started post-fitting delay annotation
Info (334004): Delay annotation completed successfully
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -4.453
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -4.453            -393.850 clk_50MHz 
Info (332146): Worst-case hold slack is 0.399
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.399               0.000 clk_50MHz 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000            -215.641 clk_50MHz 
Info: Analyzing Fast 1200mV 0C Model
Info (332123): Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties.
Critical Warning (332148): Timing requirements not met
    Info (11105): For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer.
Info (332146): Worst-case setup slack is -1.450
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -1.450            -107.701 clk_50MHz 
Info (332146): Worst-case hold slack is 0.186
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):     0.186               0.000 clk_50MHz 
Info (332140): No Recovery paths to report
Info (332140): No Removal paths to report
Info (332146): Worst-case minimum pulse width slack is -3.000
    Info (332119):     Slack       End Point TNS Clock 
    Info (332119): ========= =================== =====================
    Info (332119):    -3.000            -155.652 clk_50MHz 
Info (332102): Design is not fully constrained for setup requirements
Info (332102): Design is not fully constrained for hold requirements
Info: Quartus Prime Timing Analyzer was successful. 0 errors, 5 warnings
    Info: Peak virtual memory: 4875 megabytes
    Info: Processing ended: Fri Jul 18 11:33:12 2025
    Info: Elapsed time: 00:00:02
    Info: Total CPU time (on all processors): 00:00:02


