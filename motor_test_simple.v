`timescale 1ns / 1ps

module motor_test_simple;

// Test signals
reg clk;
reg rst_n;
reg [3:0] motor_control;
reg [7:0] speed_control;
wire motor1_a, motor1_b;
wire motor2_a, motor2_b;
wire motor3_a, motor3_b;
wire motor4_a, motor4_b;
wire clk_1m;
wire clk_100hz;

// Instantiate the module under test
zhuhoujia14131454_004 uut (
    .clk(clk),
    .rst_n(rst_n),
    .motor_control(motor_control),
    .speed_control(speed_control),
    .motor1_a(motor1_a),
    .motor1_b(motor1_b),
    .motor2_a(motor2_a),
    .motor2_b(motor2_b),
    .motor3_a(motor3_a),
    .motor3_b(motor3_b),
    .motor4_a(motor4_a),
    .motor4_b(motor4_b),
    .clk_1m(clk_1m),
    .clk_100hz(clk_100hz)
);

// Clock generation - 50MHz
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// Motor command decode task
task decode_motor_command;
    input [3:0] command;
    begin
        case (command)
            4'd0: $display("Motor Control: STOP");
            4'd1: $display("Motor Control: FORWARD");
            4'd2: $display("Motor Control: BACKWARD");
            4'd3: $display("Motor Control: LEFT");
            4'd4: $display("Motor Control: RIGHT");
            4'd5: $display("Motor Control: SOFT_LEFT");
            4'd6: $display("Motor Control: SOFT_RIGHT");
            4'd7: $display("Motor Control: SHARP_LEFT");
            4'd8: $display("Motor Control: SHARP_RIGHT");
            default: $display("Motor Control: UNKNOWN");
        endcase
    end
endtask

// Motor state decode task
task decode_motor_state;
    input motor_a, motor_b;
    begin
        case ({motor_a, motor_b})
            2'b00: $display("STOP");
            2'b01: $display("REVERSE");
            2'b10: $display("FORWARD");
            2'b11: $display("BRAKE");
        endcase
    end
endtask

// Test scenario task
task test_motor_scenario;
    input [3:0] command;
    input [7:0] speed;
    begin
        motor_control = command;
        speed_control = speed;
        #1000; // Wait for multiple PWM cycles
        
        decode_motor_command(command);
        $display("Speed Control Value: %d", speed);
        $write("Motor1 (Left Front): ");
        decode_motor_state(motor1_a, motor1_b);
        $write("Motor2 (Left Rear): ");
        decode_motor_state(motor2_a, motor2_b);
        $write("Motor3 (Right Front): ");
        decode_motor_state(motor3_a, motor3_b);
        $write("Motor4 (Right Rear): ");
        decode_motor_state(motor4_a, motor4_b);
        $display("PWM Counter: %d", uut.pwm_counter);
        $display("");
    end
endtask

// Main test sequence
initial begin
    // Initialize
    rst_n = 0;
    motor_control = 4'd0;
    speed_control = 8'd0;
    
    $display("========================================");
    $display("Motor Drive and Clock Division Test Start");
    $display("========================================");
    
    // Release reset
    #100;
    rst_n = 1;
    #100;
    
    // Test 1: Basic movement modes
    $display("[Test Group 1: Basic Movement Modes]");
    $display("=== STOP State ===");
    test_motor_scenario(4'd0, 8'd0);
    $display("=== FORWARD - 50 percent speed ===");
    test_motor_scenario(4'd1, 8'd128);
    $display("=== BACKWARD - 50 percent speed ===");
    test_motor_scenario(4'd2, 8'd128);
    $display("=== LEFT - 50 percent speed ===");
    test_motor_scenario(4'd3, 8'd128);
    $display("=== RIGHT - 50 percent speed ===");
    test_motor_scenario(4'd4, 8'd128);
    
    // Test 2: Fine control modes
    $display("[Test Group 2: Fine Control Modes]");
    $display("=== SOFT LEFT - differential control ===");
    test_motor_scenario(4'd5, 8'd160);
    $display("=== SOFT RIGHT - differential control ===");
    test_motor_scenario(4'd6, 8'd160);
    $display("=== SHARP LEFT - in-place turn ===");
    test_motor_scenario(4'd7, 8'd200);
    $display("=== SHARP RIGHT - in-place turn ===");
    test_motor_scenario(4'd8, 8'd200);
    
    // Test 3: Different speed tests
    $display("[Test Group 3: Different Speed Tests]");
    $display("=== FORWARD - 25 percent speed ===");
    test_motor_scenario(4'd1, 8'd64);
    $display("=== FORWARD - 75 percent speed ===");
    test_motor_scenario(4'd1, 8'd192);
    $display("=== FORWARD - 100 percent speed ===");
    test_motor_scenario(4'd1, 8'd255);
    
    // Test 4: Clock division function
    $display("[Test Group 4: Clock Division Function Test]");
    $display("Testing 1MHz and 100Hz clock outputs...");
    
    #10000; // Wait for some time to observe clocks
    $display("1MHz Clock State: %b", clk_1m);
    $display("100Hz Clock State: %b", clk_100hz);
    
    // Test 5: Reset function test
    $display("[Test Group 5: Reset Function Test]");
    motor_control = 4'd1;
    speed_control = 8'd200;
    #500;
    $display("State before reset:");
    $display("Motor1: %b%b, Motor2: %b%b, Motor3: %b%b, Motor4: %b%b", 
             motor1_a, motor1_b, motor2_a, motor2_b, motor3_a, motor3_b, motor4_a, motor4_b);
    
    rst_n = 0;
    #100;
    $display("State during reset:");
    $display("Motor1: %b%b, Motor2: %b%b, Motor3: %b%b, Motor4: %b%b", 
             motor1_a, motor1_b, motor2_a, motor2_b, motor3_a, motor3_b, motor4_a, motor4_b);
    
    rst_n = 1;
    #100;
    $display("State after reset:");
    $display("Motor1: %b%b, Motor2: %b%b, Motor3: %b%b, Motor4: %b%b", 
             motor1_a, motor1_b, motor2_a, motor2_b, motor3_a, motor3_b, motor4_a, motor4_b);
    
    $display("");
    $display("========================================");
    $display("Motor Drive and Clock Division Test Complete");
    $display("========================================");
    $finish;
end

// Monitor motor state changes
always @(motor1_a or motor1_b or motor2_a or motor2_b or motor3_a or motor3_b or motor4_a or motor4_b) begin
    if ($time > 200) begin
        $display("Time=%0t: Motor state changed", $time);
    end
end

// Monitor clock outputs
always @(clk_1m) begin
    if ($time > 200 && $time < 1000) begin
        $display("Time=%0t: 1MHz clock switched to %b", $time, clk_1m);
    end
end

always @(clk_100hz) begin
    if ($time > 200 && $time < 5000) begin
        $display("Time=%0t: 100Hz clock switched to %b", $time, clk_100hz);
    end
end

// Generate waveform file
initial begin
    $dumpfile("motor_test_simple.vcd");
    $dumpvars(0, motor_test_simple);
end

endmodule
