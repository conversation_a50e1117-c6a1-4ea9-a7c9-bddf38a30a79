module zhuhoujia2023111413_003(
    input clk,
    input rst_n,
    input [2:0] line_control,      // 修改为3位输入
    input [2:0] obstacle_control,  // 修改为3位输入
    output reg [3:0] motor_dir,
    output reg [7:0] motor_speed,
    output reg [2:0] final_control  // 扩展为3位支持更多档位
);

// 扩展的控制信号定义 - 3位编码支持8种状态
parameter FORWARD_FULL  = 3'b000;  // 全速前进
parameter FORWARD_SLOW  = 3'b001;  // 缓慢前进
parameter LEFT_SLOW     = 3'b010;  // 缓左转
parameter LEFT_FAST     = 3'b011;  // 急左转
parameter RIGHT_SLOW    = 3'b100;  // 缓右转
parameter RIGHT_FAST    = 3'b101;  // 急右转
parameter BACK_FULL     = 3'b110;  // 全速倒车
parameter STOP          = 3'b111;  // 停止

// 电机方向定义：[左前, 左后, 右前, 右后]
// 1表示正转（前进方向），0表示反转（后退方向）
parameter MOTOR_FORWARD = 4'b1111;  // 前进：所有轮子正转
parameter MOTOR_LEFT    = 4'b0011;  // 左转：左轮反转，右轮正转
parameter MOTOR_RIGHT   = 4'b1100;  // 右转：左轮正转，右轮反转
parameter MOTOR_BACK    = 4'b0000;  // 后退：所有轮子反转
parameter MOTOR_STOP    = 4'b1111;  // 停止：保持方向但速度为0

// 速度档位定义
parameter SPEED_FULL    = 8'd255;   // 全速
parameter SPEED_HIGH    = 8'd200;   // 高速
parameter SPEED_MEDIUM  = 8'd150;   // 中速
parameter SPEED_LOW     = 8'd100;   // 低速
parameter SPEED_SLOW    = 8'd80;    // 缓慢
parameter SPEED_ZERO    = 8'd0;     // 停止

// 优先级控制：避障优先于循线
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        final_control <= STOP;
        motor_dir <= MOTOR_STOP;
        motor_speed <= SPEED_ZERO;
    end else begin
        // 避障优先级判断 - 直接使用3位信号
        if (obstacle_control != FORWARD_FULL) begin
            final_control <= obstacle_control;
        end else begin
            final_control <= line_control;
        end
        
        // 根据最终控制信号设置电机参数
        case (final_control)
            FORWARD_FULL: begin
                motor_dir <= MOTOR_FORWARD;     // 4'b1111 - 所有轮正转
                motor_speed <= SPEED_FULL;      // 255 - 全速前进
            end
            FORWARD_SLOW: begin
                motor_dir <= MOTOR_FORWARD;     // 4'b1111 - 所有轮正转
                motor_speed <= SPEED_MEDIUM;    // 150 - 中速前进
            end
            LEFT_SLOW: begin
                motor_dir <= MOTOR_LEFT;        // 4'b0011 - 左轮反转，右轮正转
                motor_speed <= SPEED_LOW;       // 100 - 缓左转
            end
            LEFT_FAST: begin
                motor_dir <= MOTOR_LEFT;        // 4'b0011 - 左轮反转，右轮正转
                motor_speed <= SPEED_HIGH;      // 200 - 急左转
            end
            RIGHT_SLOW: begin
                motor_dir <= MOTOR_RIGHT;       // 4'b1100 - 左轮正转，右轮反转
                motor_speed <= SPEED_LOW;       // 100 - 缓右转
            end
            RIGHT_FAST: begin
                motor_dir <= MOTOR_RIGHT;       // 4'b1100 - 左轮正转，右轮反转
                motor_speed <= SPEED_HIGH;      // 200 - 急右转
            end
            BACK_FULL: begin
                motor_dir <= MOTOR_BACK;        // 4'b0000 - 所有轮反转
                motor_speed <= SPEED_FULL;      // 255 - 全速倒车
            end
            STOP: begin
                motor_dir <= MOTOR_STOP;        // 4'b0000 - 所有轮停止
                motor_speed <= SPEED_ZERO;      // 0 - 停止
            end
            default: begin
                motor_dir <= MOTOR_STOP;
                motor_speed <= SPEED_ZERO;
            end
        endcase
    end
end

endmodule
