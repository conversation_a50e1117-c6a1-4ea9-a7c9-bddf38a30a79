module zhuhoujia2023111413_003(
    input clk,
    input rst_n,
    input [2:0] line_control,      // 修改为3位输入
    input [2:0] obstacle_control,  // 修改为3位输入
    output reg [2:0] final_control, // 最终控制信号输出
    output reg [3:0] motor_control, // 电机控制命令输出
    output reg [7:0] speed_control  // 速度控制值输出
);

// 控制信号定义
parameter FORWARD_FULL  = 3'b000;  // 全速前进
parameter FORWARD_SLOW  = 3'b001;  // 缓慢前进
parameter LEFT_SLOW     = 3'b010;  // 缓左转
parameter LEFT_FAST     = 3'b011;  // 急左转
parameter RIGHT_SLOW    = 3'b100;  // 缓右转
parameter RIGHT_FAST    = 3'b101;  // 急右转
parameter BACK_FULL     = 3'b110;  // 全速倒车
parameter STOP          = 3'b111;  // 停止

// 控制逻辑：避障优先于循线，并输出电机控制命令和速度
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        final_control <= STOP;
        motor_control <= 4'd0;  // 停止
        speed_control <= 8'd0;  // 速度为0
    end else begin
        // 避障优先级判断
        if (obstacle_control != FORWARD_FULL) begin
            final_control <= obstacle_control;
        end else begin
            final_control <= line_control;
        end

        // 根据最终控制信号设置电机控制命令和速度
        case (final_control)
            FORWARD_FULL: begin
                motor_control <= 4'd1;  // 前进
                speed_control <= 8'd200; // 高速
            end
            FORWARD_SLOW: begin
                motor_control <= 4'd1;  // 前进
                speed_control <= 8'd100; // 中速
            end
            LEFT_SLOW: begin
                motor_control <= 4'd5;  // 缓左转
                speed_control <= 8'd150; // 中等速度
            end
            LEFT_FAST: begin
                motor_control <= 4'd7;  // 急左转
                speed_control <= 8'd180; // 较高速度
            end
            RIGHT_SLOW: begin
                motor_control <= 4'd6;  // 缓右转
                speed_control <= 8'd150; // 中等速度
            end
            RIGHT_FAST: begin
                motor_control <= 4'd8;  // 急右转
                speed_control <= 8'd180; // 较高速度
            end
            BACK_FULL: begin
                motor_control <= 4'd2;  // 后退
                speed_control <= 8'd150; // 中等速度
            end
            STOP: begin
                motor_control <= 4'd0;  // 停止
                speed_control <= 8'd0;  // 速度为0
            end
            default: begin
                motor_control <= 4'd0;  // 停止
                speed_control <= 8'd0;  // 速度为0
            end
        endcase
    end
end

endmodule
