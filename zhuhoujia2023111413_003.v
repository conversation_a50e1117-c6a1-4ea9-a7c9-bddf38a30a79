module zhuhoujia2023111413_003(
    input clk,
    input rst_n,
    input [1:0] line_control,
    input [1:0] obstacle_control,
    output reg [3:0] motor_dir,
    output reg [7:0] motor_speed,
    output reg [2:0] final_control  // 扩展为3位支持更多档位
);

// 扩展的控制信号定义 - 3位编码支持8种状态
parameter FORWARD_FULL  = 3'b000;  // 全速前进
parameter FORWARD_SLOW  = 3'b001;  // 缓慢前进
parameter LEFT_SLOW     = 3'b010;  // 缓左转
parameter LEFT_FAST     = 3'b011;  // 急左转
parameter RIGHT_SLOW    = 3'b100;  // 缓右转
parameter RIGHT_FAST    = 3'b101;  // 急右转
parameter BACK_FULL     = 3'b110;  // 全速倒车
parameter STOP          = 3'b111;  // 停止

// 电机方向定义：[左前, 左后, 右前, 右后]
parameter MOTOR_FORWARD = 4'b1010;  // 前进
parameter MOTOR_LEFT    = 4'b0010;  // 左转（右轮前进）
parameter MOTOR_RIGHT   = 4'b1000;  // 右转（左轮前进）
parameter MOTOR_BACK    = 4'b0101;  // 后退
parameter MOTOR_STOP    = 4'b0000;  // 停止

// 速度档位定义
parameter SPEED_FULL    = 8'd255;   // 全速
parameter SPEED_HIGH    = 8'd200;   // 高速
parameter SPEED_MEDIUM  = 8'd150;   // 中速
parameter SPEED_LOW     = 8'd100;   // 低速
parameter SPEED_SLOW    = 8'd80;    // 缓慢
parameter SPEED_ZERO    = 8'd0;     // 停止

// 控制信号转换：将2位信号映射到3位档位控制
reg [2:0] line_control_ext;
reg [2:0] obstacle_control_ext;

// 循线控制信号扩展逻辑
always @(*) begin
    case (line_control)
        2'b00: line_control_ext = FORWARD_SLOW;   // 正常前进
        2'b01: line_control_ext = LEFT_SLOW;      // 缓左转
        2'b10: line_control_ext = RIGHT_SLOW;     // 缓右转
        2'b11: line_control_ext = STOP;           // 停止
        default: line_control_ext = STOP;
    endcase
end

// 避障控制信号扩展逻辑（避障需要更快的响应）
always @(*) begin
    case (obstacle_control)
        2'b00: obstacle_control_ext = FORWARD_FULL;  // 无障碍全速前进
        2'b01: obstacle_control_ext = LEFT_FAST;     // 急左转避障
        2'b10: obstacle_control_ext = RIGHT_FAST;    // 急右转避障
        2'b11: obstacle_control_ext = BACK_FULL;     // 全速后退
        default: obstacle_control_ext = STOP;
    endcase
end

// 优先级控制：避障优先于循线
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        final_control <= STOP;
        motor_dir <= MOTOR_STOP;
        motor_speed <= SPEED_ZERO;
    end else begin
        // 避障优先级判断
        if (obstacle_control != 2'b00) begin
            final_control <= obstacle_control_ext;
        end else begin
            final_control <= line_control_ext;
        end
        
        // 根据最终控制信号设置电机参数
        case (final_control)
            FORWARD_FULL: begin
                motor_dir <= MOTOR_FORWARD;
                motor_speed <= SPEED_FULL;      // 255 - 全速前进
            end
            FORWARD_SLOW: begin
                motor_dir <= MOTOR_FORWARD;
                motor_speed <= SPEED_MEDIUM;    // 150 - 中速前进
            end
            LEFT_SLOW: begin
                motor_dir <= MOTOR_LEFT;
                motor_speed <= SPEED_LOW;       // 100 - 缓左转
            end
            LEFT_FAST: begin
                motor_dir <= MOTOR_LEFT;
                motor_speed <= SPEED_HIGH;      // 200 - 急左转
            end
            RIGHT_SLOW: begin
                motor_dir <= MOTOR_RIGHT;
                motor_speed <= SPEED_LOW;       // 100 - 缓右转
            end
            RIGHT_FAST: begin
                motor_dir <= MOTOR_RIGHT;
                motor_speed <= SPEED_HIGH;      // 200 - 急右转
            end
            BACK_FULL: begin
                motor_dir <= MOTOR_BACK;
                motor_speed <= SPEED_FULL;      // 255 - 全速倒车
            end
            STOP: begin
                motor_dir <= MOTOR_STOP;
                motor_speed <= SPEED_ZERO;      // 0 - 停止
            end
            default: begin
                motor_dir <= MOTOR_STOP;
                motor_speed <= SPEED_ZERO;
            end
        endcase
    end
end

endmodule
