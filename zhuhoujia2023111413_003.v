module zhuhoujia2023111413_003(
    input clk,
    input rst_n,
    input [2:0] line_control,      // 修改为3位输入
    input [2:0] obstacle_control,  // 修改为3位输入
    output reg [3:0] motor_dir,
    output reg [7:0] motor_speed,
    output reg [2:0] final_control  // 扩展为3位支持更多档位
);

// 扩展的控制信号定义 - 3位编码支持8种状态
parameter FORWARD_FULL  = 3'b000;  // 全速前进
parameter FORWARD_SLOW  = 3'b001;  // 缓慢前进
parameter LEFT_SLOW     = 3'b010;  // 缓左转
parameter LEFT_FAST     = 3'b011;  // 急左转
parameter RIGHT_SLOW    = 3'b100;  // 缓右转
parameter RIGHT_FAST    = 3'b101;  // 急右转
parameter BACK_FULL     = 3'b110;  // 全速倒车
parameter STOP          = 3'b111;  // 停止

// 电机方向定义：[左前, 左后, 右前, 右后]
// 1表示正转（前进方向），0表示反转（后退方向）
parameter MOTOR_FORWARD = 4'b1111;  // 前进：所有轮子正转
parameter MOTOR_LEFT    = 4'b0011;  // 左转：左轮反转，右轮正转
parameter MOTOR_RIGHT   = 4'b1100;  // 右转：左轮正转，右轮反转
parameter MOTOR_BACK    = 4'b0000;  // 后退：所有轮子反转
parameter MOTOR_STOP    = 4'b1111;  // 停止：保持方向但速度为0

// 速度档位定义
parameter SPEED_FULL    = 8'd255;   // 全速
parameter SPEED_HIGH    = 8'd200;   // 高速
parameter SPEED_MEDIUM  = 8'd150;   // 中速
parameter SPEED_LOW     = 8'd100;   // 低速
parameter SPEED_SLOW    = 8'd80;    // 缓慢
parameter SPEED_ZERO    = 8'd0;     // 停止

// 状态机定义
parameter STATE_IDLE        = 3'b000;  // 空闲状态
parameter STATE_LINE_FOLLOW = 3'b001;  // 循线状态
parameter STATE_OBSTACLE    = 3'b010;  // 避障状态
parameter STATE_LOST        = 3'b011;  // 丢失线路状态
parameter STATE_STOP        = 3'b100;  // 停止状态

reg [2:0] current_state, next_state;
reg [15:0] state_timer;  // 状态计时器

// 状态机时序逻辑
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        current_state <= STATE_IDLE;
        state_timer <= 16'd0;
    end else begin
        current_state <= next_state;
        if (current_state != next_state) begin
            state_timer <= 16'd0;  // 状态切换时重置计时器
        end else begin
            state_timer <= state_timer + 1;
        end
    end
end

// 状态机组合逻辑
always @(*) begin
    case (current_state)
        STATE_IDLE: begin
            if (obstacle_control != FORWARD_FULL) begin
                next_state = STATE_OBSTACLE;
            end else if (line_control == STOP) begin
                next_state = STATE_STOP;
            end else begin
                next_state = STATE_LINE_FOLLOW;
            end
        end

        STATE_LINE_FOLLOW: begin
            if (obstacle_control != FORWARD_FULL) begin
                next_state = STATE_OBSTACLE;
            end else if (line_control == STOP) begin
                next_state = STATE_STOP;
            end else if (line_control == FORWARD_SLOW && state_timer > 16'd5000) begin
                next_state = STATE_LOST;  // 长时间缓慢前进可能丢失线路
            end else begin
                next_state = STATE_LINE_FOLLOW;
            end
        end

        STATE_OBSTACLE: begin
            if (obstacle_control == FORWARD_FULL) begin
                if (state_timer > 16'd2000) begin  // 避障完成后延时
                    next_state = STATE_LINE_FOLLOW;
                end else begin
                    next_state = STATE_OBSTACLE;
                end
            end else begin
                next_state = STATE_OBSTACLE;
            end
        end

        STATE_LOST: begin
            if (obstacle_control != FORWARD_FULL) begin
                next_state = STATE_OBSTACLE;
            end else if (line_control != FORWARD_SLOW) begin
                next_state = STATE_LINE_FOLLOW;  // 重新找到线路
            end else if (state_timer > 16'd10000) begin
                next_state = STATE_STOP;  // 长时间找不到线路则停止
            end else begin
                next_state = STATE_LOST;
            end
        end

        STATE_STOP: begin
            if (obstacle_control != FORWARD_FULL) begin
                next_state = STATE_OBSTACLE;
            end else if (line_control != STOP) begin
                next_state = STATE_LINE_FOLLOW;
            end else begin
                next_state = STATE_STOP;
            end
        end

        default: next_state = STATE_IDLE;
    endcase
end

// 输出控制逻辑
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        final_control <= STOP;
        motor_dir <= MOTOR_STOP;
        motor_speed <= SPEED_ZERO;
    end else begin
        case (current_state)
            STATE_IDLE: begin
                final_control <= STOP;
                motor_dir <= MOTOR_STOP;
                motor_speed <= SPEED_ZERO;
            end

            STATE_LINE_FOLLOW: begin
                final_control <= line_control;
            end

            STATE_OBSTACLE: begin
                final_control <= obstacle_control;
            end

            STATE_LOST: begin
                final_control <= FORWARD_SLOW;  // 缓慢前进寻找线路
            end

            STATE_STOP: begin
                final_control <= STOP;
            end

            default: begin
                final_control <= STOP;
            end
        endcase
        
        // 根据最终控制信号设置电机参数
        case (final_control)
            FORWARD_FULL: begin
                motor_dir <= MOTOR_FORWARD;     // 4'b1111 - 所有轮正转
                motor_speed <= SPEED_FULL;      // 255 - 全速前进
            end
            FORWARD_SLOW: begin
                motor_dir <= MOTOR_FORWARD;     // 4'b1111 - 所有轮正转
                motor_speed <= SPEED_MEDIUM;    // 150 - 中速前进
            end
            LEFT_SLOW: begin
                motor_dir <= MOTOR_LEFT;        // 4'b0011 - 左轮反转，右轮正转
                motor_speed <= SPEED_LOW;       // 100 - 缓左转
            end
            LEFT_FAST: begin
                motor_dir <= MOTOR_LEFT;        // 4'b0011 - 左轮反转，右轮正转
                motor_speed <= SPEED_HIGH;      // 200 - 急左转
            end
            RIGHT_SLOW: begin
                motor_dir <= MOTOR_RIGHT;       // 4'b1100 - 左轮正转，右轮反转
                motor_speed <= SPEED_LOW;       // 100 - 缓右转
            end
            RIGHT_FAST: begin
                motor_dir <= MOTOR_RIGHT;       // 4'b1100 - 左轮正转，右轮反转
                motor_speed <= SPEED_HIGH;      // 200 - 急右转
            end
            BACK_FULL: begin
                motor_dir <= MOTOR_BACK;        // 4'b0000 - 所有轮反转
                motor_speed <= SPEED_FULL;      // 255 - 全速倒车
            end
            STOP: begin
                motor_dir <= MOTOR_STOP;        // 4'b0000 - 所有轮停止
                motor_speed <= SPEED_ZERO;      // 0 - 停止
            end
            default: begin
                motor_dir <= MOTOR_STOP;
                motor_speed <= SPEED_ZERO;
            end
        endcase
    end
end

endmodule
