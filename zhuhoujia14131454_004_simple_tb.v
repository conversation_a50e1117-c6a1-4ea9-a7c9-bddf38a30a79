`timescale 1ns / 1ps

module zhuhoujia14131454_004_simple_tb;

// 测试信号定义
reg clk;
reg rst_n;
reg [3:0] motor_control;
reg [7:0] speed_control;
wire motor1_a, motor1_b;
wire motor2_a, motor2_b;
wire motor3_a, motor3_b;
wire motor4_a, motor4_b;
wire clk_1m;
wire clk_100hz;

// 实例化被测试模块
zhuhoujia14131454_004 uut (
    .clk(clk),
    .rst_n(rst_n),
    .motor_control(motor_control),
    .speed_control(speed_control),
    .motor1_a(motor1_a),
    .motor1_b(motor1_b),
    .motor2_a(motor2_a),
    .motor2_b(motor2_b),
    .motor3_a(motor3_a),
    .motor3_b(motor3_b),
    .motor4_a(motor4_a),
    .motor4_b(motor4_b),
    .clk_1m(clk_1m),
    .clk_100hz(clk_100hz)
);

// 时钟生成 - 50MHz
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// 电机控制命令解码任务
task decode_motor_command;
    input [3:0] command;
    begin
        case (command)
            4'd0: $display("电机控制: 停止");
            4'd1: $display("电机控制: 前进");
            4'd2: $display("电机控制: 后退");
            4'd3: $display("电机控制: 左转");
            4'd4: $display("电机控制: 右转");
            4'd5: $display("电机控制: 缓左转");
            4'd6: $display("电机控制: 缓右转");
            4'd7: $display("电机控制: 急左转");
            4'd8: $display("电机控制: 急右转");
            default: $display("电机控制: 未知命令");
        endcase
    end
endtask

// 电机状态解码任务
task decode_motor_state;
    input motor_a, motor_b;
    begin
        case ({motor_a, motor_b})
            2'b00: $display("停止");
            2'b01: $display("反转");
            2'b10: $display("正转");
            2'b11: $display("制动");
        endcase
    end
endtask

// 测试场景任务
task test_motor_scenario;
    input [3:0] command;
    input [7:0] speed;
    begin
        motor_control = command;
        speed_control = speed;
        #1000; // 等待多个PWM周期
        
        decode_motor_command(command);
        $display("速度控制值: %d", speed);
        $write("电机1(左前): ");
        decode_motor_state(motor1_a, motor1_b);
        $write("电机2(左后): ");
        decode_motor_state(motor2_a, motor2_b);
        $write("电机3(右前): ");
        decode_motor_state(motor3_a, motor3_b);
        $write("电机4(右后): ");
        decode_motor_state(motor4_a, motor4_b);
        $display("PWM计数器: %d", uut.pwm_counter);
        $display("");
    end
endtask

// 主测试序列
initial begin
    // 初始化
    rst_n = 0;
    motor_control = 4'd0;
    speed_control = 8'd0;
    
    $display("========================================");
    $display("电机驱动和时钟分频模块简化测试开始");
    $display("========================================");
    
    // 释放复位
    #100;
    rst_n = 1;
    #100;
    
    // 测试1: 基本运动模式
    $display("【测试组1: 基本运动模式】");
    $display("=== 停止状态 ===");
    test_motor_scenario(4'd0, 8'd0);
    $display("=== 前进 - 50%速度 ===");
    test_motor_scenario(4'd1, 8'd128);
    $display("=== 后退 - 50%速度 ===");
    test_motor_scenario(4'd2, 8'd128);
    $display("=== 左转 - 50%速度 ===");
    test_motor_scenario(4'd3, 8'd128);
    $display("=== 右转 - 50%速度 ===");
    test_motor_scenario(4'd4, 8'd128);
    
    // 测试2: 精细控制模式
    $display("【测试组2: 精细控制模式】");
    $display("=== 缓左转 - 差速控制 ===");
    test_motor_scenario(4'd5, 8'd160);
    $display("=== 缓右转 - 差速控制 ===");
    test_motor_scenario(4'd6, 8'd160);
    $display("=== 急左转 - 原地转向 ===");
    test_motor_scenario(4'd7, 8'd200);
    $display("=== 急右转 - 原地转向 ===");
    test_motor_scenario(4'd8, 8'd200);
    
    // 测试3: 不同速度测试
    $display("【测试组3: 不同速度测试】");
    $display("=== 前进 - 25%速度 ===");
    test_motor_scenario(4'd1, 8'd64);
    $display("=== 前进 - 75%速度 ===");
    test_motor_scenario(4'd1, 8'd192);
    $display("=== 前进 - 100%速度 ===");
    test_motor_scenario(4'd1, 8'd255);
    
    // 测试4: 时钟分频功能
    $display("【测试组4: 时钟分频功能测试】");
    $display("测试1MHz和100Hz时钟输出...");
    
    #10000; // 等待一段时间观察时钟
    $display("1MHz时钟状态: %b", clk_1m);
    $display("100Hz时钟状态: %b", clk_100hz);
    
    // 测试5: 复位功能测试
    $display("【测试组5: 复位功能测试】");
    motor_control = 4'd1;
    speed_control = 8'd200;
    #500;
    $display("复位前状态:");
    $display("电机1: %b%b, 电机2: %b%b, 电机3: %b%b, 电机4: %b%b", 
             motor1_a, motor1_b, motor2_a, motor2_b, motor3_a, motor3_b, motor4_a, motor4_b);
    
    rst_n = 0;
    #100;
    $display("复位中状态:");
    $display("电机1: %b%b, 电机2: %b%b, 电机3: %b%b, 电机4: %b%b", 
             motor1_a, motor1_b, motor2_a, motor2_b, motor3_a, motor3_b, motor4_a, motor4_b);
    
    rst_n = 1;
    #100;
    $display("复位后状态:");
    $display("电机1: %b%b, 电机2: %b%b, 电机3: %b%b, 电机4: %b%b", 
             motor1_a, motor1_b, motor2_a, motor2_b, motor3_a, motor3_b, motor4_a, motor4_b);
    
    $display("\n========================================");
    $display("电机驱动和时钟分频模块简化测试完成");
    $display("========================================");
    $finish;
end

// 监控电机状态变化
always @(motor1_a or motor1_b or motor2_a or motor2_b or motor3_a or motor3_b or motor4_a or motor4_b) begin
    if ($time > 200) begin
        $display("时间=%0t: 电机状态变化", $time);
    end
end

// 监控时钟输出
always @(clk_1m) begin
    if ($time > 200 && $time < 1000) begin
        $display("时间=%0t: 1MHz时钟切换到 %b", $time, clk_1m);
    end
end

always @(clk_100hz) begin
    if ($time > 200 && $time < 5000) begin
        $display("时间=%0t: 100Hz时钟切换到 %b", $time, clk_100hz);
    end
end

// 生成波形文件
initial begin
    $dumpfile("zhuhoujia14131454_004_simple_tb.vcd");
    $dumpvars(0, zhuhoujia14131454_004_simple_tb);
end

endmodule
