{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1752754362850 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Timing Analyzer Quartus Prime " "Running Quartus Prime Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752754362856 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 17 20:12:42 2025 " "Processing started: Thu Jul 17 20:12:42 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752754362856 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Timing Analyzer" 0 -1 1752754362856 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta 11 -c 11 " "Command: quartus_sta 11 -c 11" {  } {  } 0 0 "Command: %1!s!" 0 0 "Timing Analyzer" 0 -1 1752754362856 ""}
{ "Info" "0" "" "qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "Timing Analyzer" 0 0 1752754363171 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Timing Analyzer" 0 -1 1752754363771 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Timing Analyzer" 0 -1 1752754363771 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752754363820 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752754363820 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "11.sdc " "Synopsys Design Constraints File file not found: '11.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Timing Analyzer" 0 -1 1752754363958 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "base clocks \"derive_clocks -period 1.0\" " "No user constrained base clocks found in the design. Calling \"derive_clocks -period 1.0\"" {  } {  } 0 332142 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "Timing Analyzer" 0 -1 1752754363958 ""}
{ "Info" "ISTA_DERIVE_CLOCKS_INFO" "Deriving Clocks " "Deriving Clocks" { { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name zhuhoujia2023111413_004:pwm_gen\|pwm_clk zhuhoujia2023111413_004:pwm_gen\|pwm_clk " "create_clock -period 1.000 -name zhuhoujia2023111413_004:pwm_gen\|pwm_clk zhuhoujia2023111413_004:pwm_gen\|pwm_clk" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1752754363958 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name clk_50MHz clk_50MHz " "create_clock -period 1.000 -name clk_50MHz clk_50MHz" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1752754363958 ""}  } {  } 0 332105 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752754363958 ""}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 332143 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "Timing Analyzer" 0 -1 1752754363961 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752754363961 ""}
{ "Info" "0" "" "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" {  } {  } 0 0 "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" 0 0 "Timing Analyzer" 0 0 1752754363961 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "Timing Analyzer" 0 0 1752754363967 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1752754363982 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1752754363982 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -5.258 " "Worst-case setup slack is -5.258" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754363982 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754363982 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -5.258            -576.966 clk_50MHz  " "   -5.258            -576.966 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754363982 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.262             -41.998 zhuhoujia2023111413_004:pwm_gen\|pwm_clk  " "   -4.262             -41.998 zhuhoujia2023111413_004:pwm_gen\|pwm_clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754363982 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752754363982 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.435 " "Worst-case hold slack is 0.435" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754363985 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754363985 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.435               0.000 clk_50MHz  " "    0.435               0.000 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754363985 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.465               0.000 zhuhoujia2023111413_004:pwm_gen\|pwm_clk  " "    0.465               0.000 zhuhoujia2023111413_004:pwm_gen\|pwm_clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754363985 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752754363985 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1752754363988 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1752754363991 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754363992 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754363992 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000            -304.861 clk_50MHz  " "   -3.000            -304.861 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754363992 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.487             -23.792 zhuhoujia2023111413_004:pwm_gen\|pwm_clk  " "   -1.487             -23.792 zhuhoujia2023111413_004:pwm_gen\|pwm_clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754363992 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752754363992 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1752754364020 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Timing Analyzer" 0 -1 1752754364036 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Timing Analyzer" 0 -1 1752754364235 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752754364279 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1752754364286 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1752754364286 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -4.951 " "Worst-case setup slack is -4.951" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364289 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364289 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.951            -529.351 clk_50MHz  " "   -4.951            -529.351 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364289 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.961             -38.475 zhuhoujia2023111413_004:pwm_gen\|pwm_clk  " "   -3.961             -38.475 zhuhoujia2023111413_004:pwm_gen\|pwm_clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364289 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752754364289 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.384 " "Worst-case hold slack is 0.384" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364292 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364292 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.384               0.000 clk_50MHz  " "    0.384               0.000 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364292 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.417               0.000 zhuhoujia2023111413_004:pwm_gen\|pwm_clk  " "    0.417               0.000 zhuhoujia2023111413_004:pwm_gen\|pwm_clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364292 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752754364292 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1752754364296 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1752754364296 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364299 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364299 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000            -304.861 clk_50MHz  " "   -3.000            -304.861 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364299 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.487             -23.792 zhuhoujia2023111413_004:pwm_gen\|pwm_clk  " "   -1.487             -23.792 zhuhoujia2023111413_004:pwm_gen\|pwm_clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364299 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752754364299 ""}
{ "Info" "0" "" "Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1752754364329 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752754364413 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1752754364416 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1752754364416 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -1.679 " "Worst-case setup slack is -1.679" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364418 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364418 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.679            -142.303 clk_50MHz  " "   -1.679            -142.303 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364418 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.235              -9.422 zhuhoujia2023111413_004:pwm_gen\|pwm_clk  " "   -1.235              -9.422 zhuhoujia2023111413_004:pwm_gen\|pwm_clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364418 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752754364418 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.179 " "Worst-case hold slack is 0.179" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364420 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364420 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.179               0.000 clk_50MHz  " "    0.179               0.000 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364420 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.193               0.000 zhuhoujia2023111413_004:pwm_gen\|pwm_clk  " "    0.193               0.000 zhuhoujia2023111413_004:pwm_gen\|pwm_clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364420 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752754364420 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1752754364426 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1752754364426 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364429 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364429 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000            -219.562 clk_50MHz  " "   -3.000            -219.562 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364429 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.000             -16.000 zhuhoujia2023111413_004:pwm_gen\|pwm_clk  " "   -1.000             -16.000 zhuhoujia2023111413_004:pwm_gen\|pwm_clk " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752754364429 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752754364429 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Design is not fully constrained for setup requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1752754364725 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Design is not fully constrained for hold requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1752754364725 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Timing Analyzer 0 s 5 s Quartus Prime " "Quartus Prime Timing Analyzer was successful. 0 errors, 5 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4875 " "Peak virtual memory: 4875 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752754364778 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 17 20:12:44 2025 " "Processing ended: Thu Jul 17 20:12:44 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752754364778 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:02 " "Elapsed time: 00:00:02" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752754364778 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:02 " "Total CPU time (on all processors): 00:00:02" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752754364778 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Timing Analyzer" 0 -1 1752754364778 ""}
