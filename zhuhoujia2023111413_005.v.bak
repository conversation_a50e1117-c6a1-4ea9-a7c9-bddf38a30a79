module dht11_sensor_module(
    input clk,
    input rst_n,
    inout dht11_data,
    output reg [15:0] temperature,
    output reg [15:0] humidity,
    output reg data_valid
);

// DHT11时序参数
parameter WAIT_TIME = 20'd1000000;  // 1秒等待时间
parameter START_LOW = 16'd40000;    // 18ms低电平
parameter START_HIGH = 16'd1600;    // 30us高电平

reg [2:0] state;
reg [19:0] timer;
reg [5:0] bit_counter;
reg [39:0] received_data;
reg dht11_out;
reg dht11_oe;

assign dht11_data = dht11_oe ? dht11_out : 1'bz;

parameter IDLE = 3'd0;
parameter START = 3'd1;
parameter RESPONSE = 3'd2;
parameter RECEIVE = 3'd3;
parameter FINISH = 3'd4;

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        state <= IDLE;
        timer <= 20'd0;
        bit_counter <= 6'd0;
        received_data <= 40'd0;
        temperature <= 16'd0;
        humidity <= 16'd0;
        data_valid <= 1'b0;
        dht11_out <= 1'b1;
        dht11_oe <= 1'b0;
    end else begin
        case (state)
            IDLE: begin
                if (timer >= WAIT_TIME) begin
                    state <= START;
                    timer <= 20'd0;
                    dht11_oe <= 1'b1;
                    dht11_out <= 1'b0;
                end else begin
                    timer <= timer + 1;
                end
            end
            
            START: begin
                if (timer >= START_LOW) begin
                    dht11_out <= 1'b1;
                    dht11_oe <= 1'b0;
                    state <= RESPONSE;
                    timer <= 20'd0;
                end else begin
                    timer <= timer + 1;
                end
            end
            
            RESPONSE: begin
                if (!dht11_data && timer > 16'd100) begin
                    state <= RECEIVE;
                    timer <= 20'd0;
                    bit_counter <= 6'd0;
                end else begin
                    timer <= timer + 1;
                end
            end
            
            RECEIVE: begin
                if (bit_counter < 40) begin
                    // 简化的数据接收逻辑
                    if (timer > 16'd100) begin
                        received_data[39-bit_counter] <= dht11_data;
                        bit_counter <= bit_counter + 1;
                        timer <= 20'd0;
                    end else begin
                        timer <= timer + 1;
                    end
                end else begin
                    state <= FINISH;
                end
            end
            
            FINISH: begin
                // 解析接收到的数据
                humidity <= {received_data[39:32], received_data[31:24]};
                temperature <= {received_data[23:16], received_data[15:8]};
                data_valid <= 1'b1;
                state <= IDLE;
                timer <= 20'd0;
            end
        endcase
    end
end

endmodule
