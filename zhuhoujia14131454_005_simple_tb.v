`timescale 1ns / 1ps

module zhuhoujia14131454_005_simple_tb;

// 测试信号定义
reg clk;
reg rst_n;
wire dht11_data;
wire [15:0] temperature;
wire [15:0] humidity;
wire data_valid;

// DHT11数据模拟
reg dht11_data_reg;
reg dht11_oe_sim;
assign dht11_data = dht11_oe_sim ? dht11_data_reg : 1'bz;

// 实例化被测试模块
zhuhoujia14131454_005 uut (
    .clk(clk),
    .rst_n(rst_n),
    .dht11_data(dht11_data),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(data_valid)
);

// 时钟生成 - 50MHz
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// 状态解码任务
task decode_state;
    input [2:0] state;
    begin
        case (state)
            3'd0: $display("当前状态: IDLE (空闲)");
            3'd1: $display("当前状态: START (启动)");
            3'd2: $display("当前状态: RESPONSE (等待响应)");
            3'd3: $display("当前状态: RECEIVE (接收数据)");
            3'd4: $display("当前状态: FINISH (完成)");
            default: $display("当前状态: UNKNOWN (未知)");
        endcase
    end
endtask

// 简化的DHT11响应模拟
task simple_dht11_response;
    begin
        // 等待启动信号
        #20000;
        
        // 模拟DHT11响应
        dht11_oe_sim = 1;
        dht11_data_reg = 0; 
        #1600; // 80us低电平响应
        dht11_data_reg = 1; 
        #1600; // 80us高电平响应
        
        // 发送简化的数据位（只发送几位作为演示）
        // 发送湿度高字节 (例如: 0x3C = 60%)
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit7 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit6 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit5 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit4 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit3 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit2 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit1 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit0 = 0
        
        // 发送湿度低字节 (例如: 0x00)
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // 8个0位
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        
        // 发送温度高字节 (例如: 0x19 = 25°C)
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit7 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit6 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit5 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit4 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit3 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit2 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit1 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit0 = 1
        
        // 发送温度低字节 (例如: 0x05 = 0.5°C)
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit7 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit6 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit5 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit4 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit3 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit2 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit1 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit0 = 1
        
        // 发送校验和 (例如: 0x3C + 0x00 + 0x19 + 0x05 = 0x60)
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit7 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit6 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit5 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit4 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit3 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit2 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit1 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit0 = 0
        
        // 结束信号
        dht11_data_reg = 0; 
        #1000;
        dht11_oe_sim = 0;   // 释放总线
    end
endtask

// 主测试序列
initial begin
    // 初始化
    rst_n = 0;
    dht11_data_reg = 1;
    dht11_oe_sim = 0;
    
    $display("========================================");
    $display("DHT11温湿度传感器简化测试开始");
    $display("========================================");
    
    // 释放复位
    #100;
    rst_n = 1;
    #100;
    
    // 测试1: 基本功能测试
    $display("【测试1: 基本DHT11通信测试】");
    
    // 启动DHT11响应模拟
    simple_dht11_response();
    
    // 等待数据处理完成
    #10000;
    
    $display("DHT11读取结果:");
    $display("  温度: %d.%d°C (原始值: 0x%04h)", 
             temperature[15:8], temperature[7:0], temperature);
    $display("  湿度: %d.%d%%%% (原始值: 0x%04h)",
             humidity[15:8], humidity[7:0], humidity);
    $display("  数据有效标志: %b", data_valid);
    
    // 测试2: 复位功能测试
    $display("\n【测试2: 复位功能测试】");
    $display("复位前状态:");
    decode_state(uut.state);
    $display("温度: 0x%04h, 湿度: 0x%04h, 有效: %b", temperature, humidity, data_valid);
    
    rst_n = 0;
    #100;
    $display("复位后状态:");
    decode_state(uut.state);
    $display("温度: 0x%04h, 湿度: 0x%04h, 有效: %b", temperature, humidity, data_valid);
    
    rst_n = 1;
    #100;
    
    $display("\n========================================");
    $display("DHT11温湿度传感器简化测试完成");
    $display("========================================");
    $finish;
end

// 状态监控
always @(uut.state) begin
    if ($time > 200) begin
        $display("时间=%0t: DHT11状态变化", $time);
        decode_state(uut.state);
    end
end

// 数据有效标志监控
always @(data_valid) begin
    if ($time > 200) begin
        if (data_valid) begin
            $display("时间=%0t: 数据读取完成", $time);
        end else begin
            $display("时间=%0t: 开始新的测量周期", $time);
        end
    end
end

// 生成波形文件
initial begin
    $dumpfile("zhuhoujia14131454_005_simple_tb.vcd");
    $dumpvars(0, zhuhoujia14131454_005_simple_tb);
end

endmodule
