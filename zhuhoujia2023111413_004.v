module zhuhoujia2023111413_004(
    input wire clk,            // 50MHz系统时钟
    input wire rst_n,          // 复位信号
    input wire [3:0] motor_control, // 电机控制命令
    input wire [7:0] speed_control, // 速度控制值

    output reg [3:0] motor_pwm,  // 4路电机PWM输出
    output reg [3:0] motor_dir,  // 4路电机方向控制
    output reg clk_1m,           // 1MHz输出时钟
    output reg clk_100hz         // 100Hz输出时钟
);

    // PWM计数器
    reg [7:0] pwm_counter;

    // 分频器计数器
    reg [24:0] counter_1m;      // 50分频计数器
    reg [19:0] counter_100hz;   // 500000分频计数器

    // 电机定义
    localparam MOTOR_LEFT_FRONT = 2'd0,
               MOTOR_LEFT_REAR = 2'd1,
               MOTOR_RIGHT_FRONT = 2'd2,
               MOTOR_RIGHT_REAR = 2'd3;

    // PWM生成
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            pwm_counter <= 8'd0;
        end else begin
            pwm_counter <= pwm_counter + 1'b1;
        end
    end

    // 生成1MHz时钟
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            counter_1m <= 25'd0;
            clk_1m <= 1'b0;
        end else begin
            if (counter_1m >= 25'd24) begin
                counter_1m <= 25'd0;
                clk_1m <= ~clk_1m;
            end else begin
                counter_1m <= counter_1m + 1'b1;
            end
        end
    end

    // 生成100Hz时钟
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            counter_100hz <= 20'd0;
            clk_100hz <= 1'b0;
        end else begin
            if (counter_100hz >= 20'd249999) begin
                counter_100hz <= 20'd0;
                clk_100hz <= ~clk_100hz;
            end else begin
                counter_100hz <= counter_100hz + 1'b1;
            end
        end
    end

    // 电机控制逻辑
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            motor_pwm <= 4'd0;
            motor_dir <= 4'd0;
        end else begin
            case (motor_control)
                // 停止
                4'd0: begin
                    motor_pwm <= 4'd0;
                    motor_dir <= 4'b0000;
                end

                // 前进
                4'd1: begin
                    motor_dir <= 4'b0000; // 全部向前
                    motor_pwm[0] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[1] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[2] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[3] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                end

                // 后退
                4'd2: begin
                    motor_dir <= 4'b1111; // 全部向后
                    motor_pwm[0] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[1] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[2] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[3] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                end

                // 左转
                4'd3: begin
                    motor_dir <= 4'b1100; // 左后右前
                    motor_pwm[0] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[1] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[2] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[3] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                end

                // 右转
                4'd4: begin
                    motor_dir <= 4'b0011; // 右后左前
                    motor_pwm[0] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[1] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[2] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[3] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                end

                // 缓左转
                4'd5: begin
                    motor_dir <= 4'b0000; // 全部向前
                    // 左电机慢速，右电机快速
                    motor_pwm[0] <= (pwm_counter < (speed_control/2)) ? 1'b1 : 1'b0;
                    motor_pwm[1] <= (pwm_counter < (speed_control/2)) ? 1'b1 : 1'b0;
                    motor_pwm[2] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[3] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                end

                // 缓右转
                4'd6: begin
                    motor_dir <= 4'b0000; // 全部向前
                    // 右电机慢速，左电机快速
                    motor_pwm[0] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[1] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[2] <= (pwm_counter < (speed_control/2)) ? 1'b1 : 1'b0;
                    motor_pwm[3] <= (pwm_counter < (speed_control/2)) ? 1'b1 : 1'b0;
                end

                // 急左转
                4'd7: begin
                    motor_dir <= 4'b1001; // 左后右前
                    // 左电机全速后退，右电机全速前进
                    motor_pwm[0] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[1] <= (pwm_counter < (speed_control/2)) ? 1'b1 : 1'b0;
                    motor_pwm[2] <= (pwm_counter < (speed_control/2)) ? 1'b1 : 1'b0;
                    motor_pwm[3] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                end

                // 急右转
                4'd8: begin
                    motor_dir <= 4'b0110; // 右后左前
                    // 右电机全速后退，左电机全速前进
                    motor_pwm[0] <= (pwm_counter < (speed_control/2)) ? 1'b1 : 1'b0;
                    motor_pwm[1] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[2] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
                    motor_pwm[3] <= (pwm_counter < (speed_control/2)) ? 1'b1 : 1'b0;
                end

                default: begin
                    motor_pwm <= 4'd0;
                    motor_dir <= 4'b0000;
                end
            endcase
        end
    end

endmodule
