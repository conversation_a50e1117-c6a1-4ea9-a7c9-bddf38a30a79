module zhuhoujia2023111413_004(
    input clk,
    input rst_n,
    input [7:0] speed_control,
    input [3:0] motor_dir,        // 电机方向输入：[左前, 左后, 右前, 右后]
    output reg [3:0] motor_pwm    // PWM输出：[左前, 左后, 右前, 右后]
);

// PWM计数器 - 8位计数器提供256级PWM分辨率
reg [7:0] pwm_counter;

// PWM计数器更新
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        pwm_counter <= 8'd0;
    end else begin
        pwm_counter <= pwm_counter + 1;  // 自动溢出，形成256个时钟周期的PWM周期
    end
end

// 为每个电机生成独立的PWM信号
// 修复逻辑：只要速度控制值不为0，就应该产生PWM信号
// motor_dir控制电机旋转方向，不影响PWM生成
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        motor_pwm <= 4'b0000;
    end else begin
        // 左前轮PWM生成 - 只要有速度就产生PWM
        if (speed_control > 0 && (pwm_counter < speed_control)) begin
            motor_pwm[0] <= 1'b1;
        end else begin
            motor_pwm[0] <= 1'b0;
        end

        // 左后轮PWM生成
        if (speed_control > 0 && (pwm_counter < speed_control)) begin
            motor_pwm[1] <= 1'b1;
        end else begin
            motor_pwm[1] <= 1'b0;
        end

        // 右前轮PWM生成
        if (speed_control > 0 && (pwm_counter < speed_control)) begin
            motor_pwm[2] <= 1'b1;
        end else begin
            motor_pwm[2] <= 1'b0;
        end

        // 右后轮PWM生成
        if (speed_control > 0 && (pwm_counter < speed_control)) begin
            motor_pwm[3] <= 1'b1;
        end else begin
            motor_pwm[3] <= 1'b0;
        end
    end
end

endmodule
