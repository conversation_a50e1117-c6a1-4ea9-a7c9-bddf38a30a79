module zhuhoujia2023111413_004(
    input  wire clk,      // 系统时钟（如 50MHz，看 FPGA 核心板时钟引脚）
    input  wire rst_n,    // 复位信号（低电平有效，接核心板复位键）

    // 4 组电机方向控制信号（对应 L298N 的 IN1/IN2 等）
    output reg motor1_a, motor1_b,
    output reg motor2_a, motor2_b,
    output reg motor3_a, motor3_b,
    output reg motor4_a, motor4_b
);

// 上电直接输出正转逻辑：A 高、B 低（根据你的 L298N 接线调整方向）
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        // 复位时电机全停止（双低电平）
        motor1_a <= 1'b0; motor1_b <= 1'b0;
        motor2_a <= 1'b0; motor2_b <= 1'b0;
        motor3_a <= 1'b0; motor3_b <= 1'b0;
        motor4_a <= 1'b0; motor4_b <= 1'b0;
    end else begin
        // 正转逻辑：A 相高，B 相低（如果电机反转，就交换 1 和 0）
        motor1_a <= 1'b0; motor1_b <= 1'b1;
        motor2_a <= 1'b0; motor2_b <= 1'b1;
        motor3_a <= 1'b0; motor3_b <= 1'b1;
        motor4_a <= 1'b0; motor4_b <= 1'b1;
    end
end

endmodule
