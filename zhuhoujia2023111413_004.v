module zhuhoujia2023111413_004(
    input clk,                    // 50MHz主时钟
    input rst_n,
    input [7:0] speed_control,
    input [3:0] motor_dir,        // 电机方向输入：[左前, 左后, 右前, 右后]
    output reg [3:0] motor_pwm_a, // PWM输出A：[左前, 左后, 右前, 右后]
    output reg [3:0] motor_pwm_b  // PWM输出B：[左前, 左后, 右前, 右后]
);

// 时钟分频器 - 将50MHz分频到合适的PWM频率
// 分频系数：50MHz / 1024 ≈ 48.8kHz PWM频率
reg [9:0] clk_divider;
reg pwm_clk;

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        clk_divider <= 10'd0;
        pwm_clk <= 1'b0;
    end else begin
        if (clk_divider == 10'd511) begin  // 分频系数512，得到约97kHz PWM时钟
            clk_divider <= 10'd0;
            pwm_clk <= ~pwm_clk;
        end else begin
            clk_divider <= clk_divider + 1;
        end
    end
end

// PWM计数器 - 8位计数器提供256级PWM分辨率
reg [7:0] pwm_counter;

// PWM计数器更新 - 使用分频后的时钟
always @(posedge pwm_clk or negedge rst_n) begin
    if (!rst_n) begin
        pwm_counter <= 8'd0;
    end else begin
        pwm_counter <= pwm_counter + 1;  // 自动溢出，形成256个PWM时钟周期的PWM周期
    end
end

// 为每个电机生成双PWM信号（标准H桥驱动方式）
// motor_dir=1: A端PWM，B端低电平（正转）
// motor_dir=0: A端低电平，B端PWM（反转）
always @(posedge pwm_clk or negedge rst_n) begin
    if (!rst_n) begin
        motor_pwm_a <= 4'b0000;
        motor_pwm_b <= 4'b0000;
    end else begin
        // 左前轮PWM生成
        if (speed_control > 0 && (pwm_counter < speed_control)) begin
            if (motor_dir[0]) begin
                motor_pwm_a[0] <= 1'b1;  // 正转：A端PWM
                motor_pwm_b[0] <= 1'b0;  // B端低电平
            end else begin
                motor_pwm_a[0] <= 1'b0;  // 反转：A端低电平
                motor_pwm_b[0] <= 1'b1;  // B端PWM
            end
        end else begin
            motor_pwm_a[0] <= 1'b0;      // 停止：两端都低电平
            motor_pwm_b[0] <= 1'b0;
        end

        // 左后轮PWM生成
        if (speed_control > 0 && (pwm_counter < speed_control)) begin
            if (motor_dir[1]) begin
                motor_pwm_a[1] <= 1'b1;
                motor_pwm_b[1] <= 1'b0;
            end else begin
                motor_pwm_a[1] <= 1'b0;
                motor_pwm_b[1] <= 1'b1;
            end
        end else begin
            motor_pwm_a[1] <= 1'b0;
            motor_pwm_b[1] <= 1'b0;
        end

        // 右前轮PWM生成
        if (speed_control > 0 && (pwm_counter < speed_control)) begin
            if (motor_dir[2]) begin
                motor_pwm_a[2] <= 1'b1;
                motor_pwm_b[2] <= 1'b0;
            end else begin
                motor_pwm_a[2] <= 1'b0;
                motor_pwm_b[2] <= 1'b1;
            end
        end else begin
            motor_pwm_a[2] <= 1'b0;
            motor_pwm_b[2] <= 1'b0;
        end

        // 右后轮PWM生成
        if (speed_control > 0 && (pwm_counter < speed_control)) begin
            if (motor_dir[3]) begin
                motor_pwm_a[3] <= 1'b1;
                motor_pwm_b[3] <= 1'b0;
            end else begin
                motor_pwm_a[3] <= 1'b0;
                motor_pwm_b[3] <= 1'b1;
            end
        end else begin
            motor_pwm_a[3] <= 1'b0;
            motor_pwm_b[3] <= 1'b0;
        end
    end
end

endmodule
