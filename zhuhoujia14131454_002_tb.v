`timescale 1ns / 1ps

module zhuhoujia14131454_002_tb;

// 测试信号定义
reg clk;
reg rst_n;
reg [1:0] obstacle_sensor;
wire [2:0] control_signal;

// 实例化被测试模块
zhuhoujia14131454_002 uut (
    .clk(clk),
    .rst_n(rst_n),
    .obstacle_sensor(obstacle_sensor),
    .control_signal(control_signal)
);

// 时钟生成 - 50MHz
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// 控制信号解码任务
task decode_control_signal;
    input [2:0] signal;
    begin
        case (signal)
            3'b000: $display("控制信号: FORWARD_FULL (全速前进)");
            3'b001: $display("控制信号: FORWARD_SLOW (缓慢前进)");
            3'b010: $display("控制信号: LEFT_SLOW (缓左转)");
            3'b011: $display("控制信号: LEFT_FAST (急左转)");
            3'b100: $display("控制信号: RIGHT_SLOW (缓右转)");
            3'b101: $display("控制信号: RIGHT_FAST (急右转)");
            3'b110: $display("控制信号: BACK_FULL (全速倒车)");
            3'b111: $display("控制信号: STOP (停止)");
            default: $display("控制信号: UNKNOWN (未知)");
        endcase
    end
endtask

// 传感器状态解码任务
task decode_sensor_state;
    input [1:0] sensor;
    begin
        case (sensor)
            2'b00: $display("传感器状态: 无障碍");
            2'b01: $display("传感器状态: 右侧有障碍");
            2'b10: $display("传感器状态: 左侧有障碍");
            2'b11: $display("传感器状态: 前方有障碍");
            default: $display("传感器状态: 未知");
        endcase
    end
endtask

// 测试场景任务
task test_obstacle_scenario;
    input [1:0] sensor_input;
    begin
        obstacle_sensor = sensor_input;
        #100; // 等待信号稳定

        $display("传感器输入: %b", sensor_input);
        decode_sensor_state(sensor_input);
        decode_control_signal(control_signal);
        $display("输出编码: %b", control_signal);
        $display("");
    end
endtask

// 主测试序列
initial begin
    // 初始化
    rst_n = 0;
    obstacle_sensor = 2'b00;
    
    $display("========================================");
    $display("避障传感器模块测试开始");
    $display("传感器定义: [1:左侧, 0:右侧]");
    $display("========================================");
    
    // 释放复位
    #50;
    rst_n = 1;
    #50;
    
    // 测试所有避障场景
    $display("=== 无障碍场景 ===");
    test_obstacle_scenario(2'b00);
    $display("=== 右侧障碍场景 ===");
    test_obstacle_scenario(2'b01);
    $display("=== 左侧障碍场景 ===");
    test_obstacle_scenario(2'b10);
    $display("=== 前方障碍场景 ===");
    test_obstacle_scenario(2'b11);
    
    // 测试复位功能
    $display("=== 测试复位功能 ===");
    obstacle_sensor = 2'b11; // 设置为前方障碍
    #50;
    rst_n = 0; // 复位
    #50;
    $display("复位后控制信号: %b (应为000-FORWARD_FULL)", control_signal);
    rst_n = 1;
    #50;
    decode_control_signal(control_signal);
    
    // 测试动态避障过程
    $display("\n=== 测试动态避障过程 ===");
    $display("模拟小车遇到障碍物的避障过程:");
    
    obstacle_sensor = 2'b00; #200; // 无障碍，正常前进
    $display("步骤1: 无障碍前进 -> %b", control_signal);
    
    obstacle_sensor = 2'b01; #200; // 右侧出现障碍
    $display("步骤2: 右侧障碍，急左转 -> %b", control_signal);
    
    obstacle_sensor = 2'b00; #200; // 障碍消失
    $display("步骤3: 障碍消失，继续前进 -> %b", control_signal);
    
    obstacle_sensor = 2'b10; #200; // 左侧出现障碍
    $display("步骤4: 左侧障碍，急右转 -> %b", control_signal);
    
    obstacle_sensor = 2'b11; #200; // 前方出现障碍
    $display("步骤5: 前方障碍，全速后退 -> %b", control_signal);
    
    obstacle_sensor = 2'b00; #200; // 所有障碍消失
    $display("步骤6: 障碍全部消失，恢复前进 -> %b", control_signal);
    
    // 测试快速变化场景
    $display("\n=== 测试快速障碍变化 ===");
    $display("模拟快速变化的障碍环境:");
    
    repeat(10) begin
        obstacle_sensor = $random % 4; // 随机生成0-3的值
        #100;
        $display("随机障碍 %b -> 控制信号 %b", obstacle_sensor, control_signal);
    end
    
    // 测试边界条件
    $display("\n=== 测试边界条件 ===");
    
    // 测试时钟边沿
    obstacle_sensor = 2'b01;
    @(posedge clk);
    #1;
    $display("时钟上升沿后: %b", control_signal);
    
    @(negedge clk);
    #1;
    $display("时钟下降沿后: %b", control_signal);
    
    // 测试响应时间
    $display("\n=== 测试响应时间 ===");
    obstacle_sensor = 2'b00;
    #50;
    obstacle_sensor = 2'b11; // 突然出现前方障碍
    #10; // 等待半个时钟周期
    $display("半时钟周期后: %b (可能还未更新)", control_signal);
    #20; // 等待一个完整时钟周期
    $display("一个时钟周期后: %b (应已更新)", control_signal);
    
    $display("\n========================================");
    $display("避障传感器模块测试完成");
    $display("========================================");
    $finish;
end

// 监控控制信号变化
always @(control_signal) begin
    if ($time > 100) begin
        $display("时间=%0t: 避障控制信号变化为 %b", $time, control_signal);
    end
end

// 监控传感器输入变化
always @(obstacle_sensor) begin
    if ($time > 100) begin
        $display("时间=%0t: 传感器输入变化为 %b", $time, obstacle_sensor);
    end
end

// 生成波形文件
initial begin
    $dumpfile("zhuhoujia14131454_002_tb.vcd");
    $dumpvars(0, zhuhoujia14131454_002_tb);
end

endmodule
