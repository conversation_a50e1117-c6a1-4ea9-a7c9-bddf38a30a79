{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1752809589192 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Assembler Quartus Prime " "Running Quartus Prime Assembler" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752809589195 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Jul 18 11:33:09 2025 " "Processing started: Fri Jul 18 11:33:09 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752809589195 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Assembler" 0 -1 1752809589195 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_asm --read_settings_files=off --write_settings_files=off 11 -c 11 " "Command: quartus_asm --read_settings_files=off --write_settings_files=off 11 -c 11" {  } {  } 0 0 "Command: %1!s!" 0 0 "Assembler" 0 -1 1752809589195 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Assembler" 0 -1 1752809589548 ""}
{ "Info" "IASM_ASM_GENERATING_POWER_DATA" "" "Writing out detailed assembly data for power analysis" {  } {  } 0 115031 "Writing out detailed assembly data for power analysis" 0 0 "Assembler" 0 -1 1752809589741 ""}
{ "Info" "IASM_ASM_GENERATING_PROGRAMMING_FILES" "" "Assembler is generating device programming files" {  } {  } 0 115030 "Assembler is generating device programming files" 0 0 "Assembler" 0 -1 1752809589751 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Assembler 0 s 1  Quartus Prime " "Quartus Prime Assembler was successful. 0 errors, 1 warning" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4675 " "Peak virtual memory: 4675 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752809589869 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Jul 18 11:33:09 2025 " "Processing ended: Fri Jul 18 11:33:09 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752809589869 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:00 " "Elapsed time: 00:00:00" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752809589869 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752809589869 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Assembler" 0 -1 1752809589869 ""}
