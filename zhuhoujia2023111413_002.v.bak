module obstacle_avoidance_module(
    input clk,
    input rst_n,
    input [1:0] obstacle_sensor,
    output reg [1:0] control_signal
);

parameter FORWARD = 2'b00;
parameter LEFT    = 2'b01;
parameter RIGHT   = 2'b10;
parameter BACK    = 2'b11;

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        control_signal <= FORWARD;
    end else begin
        case (obstacle_sensor)
            2'b00: control_signal <= FORWARD;  // 无障碍
            2'b01: control_signal <= LEFT;     // 右侧有障碍，左转
            2'b10: control_signal <= RIGHT;    // 左侧有障碍，右转
            2'b11: control_signal <= BACK;     // 前方有障碍，后退
            default: control_signal <= FORWARD;
        endcase
    end
end

endmodule
