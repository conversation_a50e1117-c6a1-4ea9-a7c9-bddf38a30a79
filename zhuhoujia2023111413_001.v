module zhuhoujia2023111413_001(
    input clk,
    input rst_n,
    input [3:0] line_sensor,
    output reg [2:0] control_signal  // 扩展为3位支持精细控制
);

// 扩展的控制信号定义
parameter FORWARD_FULL  = 3'b000;  // 全速前进
parameter FORWARD_SLOW  = 3'b001;  // 缓慢前进  
parameter LEFT_SLOW     = 3'b010;  // 缓左转
parameter LEFT_FAST     = 3'b011;  // 急左转
parameter RIGHT_SLOW    = 3'b100;  // 缓右转
parameter RIGHT_FAST    = 3'b101;  // 急右转
parameter BACK_FULL     = 3'b110;  // 全速倒车
parameter STOP          = 3'b111;  // 停止

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        control_signal <= STOP;
    end else begin
        case (line_sensor)
            // 理想跟线状态
            4'b0110: control_signal <= FORWARD_FULL;   // 中间检测到线，全速前进
            
            // 轻微偏移 - 缓慢调整
            4'b1110: control_signal <= LEFT_SLOW;      // 轻微左偏，缓左转
            4'b0111: control_signal <= RIGHT_SLOW;     // 轻微右偏，缓右转
            
            // 中等偏移 - 中速调整
            4'b1100: control_signal <= LEFT_SLOW;      // 中等左偏，缓左转
            4'b0011: control_signal <= RIGHT_SLOW;     // 中等右偏，缓右转
            
            // 严重偏移 - 快速调整
            4'b1000: control_signal <= LEFT_FAST;      // 严重左偏，急左转
            4'b0001: control_signal <= RIGHT_FAST;     // 严重右偏，急右转
            
            // 极端偏移 - 急速调整
            4'b1001: control_signal <= LEFT_FAST;      // 极端左偏，急左转
            4'b0110: control_signal <= RIGHT_FAST;     // 极端右偏，急右转
            
            // 特殊情况
            4'b1111: control_signal <= STOP;           // 全部检测到（终点或交叉路口）
            4'b0000: control_signal <= FORWARD_SLOW;   // 丢失线路，缓慢前进搜索
            
            // 其他未定义状态
            default: control_signal <= STOP;
        endcase
    end
end

endmodule
