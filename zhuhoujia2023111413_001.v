module zhuhoujia2023111413_001(
    input clk,
    input rst_n,
    input [3:0] line_sensor,
    output reg [2:0] control_signal  // 扩展为3位支持精细控制
);

// 传感器滤波和防抖动
reg [3:0] sensor_reg1, sensor_reg2, sensor_reg3;
reg [3:0] sensor_filtered;
reg [15:0] debounce_counter;

// 传感器数据滤波（3级寄存器滤波）
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        sensor_reg1 <= 4'b0000;
        sensor_reg2 <= 4'b0000;
        sensor_reg3 <= 4'b0000;
        sensor_filtered <= 4'b0000;
        debounce_counter <= 16'd0;
    end else begin
        sensor_reg1 <= line_sensor;
        sensor_reg2 <= sensor_reg1;
        sensor_reg3 <= sensor_reg2;

        // 多数表决滤波
        if ((sensor_reg1 == sensor_reg2) && (sensor_reg2 == sensor_reg3)) begin
            if (debounce_counter >= 16'd1000) begin  // 防抖动延时
                sensor_filtered <= sensor_reg3;
                debounce_counter <= 16'd0;
            end else begin
                debounce_counter <= debounce_counter + 1;
            end
        end else begin
            debounce_counter <= 16'd0;
        end
    end
end

// 扩展的控制信号定义
parameter FORWARD_FULL  = 3'b000;  // 全速前进
parameter FORWARD_SLOW  = 3'b001;  // 缓慢前进  
parameter LEFT_SLOW     = 3'b010;  // 缓左转
parameter LEFT_FAST     = 3'b011;  // 急左转
parameter RIGHT_SLOW    = 3'b100;  // 缓右转
parameter RIGHT_FAST    = 3'b101;  // 急右转
parameter BACK_FULL     = 3'b110;  // 全速倒车
parameter STOP          = 3'b111;  // 停止

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        control_signal <= STOP;
    end else begin
        case (sensor_filtered)  // 使用滤波后的传感器数据
            // 理想跟线状态
            4'b0110: control_signal <= FORWARD_FULL;   // 中间检测到线，全速前进

            // 轻微偏移 - 缓慢调整
            4'b1110: control_signal <= LEFT_SLOW;      // 轻微左偏，缓左转
            4'b0111: control_signal <= RIGHT_SLOW;     // 轻微右偏，缓右转
            4'b1010: control_signal <= LEFT_SLOW;      // 左中传感器检测到，缓左转
            4'b0101: control_signal <= RIGHT_SLOW;     // 右中传感器检测到，缓右转

            // 中等偏移 - 中速调整
            4'b1100: control_signal <= LEFT_SLOW;      // 中等左偏，缓左转
            4'b0011: control_signal <= RIGHT_SLOW;     // 中等右偏，缓右转
            4'b0010: control_signal <= LEFT_SLOW;      // 左中传感器检测到
            4'b0100: control_signal <= RIGHT_SLOW;     // 右中传感器检测到

            // 严重偏移 - 快速调整
            4'b1000: control_signal <= LEFT_FAST;      // 严重左偏，急左转
            4'b0001: control_signal <= RIGHT_FAST;     // 严重右偏，急右转

            // 复杂情况处理
            4'b1001: control_signal <= FORWARD_SLOW;   // 两端传感器检测到，可能是宽线或交叉
            4'b1011: control_signal <= LEFT_SLOW;      // 左侧多个传感器，左转
            4'b1101: control_signal <= RIGHT_SLOW;     // 右侧多个传感器，右转

            // 特殊情况
            4'b1111: control_signal <= STOP;           // 全部检测到（终点或交叉路口）
            4'b0000: control_signal <= FORWARD_SLOW;   // 丢失线路，缓慢前进搜索

            // 其他未定义状态
            default: control_signal <= FORWARD_SLOW;   // 默认缓慢前进
        endcase
    end
end

endmodule
