`timescale 1ns / 1ps

module zhuhoujia2023111413_005_tb;

// 测试信号定义
reg clk;
reg rst_n;
wire dht11_data;
wire [15:0] temperature;
wire [15:0] humidity;
wire data_valid;

// DHT11数据模拟
reg dht11_data_reg;
reg dht11_oe_sim;
assign dht11_data = dht11_oe_sim ? dht11_data_reg : 1'bz;

// 实例化被测试模块 - DHT11传感器模块
zhuhoujia2023111413_005 uut (
    .clk(clk),
    .rst_n(rst_n),
    .dht11_data(dht11_data),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(data_valid)
);

// 时钟生成 - 50MHz
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// DHT11响应模拟任务
task simulate_dht11_response;
    input [39:0] test_data; // 40位测试数据
    integer i;
    begin
        // 等待主机发送启动信号
        wait(dht11_data == 1'b0);
        wait(dht11_data == 1'bz);
        
        // DHT11响应序列
        dht11_oe_sim = 1;
        dht11_data_reg = 0;
        #800; // 80us低电平响应
        
        dht11_data_reg = 1;
        #800; // 80us高电平响应
        
        // 发送40位数据
        for (i = 39; i >= 0; i = i - 1) begin
            dht11_data_reg = 0;
            #500; // 50us低电平
            
            dht11_data_reg = 1;
            if (test_data[i])
                #700; // 70us高电平表示'1'
            else
                #260; // 26us高电平表示'0'
        end
        
        dht11_data_reg = 0;
        #500;
        dht11_oe_sim = 0; // 释放总线
    end
endtask

// 测试序列
initial begin
    // 初始化
    rst_n = 0;
    dht11_data_reg = 1;
    dht11_oe_sim = 0;
    
    #1000;
    rst_n = 1;
    
    $display("开始DHT11传感器测试...");
    
    // 测试用例1：正常温湿度数据
    // 湿度：60% (0x3C), 温度：25°C (0x19)
    // 数据格式：湿度高字节+湿度低字节+温度高字节+温度低字节+校验和
    fork
        simulate_dht11_response(40'h3C001900_55); // 60%, 25°C, 校验和0x55
    join_none
    
    // 等待数据有效
    wait(data_valid);
    #100;
    $display("测试1 - 正常数据: 湿度=%d%%, 温度=%d°C, 有效=%b", 
             humidity[15:8], temperature[15:8], data_valid);
    
    // 等待下一次测量周期
    wait(!data_valid);
    #50000; // 等待足够时间进入下一个测量周期
    
    // 测试用例2：不同温湿度值
    fork
        simulate_dht11_response(40'h4B001E00_69); // 75%, 30°C
    join_none
    
    wait(data_valid);
    #100;
    $display("测试2 - 不同数据: 湿度=%d%%, 温度=%d°C, 有效=%b", 
             humidity[15:8], temperature[15:8], data_valid);
    
    $display("DHT11传感器模块测试完成！");
    $finish;
end

// 状态监控
always @(posedge clk) begin
    if (uut.state != uut.IDLE && $time > 2000)
        $display("时间=%0t, DHT11状态=%d", $time, uut.state);
end

initial begin
    $dumpfile("zhuhoujia2023111413_005_tb.vcd");
    $dumpvars(0, zhuhoujia2023111413_005_tb);
end

endmodule