{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1752752075278 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus Prime " "Running Quartus Prime Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752752075284 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 17 19:34:35 2025 " "Processing started: Thu Jul 17 19:34:35 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752752075284 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752752075284 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off 11 -c 11 " "Command: quartus_map --read_settings_files=on --write_settings_files=off 11 -c 11" {  } {  } 0 0 "Command: %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752752075284 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Analysis & Synthesis" 0 -1 1752752076430 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Analysis & Synthesis" 0 -1 1752752076433 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_001.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_001.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_001 " "Found entity 1: zhuhoujia2023111413_001" {  } { { "zhuhoujia2023111413_001.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752752083685 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752752083685 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_002.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_002.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_002 " "Found entity 1: zhuhoujia2023111413_002" {  } { { "zhuhoujia2023111413_002.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_002.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752752083688 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752752083688 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_003.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_003.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_003 " "Found entity 1: zhuhoujia2023111413_003" {  } { { "zhuhoujia2023111413_003.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_003.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752752083691 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752752083691 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_004.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_004.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_004 " "Found entity 1: zhuhoujia2023111413_004" {  } { { "zhuhoujia2023111413_004.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752752083694 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752752083694 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_005.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_005.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_005 " "Found entity 1: zhuhoujia2023111413_005" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752752083698 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752752083698 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_006.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_006.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_006 " "Found entity 1: zhuhoujia2023111413_006" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752752083701 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752752083701 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_007.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_007.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_007 " "Found entity 1: zhuhoujia2023111413_007" {  } { { "zhuhoujia2023111413_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752752083704 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752752083704 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "zhuhoujia2023111413_007 " "Elaborating entity \"zhuhoujia2023111413_007\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Analysis & Synthesis" 0 -1 1752752083766 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_001 zhuhoujia2023111413_001:line_follow " "Elaborating entity \"zhuhoujia2023111413_001\" for hierarchy \"zhuhoujia2023111413_001:line_follow\"" {  } { { "zhuhoujia2023111413_007.v" "line_follow" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 40 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752752083772 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_002 zhuhoujia2023111413_002:obstacle_avoid " "Elaborating entity \"zhuhoujia2023111413_002\" for hierarchy \"zhuhoujia2023111413_002:obstacle_avoid\"" {  } { { "zhuhoujia2023111413_007.v" "obstacle_avoid" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 47 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752752083778 ""}
{ "Warning" "WVRFX_L2_HDL_OBJECT_ASSIGNED_NOT_READ" "obstacle_output zhuhoujia2023111413_002.v(19) " "Verilog HDL or VHDL warning at zhuhoujia2023111413_002.v(19): object \"obstacle_output\" assigned a value but never read" {  } { { "zhuhoujia2023111413_002.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_002.v" 19 0 0 } }  } 0 10036 "Verilog HDL or VHDL warning at %2!s!: object \"%1!s!\" assigned a value but never read" 0 0 "Analysis & Synthesis" 0 -1 1752752083778 "|zhuhoujia2023111413_007|zhuhoujia2023111413_002:obstacle_avoid"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_003 zhuhoujia2023111413_003:motor_ctrl " "Elaborating entity \"zhuhoujia2023111413_003\" for hierarchy \"zhuhoujia2023111413_003:motor_ctrl\"" {  } { { "zhuhoujia2023111413_007.v" "motor_ctrl" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 57 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752752083781 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_004 zhuhoujia2023111413_004:pwm_gen " "Elaborating entity \"zhuhoujia2023111413_004\" for hierarchy \"zhuhoujia2023111413_004:pwm_gen\"" {  } { { "zhuhoujia2023111413_007.v" "pwm_gen" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 65 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752752083787 ""}
{ "Warning" "WVRFX_L2_HDL_OBJECT_ASSIGNED_NOT_READ" "duty_cycle zhuhoujia2023111413_004.v(10) " "Verilog HDL or VHDL warning at zhuhoujia2023111413_004.v(10): object \"duty_cycle\" assigned a value but never read" {  } { { "zhuhoujia2023111413_004.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v" 10 0 0 } }  } 0 10036 "Verilog HDL or VHDL warning at %2!s!: object \"%1!s!\" assigned a value but never read" 0 0 "Analysis & Synthesis" 0 -1 1752752083787 "|zhuhoujia2023111413_007|zhuhoujia2023111413_004:pwm_gen"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 8 zhuhoujia2023111413_004.v(20) " "Verilog HDL assignment warning at zhuhoujia2023111413_004.v(20): truncated value with size 32 to match size of target (8)" {  } { { "zhuhoujia2023111413_004.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v" 20 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752752083787 "|zhuhoujia2023111413_007|zhuhoujia2023111413_004:pwm_gen"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_005 zhuhoujia2023111413_005:dht11_sensor " "Elaborating entity \"zhuhoujia2023111413_005\" for hierarchy \"zhuhoujia2023111413_005:dht11_sensor\"" {  } { { "zhuhoujia2023111413_007.v" "dht11_sensor" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 74 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752752083793 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia2023111413_005.v(50) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(50): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 50 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752752083806 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia2023111413_005.v(61) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(61): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 61 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752752083806 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia2023111413_005.v(71) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(71): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 71 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752752083806 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 6 zhuhoujia2023111413_005.v(80) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(80): truncated value with size 32 to match size of target (6)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 80 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752752083806 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia2023111413_005.v(83) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(83): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 83 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752752083806 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_006 zhuhoujia2023111413_006:lcd_display " "Elaborating entity \"zhuhoujia2023111413_006\" for hierarchy \"zhuhoujia2023111413_006:lcd_display\"" {  } { { "zhuhoujia2023111413_007.v" "lcd_display" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 86 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752752083806 ""}
{ "Warning" "WVRFX_L2_HDL_OBJECT_ASSIGNED_NOT_READ" "char_counter zhuhoujia2023111413_006.v(15) " "Verilog HDL or VHDL warning at zhuhoujia2023111413_006.v(15): object \"char_counter\" assigned a value but never read" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 15 0 0 } }  } 0 10036 "Verilog HDL or VHDL warning at %2!s!: object \"%1!s!\" assigned a value but never read" 0 0 "Analysis & Synthesis" 0 -1 1752752083816 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia2023111413_006.v(40) " "Verilog HDL assignment warning at zhuhoujia2023111413_006.v(40): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 40 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752752083816 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia2023111413_006.v(50) " "Verilog HDL assignment warning at zhuhoujia2023111413_006.v(50): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 50 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752752083816 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "lcd_rw zhuhoujia2023111413_006.v(23) " "Verilog HDL Always Construct warning at zhuhoujia2023111413_006.v(23): inferring latch(es) for variable \"lcd_rw\", which holds its previous value in one or more paths through the always construct" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 23 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752752083816 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "lcd_rw zhuhoujia2023111413_006.v(23) " "Inferred latch for \"lcd_rw\" at zhuhoujia2023111413_006.v(23)" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 23 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752752083816 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Info" "IMLS_MLS_PRESET_POWER_UP" "" "Registers with preset signals will power-up high" {  } { { "zhuhoujia2023111413_003.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_003.v" 71 -1 0 } } { "zhuhoujia2023111413_001.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v" 22 -1 0 } } { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 19 -1 0 } }  } 0 13000 "Registers with preset signals will power-up high" 0 0 "Analysis & Synthesis" 0 -1 1752752084283 ""}
{ "Info" "IMLS_MLS_DEV_CLRN_SETS_REGISTERS" "" "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" {  } {  } 0 13003 "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" 0 0 "Analysis & Synthesis" 0 -1 1752752084283 ""}
{ "Warning" "WMLS_MLS_STUCK_PIN_HDR" "" "Output pins are stuck at VCC or GND" { { "Warning" "WMLS_MLS_STUCK_PIN" "lcd_rw GND " "Pin \"lcd_rw\" is stuck at GND" {  } { { "zhuhoujia2023111413_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 20 -1 0 } }  } 0 13410 "Pin \"%1!s!\" is stuck at %2!s!" 0 0 "Design Software" 0 -1 1752752084358 "|zhuhoujia2023111413_007|lcd_rw"}  } {  } 0 13024 "Output pins are stuck at VCC or GND" 0 0 "Analysis & Synthesis" 0 -1 1752752084358 ""}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Timing-Driven Synthesis is running" {  } {  } 0 286030 "Timing-Driven Synthesis is running" 0 0 "Analysis & Synthesis" 0 -1 1752752084422 ""}
{ "Info" "ISCL_SCL_LOST_FANOUT_MSG_HDR" "5 " "5 registers lost all their fanouts during netlist optimizations." {  } {  } 0 17049 "%1!d! registers lost all their fanouts during netlist optimizations." 0 0 "Analysis & Synthesis" 0 -1 1752752084852 ""}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "0 0 0 0 0 " "Adding 0 node(s), including 0 DDIO, 0 PLL, 0 transceiver and 0 LCELL" {  } {  } 0 16011 "Adding %1!d! node(s), including %2!d! DDIO, %3!d! PLL, %4!d! transceiver and %5!d! LCELL" 0 0 "Design Software" 0 -1 1752752085069 ""}  } {  } 0 16010 "Generating hard_block partition \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752752085069 ""}
{ "Info" "ICUT_CUT_TM_SUMMARY" "230 " "Implemented 230 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "8 " "Implemented 8 input pins" {  } {  } 0 21058 "Implemented %1!d! input pins" 0 0 "Design Software" 0 -1 1752752085209 ""} { "Info" "ICUT_CUT_TM_OPINS" "19 " "Implemented 19 output pins" {  } {  } 0 21059 "Implemented %1!d! output pins" 0 0 "Design Software" 0 -1 1752752085209 ""} { "Info" "ICUT_CUT_TM_BIDIRS" "1 " "Implemented 1 bidirectional pins" {  } {  } 0 21060 "Implemented %1!d! bidirectional pins" 0 0 "Design Software" 0 -1 1752752085209 ""} { "Info" "ICUT_CUT_TM_LCELLS" "202 " "Implemented 202 logic cells" {  } {  } 0 21061 "Implemented %1!d! logic cells" 0 0 "Design Software" 0 -1 1752752085209 ""}  } {  } 0 21057 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "Analysis & Synthesis" 0 -1 1752752085209 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 15 s Quartus Prime " "Quartus Prime Analysis & Synthesis was successful. 0 errors, 15 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4832 " "Peak virtual memory: 4832 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752752085221 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 17 19:34:45 2025 " "Processing ended: Thu Jul 17 19:34:45 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752752085221 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:10 " "Elapsed time: 00:00:10" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752752085221 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:17 " "Total CPU time (on all processors): 00:00:17" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752752085221 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1752752085221 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Analysis & Synthesis" 0 -1 1752752087229 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Fitter Quartus Prime " "Running Quartus Prime Fitter" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752752087232 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 17 19:34:46 2025 " "Processing started: Thu Jul 17 19:34:46 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752752087232 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Fitter" 0 -1 1752752087232 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_fit --read_settings_files=off --write_settings_files=off 11 -c 11 " "Command: quartus_fit --read_settings_files=off --write_settings_files=off 11 -c 11" {  } {  } 0 0 "Command: %1!s!" 0 0 "Fitter" 0 -1 1752752087232 ""}
{ "Info" "0" "" "qfit2_default_script.tcl version: #1" {  } {  } 0 0 "qfit2_default_script.tcl version: #1" 0 0 "Fitter" 0 0 1752752088755 ""}
{ "Info" "0" "" "Project  = 11" {  } {  } 0 0 "Project  = 11" 0 0 "Fitter" 0 0 1752752088758 ""}
{ "Info" "0" "" "Revision = 11" {  } {  } 0 0 "Revision = 11" 0 0 "Fitter" 0 0 1752752088758 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Fitter" 0 -1 1752752088877 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Fitter" 0 -1 1752752088877 ""}
{ "Info" "IMPP_MPP_USER_DEVICE" "11 EP4CE6E22C8 " "Selected device EP4CE6E22C8 for design \"11\"" {  } {  } 0 119006 "Selected device %2!s! for design \"%1!s!\"" 0 0 "Fitter" 0 -1 1752752088893 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1752752089037 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1752752089037 ""}
{ "Info" "IFITCC_FITCC_INFO_AUTO_FIT_COMPILATION_ON" "" "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" {  } {  } 0 171003 "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" 0 0 "Fitter" 0 -1 1752752089306 ""}
{ "Warning" "WCPT_FEATURE_DISABLED_POST" "LogicLock " "Feature LogicLock is only available with a valid subscription license. You can purchase a software subscription to gain full access to this feature." {  } {  } 0 292013 "Feature %1!s! is only available with a valid subscription license. You can purchase a software subscription to gain full access to this feature." 0 0 "Fitter" 0 -1 1752752089328 ""}
{ "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED" "" "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" { { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE10E22C8 " "Device EP4CE10E22C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1752752090178 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE15E22C8 " "Device EP4CE15E22C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1752752090178 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE22E22C8 " "Device EP4CE22E22C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1752752090178 ""}  } {  } 2 176444 "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" 0 0 "Fitter" 0 -1 1752752090178 ""}
{ "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION" "5 " "Fitter converted 5 user pins into dedicated programming pins" { { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_ASDO_DATA1~ 6 " "Pin ~ALTERA_ASDO_DATA1~ is reserved at location 6" {  } { { "c:/intelfpga_lite/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/intelfpga_lite/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_ASDO_DATA1~ } } } { "temporary_test_loc" "" { Generic "E:/zhuhoujia2023111413/" { { 0 { 0 ""} 0 488 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1752752090181 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_FLASH_nCE_nCSO~ 8 " "Pin ~ALTERA_FLASH_nCE_nCSO~ is reserved at location 8" {  } { { "c:/intelfpga_lite/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/intelfpga_lite/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_FLASH_nCE_nCSO~ } } } { "temporary_test_loc" "" { Generic "E:/zhuhoujia2023111413/" { { 0 { 0 ""} 0 490 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1752752090181 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DCLK~ 12 " "Pin ~ALTERA_DCLK~ is reserved at location 12" {  } { { "c:/intelfpga_lite/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/intelfpga_lite/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DCLK~ } } } { "temporary_test_loc" "" { Generic "E:/zhuhoujia2023111413/" { { 0 { 0 ""} 0 492 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1752752090181 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DATA0~ 13 " "Pin ~ALTERA_DATA0~ is reserved at location 13" {  } { { "c:/intelfpga_lite/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/intelfpga_lite/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DATA0~ } } } { "temporary_test_loc" "" { Generic "E:/zhuhoujia2023111413/" { { 0 { 0 ""} 0 494 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1752752090181 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_nCEO~ 101 " "Pin ~ALTERA_nCEO~ is reserved at location 101" {  } { { "c:/intelfpga_lite/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "c:/intelfpga_lite/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_nCEO~ } } } { "temporary_test_loc" "" { Generic "E:/zhuhoujia2023111413/" { { 0 { 0 ""} 0 496 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1752752090181 ""}  } {  } 0 169124 "Fitter converted %1!d! user pins into dedicated programming pins" 0 0 "Fitter" 0 -1 1752752090181 ""}
{ "Warning" "WCUT_CUT_ATOM_PINS_WITH_INCOMPLETE_IO_ASSIGNMENTS" "" "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" {  } {  } 0 15714 "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" 0 0 "Fitter" 0 -1 1752752090184 ""}
{ "Critical Warning" "WFIOMGR_PINS_MISSING_LOCATION_INFO" "28 28 " "No exact pin location assignment(s) for 28 pins of 28 total pins. For the list of pins please refer to the I/O Assignment Warnings table in the fitter report." {  } {  } 1 169085 "No exact pin location assignment(s) for %1!d! pins of %2!d! total pins. For the list of pins please refer to the I/O Assignment Warnings table in the fitter report." 0 0 "Fitter" 0 -1 1752752090455 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "11.sdc " "Synopsys Design Constraints File file not found: '11.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Fitter" 0 -1 1752752090668 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_NO_DERIVING_MSG" "base clocks " "No user constrained base clocks found in the design" {  } {  } 0 332144 "No user constrained %1!s! found in the design" 0 0 "Fitter" 0 -1 1752752090668 ""}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 332143 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "Fitter" 0 -1 1752752090670 ""}
{ "Info" "ISTA_NO_UNCERTAINTY_FOUND" "" "The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." {  } {  } 0 332154 "The derive_clock_uncertainty command did not apply clock uncertainty to any clock-to-clock transfers." 0 0 "Fitter" 0 -1 1752752090670 ""}
{ "Info" "ISTA_TDC_NO_DEFAULT_OPTIMIZATION_GOALS" "" "Timing requirements not specified -- quality metrics such as performance may be sacrificed to reduce compilation time." {  } {  } 0 332130 "Timing requirements not specified -- quality metrics such as performance may be sacrificed to reduce compilation time." 0 0 "Fitter" 0 -1 1752752090670 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "clk_50MHz~input (placed in PIN 23 (CLK1, DIFFCLK_0n)) " "Automatically promoted node clk_50MHz~input (placed in PIN 23 (CLK1, DIFFCLK_0n))" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G2 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G2" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1752752090695 ""}  } { { "zhuhoujia2023111413_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 2 0 0 } } { "temporary_test_loc" "" { Generic "E:/zhuhoujia2023111413/" { { 0 { 0 ""} 0 476 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1752752090695 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "rst_n~input (placed in PIN 24 (CLK2, DIFFCLK_1p)) " "Automatically promoted node rst_n~input (placed in PIN 24 (CLK2, DIFFCLK_1p))" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G4 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G4" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1752752090695 ""}  } { { "zhuhoujia2023111413_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 3 0 0 } } { "temporary_test_loc" "" { Generic "E:/zhuhoujia2023111413/" { { 0 { 0 ""} 0 477 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1752752090695 ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_START_REGPACKING_INFO" "" "Starting register packing" {  } {  } 0 176233 "Starting register packing" 0 0 "Fitter" 0 -1 1752752090887 ""}
{ "Extra Info" "IFSAC_FSAC_START_REG_LOCATION_PROCESSING" "" "Performing register packing on registers with non-logic cell location assignments" {  } {  } 1 176273 "Performing register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1752752090887 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_REG_LOCATION_PROCESSING" "" "Completed register packing on registers with non-logic cell location assignments" {  } {  } 1 176274 "Completed register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1752752090887 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_BEGIN_FAST_REGISTER_INFO" "" "Started Fast Input/Output/OE register processing" {  } {  } 1 176236 "Started Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1752752090890 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_FAST_REGISTER_INFO" "" "Finished Fast Input/Output/OE register processing" {  } {  } 1 176237 "Finished Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1752752090890 ""}
{ "Extra Info" "IFSAC_FSAC_START_MAC_SCAN_CHAIN_INFERENCING" "" "Start inferring scan chains for DSP blocks" {  } {  } 1 176238 "Start inferring scan chains for DSP blocks" 1 0 "Fitter" 0 -1 1752752090890 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_MAC_SCAN_CHAIN_INFERENCING" "" "Inferring scan chains for DSP blocks is complete" {  } {  } 1 176239 "Inferring scan chains for DSP blocks is complete" 1 0 "Fitter" 0 -1 1752752090890 ""}
{ "Extra Info" "IFSAC_FSAC_START_IO_MULT_RAM_PACKING" "" "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" {  } {  } 1 176248 "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" 1 0 "Fitter" 0 -1 1752752090890 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_IO_MULT_RAM_PACKING" "" "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" {  } {  } 1 176249 "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" 1 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_REGPACKING_INFO" "" "Finished register packing" { { "Extra Info" "IFSAC_NO_REGISTERS_WERE_PACKED" "" "No registers were packed into other blocks" {  } {  } 1 176219 "No registers were packed into other blocks" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 176235 "Finished register packing" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFSAC_FSAC_IO_BANK_PIN_GROUP_STATISTICS" "I/O pins that need to be placed that use the same VCCIO and VREF, before I/O pin placement " "Statistics of I/O pins that need to be placed that use the same VCCIO and VREF, before I/O pin placement" { { "Info" "IFSAC_FSAC_SINGLE_IOC_GROUP_STATISTICS" "26 unused 2.5V 6 19 1 " "Number of I/O pins in group: 26 (unused VREF, 2.5V VCCIO, 6 input, 19 output, 1 bidirectional)" { { "Info" "IFSAC_FSAC_IO_STDS_IN_IOC_GROUP" "2.5 V. " "I/O standards used: 2.5 V." {  } {  } 0 176212 "I/O standards used: %1!s!" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 176211 "Number of I/O pins in group: %1!d! (%2!s! VREF, %3!s! VCCIO, %4!d! input, %5!d! output, %6!d! bidirectional)" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 176214 "Statistics of %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFSAC_FSAC_IO_STATS_BEFORE_AFTER_PLACEMENT" "before " "I/O bank details before I/O pin placement" { { "Info" "IFSAC_FSAC_IO_BANK_PIN_GROUP_STATISTICS" "I/O banks " "Statistics of I/O banks" { { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "1 does not use undetermined 5 6 " "I/O bank number 1 does not use VREF pins and has undetermined VCCIO pins. 5 total pin(s) used --  6 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "2 does not use undetermined 1 7 " "I/O bank number 2 does not use VREF pins and has undetermined VCCIO pins. 1 total pin(s) used --  7 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "3 does not use undetermined 0 11 " "I/O bank number 3 does not use VREF pins and has undetermined VCCIO pins. 0 total pin(s) used --  11 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "4 does not use undetermined 0 14 " "I/O bank number 4 does not use VREF pins and has undetermined VCCIO pins. 0 total pin(s) used --  14 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "5 does not use undetermined 0 13 " "I/O bank number 5 does not use VREF pins and has undetermined VCCIO pins. 0 total pin(s) used --  13 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "6 does not use undetermined 1 9 " "I/O bank number 6 does not use VREF pins and has undetermined VCCIO pins. 1 total pin(s) used --  9 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "7 does not use undetermined 0 13 " "I/O bank number 7 does not use VREF pins and has undetermined VCCIO pins. 0 total pin(s) used --  13 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "8 does not use undetermined 0 12 " "I/O bank number 8 does not use VREF pins and has undetermined VCCIO pins. 0 total pin(s) used --  12 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 176214 "Statistics of %1!s!" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 176215 "I/O bank details %1!s! I/O pin placement" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITCC_FITTER_PREPARATION_END" "00:00:00 " "Fitter preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 171121 "Fitter preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IVPR20K_VPR_FAMILY_APL_ERROR" "" "Fitter has disabled Advanced Physical Optimization because it is not supported for the current family." {  } {  } 0 14896 "Fitter has disabled Advanced Physical Optimization because it is not supported for the current family." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_START" "" "Fitter placement preparation operations beginning" {  } {  } 0 170189 "Fitter placement preparation operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_END" "00:00:00 " "Fitter placement preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 170190 "Fitter placement preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1752752091389 ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_START" "" "Fitter placement operations beginning" {  } {  } 0 170191 "Fitter placement operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_INFO_VPR_PLACEMENT_FINISH" "" "Fitter placement was successful" {  } {  } 0 170137 "Fitter placement was successful" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_END" "00:00:01 " "Fitter placement operations ending: elapsed time is 00:00:01" {  } {  } 0 170192 "Fitter placement operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_START" "" "Fitter routing operations beginning" {  } {  } 0 170193 "Fitter routing operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_PERCENT_ROUTING_RESOURCE_USAGE" "0 " "Router estimated average interconnect usage is 0% of the available device resources" { { "Info" "IFITAPI_FITAPI_VPR_PEAK_ROUTING_REGION" "0 X23_Y12 X34_Y24 " "Router estimated peak interconnect usage is 0% of the available device resources in the region that extends from location X23_Y12 to location X34_Y24" {  } { { "loc" "" { Generic "E:/zhuhoujia2023111413/" { { 1 { 0 "Router estimated peak interconnect usage is 0% of the available device resources in the region that extends from location X23_Y12 to location X34_Y24"} { { 12 { 0 ""} 23 12 12 13 }  }  }  }  } }  } 0 170196 "Router estimated peak interconnect usage is %1!d!%% of the available device resources in the region that extends from location %2!s! to location %3!s!" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 170195 "Router estimated average interconnect usage is %1!d!%% of the available device resources" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED" "" "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." { { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_ROUTABILITY" "" "Optimizations that may affect the design's routability were skipped" {  } {  } 0 170201 "Optimizations that may affect the design's routability were skipped" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 170199 "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_END" "00:00:00 " "Fitter routing operations ending: elapsed time is 00:00:00" {  } {  } 0 170194 "Fitter routing operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IVPR20K_VPR_TIMING_ANALYSIS_TIME" "the Fitter 0.15 " "Total time spent on timing analysis during the Fitter is 0.15 seconds." {  } {  } 0 11888 "Total time spent on timing analysis during %1!s! is %2!s! seconds." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 1752752093206 ""}
{ "Info" "IFITCC_FITTER_POST_OPERATION_END" "00:00:01 " "Fitter post-fit operations ending: elapsed time is 00:00:01" {  } {  } 0 11218 "Fitter post-fit operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1752752093623 ""}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "E:/zhuhoujia2023111413/output_files/11.fit.smsg " "Generated suppressed messages file E:/zhuhoujia2023111413/output_files/11.fit.smsg" {  } {  } 0 144001 "Generated suppressed messages file %1!s!" 0 0 "Fitter" 0 -1 1752752093922 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Fitter 0 s 5 s Quartus Prime " "Quartus Prime Fitter was successful. 0 errors, 5 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "6282 " "Peak virtual memory: 6282 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752752094202 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 17 19:34:54 2025 " "Processing ended: Thu Jul 17 19:34:54 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752752094202 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:08 " "Elapsed time: 00:00:08" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752752094202 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:09 " "Total CPU time (on all processors): 00:00:09" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752752094202 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Fitter" 0 -1 1752752094202 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Fitter" 0 -1 1752752095621 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Assembler Quartus Prime " "Running Quartus Prime Assembler" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752752095627 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 17 19:34:55 2025 " "Processing started: Thu Jul 17 19:34:55 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752752095627 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Assembler" 0 -1 1752752095627 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_asm --read_settings_files=off --write_settings_files=off 11 -c 11 " "Command: quartus_asm --read_settings_files=off --write_settings_files=off 11 -c 11" {  } {  } 0 0 "Command: %1!s!" 0 0 "Assembler" 0 -1 1752752095627 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Assembler" 0 -1 1752752096098 ""}
{ "Info" "IASM_ASM_GENERATING_POWER_DATA" "" "Writing out detailed assembly data for power analysis" {  } {  } 0 115031 "Writing out detailed assembly data for power analysis" 0 0 "Assembler" 0 -1 1752752096346 ""}
{ "Info" "IASM_ASM_GENERATING_PROGRAMMING_FILES" "" "Assembler is generating device programming files" {  } {  } 0 115030 "Assembler is generating device programming files" 0 0 "Assembler" 0 -1 1752752096359 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Assembler 0 s 1  Quartus Prime " "Quartus Prime Assembler was successful. 0 errors, 1 warning" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4674 " "Peak virtual memory: 4674 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752752096524 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 17 19:34:56 2025 " "Processing ended: Thu Jul 17 19:34:56 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752752096524 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752752096524 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752752096524 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Assembler" 0 -1 1752752096524 ""}
{ "Info" "IFLOW_DISABLED_MODULE" "Power Analyzer FLOW_ENABLE_POWER_ANALYZER " "Skipped module Power Analyzer due to the assignment FLOW_ENABLE_POWER_ANALYZER" {  } {  } 0 293026 "Skipped module %1!s! due to the assignment %2!s!" 0 0 "Assembler" 0 -1 1752752097190 ""}
{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Assembler" 0 -1 1752752098259 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Timing Analyzer Quartus Prime " "Running Quartus Prime Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752752098265 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 17 19:34:57 2025 " "Processing started: Thu Jul 17 19:34:57 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752752098265 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Timing Analyzer" 0 -1 1752752098265 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta 11 -c 11 " "Command: quartus_sta 11 -c 11" {  } {  } 0 0 "Command: %1!s!" 0 0 "Timing Analyzer" 0 -1 1752752098265 ""}
{ "Info" "0" "" "qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "Timing Analyzer" 0 0 1752752098607 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Timing Analyzer" 0 -1 1752752099238 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Timing Analyzer" 0 -1 1752752099238 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752752099288 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752752099288 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "11.sdc " "Synopsys Design Constraints File file not found: '11.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Timing Analyzer" 0 -1 1752752099422 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "base clocks \"derive_clocks -period 1.0\" " "No user constrained base clocks found in the design. Calling \"derive_clocks -period 1.0\"" {  } {  } 0 332142 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "Timing Analyzer" 0 -1 1752752099422 ""}
{ "Info" "ISTA_DERIVE_CLOCKS_INFO" "Deriving Clocks " "Deriving Clocks" { { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name clk_50MHz clk_50MHz " "create_clock -period 1.000 -name clk_50MHz clk_50MHz" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1752752099422 ""}  } {  } 0 332105 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752752099422 ""}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 332143 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "Timing Analyzer" 0 -1 1752752099424 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752752099424 ""}
{ "Info" "0" "" "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" {  } {  } 0 0 "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" 0 0 "Timing Analyzer" 0 0 1752752099425 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "Timing Analyzer" 0 0 1752752099435 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1752752099447 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1752752099447 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -4.320 " "Worst-case setup slack is -4.320" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099447 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099447 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.320            -323.318 clk_50MHz  " "   -4.320            -323.318 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099447 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752752099447 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.433 " "Worst-case hold slack is 0.433" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099450 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099450 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.433               0.000 clk_50MHz  " "    0.433               0.000 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099450 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752752099450 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1752752099454 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1752752099455 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099457 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099457 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000            -199.284 clk_50MHz  " "   -3.000            -199.284 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099457 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752752099457 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1752752099485 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Timing Analyzer" 0 -1 1752752099506 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Timing Analyzer" 0 -1 1752752099740 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752752099778 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1752752099781 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1752752099781 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -4.075 " "Worst-case setup slack is -4.075" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099784 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099784 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.075            -297.481 clk_50MHz  " "   -4.075            -297.481 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099784 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752752099784 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.383 " "Worst-case hold slack is 0.383" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099784 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099784 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.383               0.000 clk_50MHz  " "    0.383               0.000 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099784 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752752099784 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1752752099788 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1752752099791 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099791 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099791 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000            -199.284 clk_50MHz  " "   -3.000            -199.284 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099791 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752752099791 ""}
{ "Info" "0" "" "Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1752752099813 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1752752099897 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1752752099900 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1752752099900 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -1.265 " "Worst-case setup slack is -1.265" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099903 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099903 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.265             -74.622 clk_50MHz  " "   -1.265             -74.622 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099903 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752752099903 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold 0.178 " "Worst-case hold slack is 0.178" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099906 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099906 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.178               0.000 clk_50MHz  " "    0.178               0.000 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099906 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752752099906 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Recovery " "No Recovery paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1752752099910 ""}
{ "Info" "ISTA_NO_PATHS_TO_REPORT" "Removal " "No Removal paths to report" {  } {  } 0 332140 "No %1!s! paths to report" 0 0 "Timing Analyzer" 0 -1 1752752099912 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099912 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099912 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000            -143.811 clk_50MHz  " "   -3.000            -143.811 clk_50MHz " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1752752099912 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1752752099912 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Design is not fully constrained for setup requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1752752100199 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Design is not fully constrained for hold requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1752752100202 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Timing Analyzer 0 s 5 s Quartus Prime " "Quartus Prime Timing Analyzer was successful. 0 errors, 5 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4875 " "Peak virtual memory: 4875 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752752100242 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 17 19:35:00 2025 " "Processing ended: Thu Jul 17 19:35:00 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752752100242 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:03 " "Elapsed time: 00:00:03" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752752100242 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:02 " "Total CPU time (on all processors): 00:00:02" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752752100242 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Timing Analyzer" 0 -1 1752752100242 ""}
{ "Info" "IFLOW_ERROR_COUNT" "Full Compilation 0 s 26 s " "Quartus Prime Full Compilation was successful. 0 errors, 26 warnings" {  } {  } 0 293000 "Quartus Prime %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Timing Analyzer" 0 -1 1752752100859 ""}
