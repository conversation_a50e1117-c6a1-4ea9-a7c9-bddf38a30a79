# 智能小车代码修复总结

## 问题分析

经过深入分析，发现了以下关键问题导致四个轮子都没有动，且避障循迹功能不正常：

### 1. 时钟分频问题
- **原问题**: PWM模块直接使用50MHz时钟，频率过高，不适合电机驱动
- **修复方案**: 添加时钟分频器，将50MHz分频到约97kHz的PWM时钟

### 2. PWM占空比设置问题
- **原问题**: PWM生成逻辑依赖motor_dir信号，当motor_dir为0时PWM输出为0
- **修复方案**: 修改PWM生成逻辑，只要speed_control > 0就产生PWM信号

### 3. 电机驱动信号极性错误
- **原问题**: 使用单PWM控制方式，无法正确控制电机正反转
- **修复方案**: 改为双PWM控制方式（PWM_A和PWM_B），符合标准H桥驱动

### 4. 传感器数据处理逻辑错误
- **原问题**: 传感器数据直接使用，容易受噪声干扰
- **修复方案**: 添加3级寄存器滤波和防抖动逻辑

### 5. 状态机设计问题
- **原问题**: 简单的优先级控制，缺乏状态管理
- **修复方案**: 设计完整的状态机，包含空闲、循线、避障、丢失线路、停止等状态

## 具体修复内容

### PWM模块 (zhuhoujia2023111413_004.v)
```verilog
// 添加时钟分频器
reg [9:0] clk_divider;
reg pwm_clk;

// 双PWM输出
output reg [3:0] motor_pwm_a;
output reg [3:0] motor_pwm_b;

// H桥驱动逻辑
if (motor_dir[i]) begin
    motor_pwm_a[i] <= 1'b1;  // 正转：A端PWM
    motor_pwm_b[i] <= 1'b0;  // B端低电平
end else begin
    motor_pwm_a[i] <= 1'b0;  // 反转：A端低电平
    motor_pwm_b[i] <= 1'b1;  // B端PWM
end
```

### 传感器滤波 (zhuhoujia2023111413_001.v, zhuhoujia2023111413_002.v)
```verilog
// 3级寄存器滤波
reg [3:0] sensor_reg1, sensor_reg2, sensor_reg3;
reg [3:0] sensor_filtered;
reg [15:0] debounce_counter;

// 多数表决滤波
if ((sensor_reg1 == sensor_reg2) && (sensor_reg2 == sensor_reg3)) begin
    if (debounce_counter >= 16'd1000) begin
        sensor_filtered <= sensor_reg3;
    end
end
```

### 状态机设计 (zhuhoujia2023111413_003.v)
```verilog
// 状态定义
parameter STATE_IDLE        = 3'b000;
parameter STATE_LINE_FOLLOW = 3'b001;
parameter STATE_OBSTACLE    = 3'b010;
parameter STATE_LOST        = 3'b011;
parameter STATE_STOP        = 3'b100;

// 状态转换逻辑
always @(*) begin
    case (current_state)
        STATE_LINE_FOLLOW: begin
            if (obstacle_control != FORWARD_FULL) begin
                next_state = STATE_OBSTACLE;
            end else if (line_control == STOP) begin
                next_state = STATE_STOP;
            end
            // ... 其他状态转换
        end
    endcase
end
```

## 修复效果验证

### 预期改进
1. **电机控制**: 四个轮子应该能够正常转动，支持前进、后退、左转、右转
2. **循线功能**: 能够根据传感器信号进行精确的线路跟踪
3. **避障功能**: 检测到障碍物时能够及时避让
4. **状态协调**: 循线和避障功能能够协调工作
5. **抗干扰**: 传感器滤波提高系统稳定性

### 测试建议
1. 使用simple_motor_test.v进行基本功能测试
2. 检查PWM信号是否正确生成
3. 验证电机方向控制是否正确
4. 测试传感器响应是否稳定
5. 验证状态机转换是否正常

## 硬件连接建议

### 电机驱动连接
```
电机驱动器输入:
- 左前轮: IN1A <- motor_pwm_a[0], IN1B <- motor_pwm_b[0]
- 左后轮: IN2A <- motor_pwm_a[1], IN2B <- motor_pwm_b[1]
- 右前轮: IN3A <- motor_pwm_a[2], IN3B <- motor_pwm_b[2]
- 右后轮: IN4A <- motor_pwm_a[3], IN4B <- motor_pwm_b[3]
```

### 传感器连接
```
循线传感器: [左外, 左内, 右内, 右外] -> line_sensor[3:0]
避障传感器: [左侧, 右侧] -> obstacle_sensor[1:0]
```

## 注意事项

1. **时钟频率**: 确保输入时钟为50MHz
2. **复位信号**: 确保rst_n为低电平有效复位
3. **传感器极性**: 确认传感器输出极性与代码逻辑匹配
4. **电机驱动**: 确认电机驱动器支持双PWM输入模式
5. **电源供应**: 确保电机和控制电路有足够的电源供应

通过以上修复，智能小车应该能够正常实现循线和避障功能。
