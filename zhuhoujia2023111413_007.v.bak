module smart_car_top(
    input clk_50MHz,
    input rst_n,
    
    // 循线寻迹信号 (4位)
    input [3:0] line_sensor,
    
    // 避障信号 (2位)
    input [1:0] obstacle_sensor,
    
    // DHT11温湿度传感器信号
    inout dht11_data,
    
    // 电机控制输出
    output [3:0] motor_dir,    // 电机方向控制
    output [3:0] motor_pwm,    // 电机PWM调速
    
    // LCD显示接口
    output lcd_rst,
    output lcd_rw,
    output lcd_en,
    output [7:0] lcd_data
);

// 内部信号定义
wire [1:0] line_control;
wire [1:0] obstacle_control;
wire [1:0] final_control;
wire [7:0] motor_speed;
wire [15:0] temperature;
wire [15:0] humidity;
wire dht11_valid;

// 模块实例化
line_following_module line_follow(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .line_sensor(line_sensor),
    .control_signal(line_control)
);

obstacle_avoidance_module obstacle_avoid(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .obstacle_sensor(obstacle_sensor),
    .control_signal(obstacle_control)
);

motor_control_module motor_ctrl(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .line_control(line_control),
    .obstacle_control(obstacle_control),
    .motor_dir(motor_dir),
    .motor_speed(motor_speed),
    .final_control(final_control)
);

pwm_generator_module pwm_gen(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .speed_control(motor_speed),
    .motor_pwm(motor_pwm)
);

dht11_sensor_module dht11_sensor(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .dht11_data(dht11_data),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(dht11_valid)
);

lcd_display_module lcd_display(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(dht11_valid),
    .lcd_rst(lcd_rst),
    .lcd_rw(lcd_rw),
    .lcd_en(lcd_en),
    .lcd_data(lcd_data)
);

endmodule
