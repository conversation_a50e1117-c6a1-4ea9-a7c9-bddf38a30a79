`timescale 1ns / 1ps

module zhuhoujia2023111413_007_tb;

// 测试信号定义
reg clk_50MHz;
reg rst_n;
reg [3:0] line_sensor;
reg [1:0] obstacle_sensor;
wire dht11_data;
wire [3:0] motor_dir;
wire [3:0] motor_pwm;
wire lcd_rst;
wire lcd_rw;
wire lcd_en;
wire [7:0] lcd_data;

// DHT11数据模拟
reg dht11_data_reg;
reg dht11_oe_sim;
assign dht11_data = dht11_oe_sim ? dht11_data_reg : 1'bz;

// 实例化被测试模块 - 智能小车顶层模块
zhuhoujia2023111413_007 uut (
    .clk_50MHz(clk_50MHz),
    .rst_n(rst_n),
    .line_sensor(line_sensor),
    .obstacle_sensor(obstacle_sensor),
    .dht11_data(dht11_data),
    .motor_dir(motor_dir),
    .motor_pwm(motor_pwm),
    .lcd_rst(lcd_rst),
    .lcd_rw(lcd_rw),
    .lcd_en(lcd_en),
    .lcd_data(lcd_data)
);

// 时钟生成 - 50MHz主时钟
initial begin
    clk_50MHz = 0;
    forever #10 clk_50MHz = ~clk_50MHz;
end

// DHT11响应模拟任务
task simulate_dht11_response;
    input [39:0] test_data;
    integer i;
    begin
        wait(dht11_data == 1'b0);
        wait(dht11_data == 1'bz);
        
        dht11_oe_sim = 1;
        dht11_data_reg = 0; #800;
        dht11_data_reg = 1; #800;
        
        for (i = 39; i >= 0; i = i - 1) begin
            dht11_data_reg = 0; #500;
            dht11_data_reg = 1;
            if (test_data[i]) #700;
            else #260;
        end
        
        dht11_data_reg = 0; #500;
        dht11_oe_sim = 0;
    end
endtask

// 场景测试任务
task test_scenario;
    input [3:0] line_input;
    input [1:0] obstacle_input;
    input string scenario_name;
    begin
        line_sensor = line_input;
        obstacle_sensor = obstacle_input;
        #1000; // 等待系统响应
        
        $display("=== %s ===", scenario_name);
        $display("输入 - 循线传感器: %b, 避障传感器: %b", line_input, obstacle_input);
        $display("输出 - 电机方向: %b, 电机PWM: %b", motor_dir, motor_pwm);
        $display("内部信号 - 循线控制: %b, 避障控制: %b, 最终控制: %b", 
                 uut.line_control, uut.obstacle_control, uut.final_control);
        $display("温湿度 - 温度: %d°C, 湿度: %d%%, 数据有效: %b", 
                 uut.temperature[15:8], uut.humidity[15:8], uut.dht11_valid);
        $display("");
    end
endtask

// 主测试序列
initial begin
    // 系统初始化
    rst_n = 0;
    line_sensor = 4'b0000;
    obstacle_sensor = 2'b00;
    dht11_data_reg = 1;
    dht11_oe_sim = 0;
    
    $display("========================================");
    $display("智能小车系统集成测试开始");
    $display("========================================");
    
    // 释放复位
    #1000;
    rst_n = 1;
    #2000;
    
    // 启动DHT11数据模拟
    fork
        begin
            forever begin
                #100000; // 每100ms模拟一次DHT11数据
                simulate_dht11_response(40'h3C001900_55); // 60%, 25°C
            end
        end
    join_none
    
    // 场景1：正常循线行驶
    test_scenario(4'b0110, 2'b00, "场景1: 正常循线前进");
    
    // 场景2：循线过程中遇到右侧障碍
    test_scenario(4'b0110, 2'b01, "场景2: 循线时右侧有障碍");
    
    // 场景3：循线左偏且左侧有障碍
    test_scenario(4'b1110, 2'b10, "场景3: 循线左偏遇左侧障碍");
    
    // 场景4：前方有障碍需要后退
    test_scenario(4'b0110, 2'b11, "场景4: 前方障碍需要后退");
    
    // 场景5：急剧右偏无障碍
    test_scenario(4'b0001, 2'b00, "场景5: 急剧右偏修正");
    
    // 场景6：到达终点（全部传感器检测到线）
    test_scenario(4'b1111, 2'b00, "场景6: 到达终点停止");
    
    // 场景7：丢失线路
    test_scenario(4'b0000, 2'b00, "场景7: 丢失线路继续前进");
    
    // 等待LCD显示稳定
    #10000;
    
    // 检查系统状态
    $display("========================================");
    $display("系统状态检查");
    $display("========================================");
    $display("LCD状态: rst=%b, rw=%b, en=%b, data=0x%h", 
             lcd_rst, lcd_rw, lcd_en, lcd_data);
    $display("电机状态: 方向=%b, PWM=%b", motor_dir, motor_pwm);
    $display("传感器数据: 温度=%d°C, 湿度=%d%%", 
             uut.temperature[15:8], uut.humidity[15:8]);
    
    // 测试系统复位
    $display("\n测试系统复位功能...");
    rst_n = 0;
    #1000;
    
    if (motor_pwm == 4'b0000 && lcd_rst == 1'b0) begin
        $display("系统复位测试通过");
    end else begin
        $display("系统复位测试失败");
    end
    
    rst_n = 1;
    #2000;
    
    $display("========================================");
    $display("智能小车系统集成测试完成");
    $display("========================================");
    $finish;
end

// 实时监控关键信号变化
always @(posedge clk_50MHz) begin
    // 监控控制信号变化
    if (uut.line_control != uut.obstacle_control && $time > 5000) begin
        $display("控制冲突: 时间=%0t, 循线=%b, 避障=%b, 最终=%b", 
                 $time, uut.line_control, uut.obstacle_control, uut.final_control);
    end
end

// 监控DHT11数据更新
always @(posedge uut.dht11_valid) begin
    if ($time > 5000) begin
        $display("DHT11数据更新: 时间=%0t, 温度=%d°C, 湿度=%d%%", 
                 $time, uut.temperature[15:8], uut.humidity[15:8]);
    end
end

// 监控电机状态变化
always @(motor_dir or motor_pwm) begin
    if ($time > 5000) begin
        $display("电机状态变化: 时间=%0t, 方向=%b, PWM=%b", 
                 $time, motor_dir, motor_pwm);
    end
end

// 生成波形文件
initial begin
    $dumpfile("zhuhoujia2023111413_007_tb.vcd");
    $dumpvars(0, zhuhoujia2023111413_007_tb);
end

endmodule