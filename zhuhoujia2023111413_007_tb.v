`timescale 1ns / 1ps

module zhuhoujia2023111413_007_tb;

// 测试信号定义
reg clk_50MHz;
reg rst_n;
reg [3:0] line_sensor;
reg [1:0] obstacle_sensor;
wire dht11_data;
wire motor1_a, motor1_b;
wire motor2_a, motor2_b;
wire motor3_a, motor3_b;
wire motor4_a, motor4_b;
wire lcd_rst;
wire lcd_rw;
wire lcd_en;
wire [7:0] lcd_data;

// DHT11数据模拟
reg dht11_data_reg;
reg dht11_oe_sim;
assign dht11_data = dht11_oe_sim ? dht11_data_reg : 1'bz;

// 实例化被测试模块 - 智能小车顶层模块
zhuhoujia2023111413_007 uut (
    .clk_50MHz(clk_50MHz),
    .rst_n(rst_n),
    .line_sensor(line_sensor),
    .obstacle_sensor(obstacle_sensor),
    .dht11_data(dht11_data),
    .motor1_a(motor1_a),
    .motor1_b(motor1_b),
    .motor2_a(motor2_a),
    .motor2_b(motor2_b),
    .motor3_a(motor3_a),
    .motor3_b(motor3_b),
    .motor4_a(motor4_a),
    .motor4_b(motor4_b),
    .lcd_rst(lcd_rst),
    .lcd_rw(lcd_rw),
    .lcd_en(lcd_en),
    .lcd_data(lcd_data)
);

// 时钟生成 - 50MHz主时钟
initial begin
    clk_50MHz = 0;
    forever #10 clk_50MHz = ~clk_50MHz;
end

// 场景测试任务
task test_scenario;
    input [3:0] line_input;
    input [1:0] obstacle_input;
    input string scenario_name;
    begin
        line_sensor = line_input;
        obstacle_sensor = obstacle_input;
        #5000; // 等待系统响应
        
        $display("=== %s ===", scenario_name);
        $display("输入 - 循线传感器: %b, 避障传感器: %b", line_input, obstacle_input);
        $display("输出 - 电机1: A=%b B=%b, 电机2: A=%b B=%b", motor1_a, motor1_b, motor2_a, motor2_b);
        $display("       电机3: A=%b B=%b, 电机4: A=%b B=%b", motor3_a, motor3_b, motor4_a, motor4_b);
        $display("LCD - rst=%b, rw=%b, en=%b, data=0x%h", lcd_rst, lcd_rw, lcd_en, lcd_data);
        $display("");
    end
endtask

// 主测试序列
initial begin
    // 系统初始化
    rst_n = 0;
    line_sensor = 4'b0000;
    obstacle_sensor = 2'b00;
    dht11_data_reg = 1;
    dht11_oe_sim = 0;
    
    $display("========================================");
    $display("智能小车系统测试开始");
    $display("========================================");
    
    // 释放复位
    #1000;
    rst_n = 1;
    #2000;
    
    // 测试1：正常循线前进
    test_scenario(4'b0110, 2'b00, "场景1: 正常循线前进");
    
    // 测试2：循线过程中遇到右侧障碍
    test_scenario(4'b0110, 2'b01, "场景2: 循线时右侧有障碍");
    
    // 测试3：循线左偏且左侧有障碍
    test_scenario(4'b1110, 2'b10, "场景3: 循线左偏遇左侧障碍");
    
    // 测试4：前方有障碍需要后退
    test_scenario(4'b0110, 2'b11, "场景4: 前方障碍需要后退");
    
    // 测试5：急剧右偏无障碍
    test_scenario(4'b0001, 2'b00, "场景5: 急剧右偏修正");
    
    // 测试6：到达终点（全部传感器检测到线）
    test_scenario(4'b1111, 2'b00, "场景6: 到达终点停止");
    
    // 测试7：丢失线路
    test_scenario(4'b0000, 2'b00, "场景7: 丢失线路继续前进");
    
    // 等待LCD显示稳定
    #10000;
    
    // 检查系统状态
    $display("========================================");
    $display("系统状态检查");
    $display("========================================");
    $display("LCD状态: rst=%b, rw=%b, en=%b, data=0x%h", 
             lcd_rst, lcd_rw, lcd_en, lcd_data);
    $display("电机状态:");
    $display("  电机1: A=%b B=%b", motor1_a, motor1_b);
    $display("  电机2: A=%b B=%b", motor2_a, motor2_b);
    $display("  电机3: A=%b B=%b", motor3_a, motor3_b);
    $display("  电机4: A=%b B=%b", motor4_a, motor4_b);
    
    // 测试系统复位
    $display("\n测试系统复位功能...");
    rst_n = 0;
    #1000;
    
    if (motor1_a == 1'b0 && motor1_b == 1'b0 && lcd_rst == 1'b0) begin
        $display("系统复位测试通过");
    end else begin
        $display("系统复位测试失败");
    end
    
    rst_n = 1;
    #2000;
    
    $display("========================================");
    $display("智能小车系统测试完成");
    $display("========================================");
    $finish;
end

// 实时监控电机状态变化
always @(motor1_a or motor1_b or motor2_a or motor2_b or motor3_a or motor3_b or motor4_a or motor4_b) begin
    if ($time > 5000) begin
        $display("电机状态变化: 时间=%0t", $time);
        $display("  电机1: A=%b B=%b, 电机2: A=%b B=%b", motor1_a, motor1_b, motor2_a, motor2_b);
        $display("  电机3: A=%b B=%b, 电机4: A=%b B=%b", motor3_a, motor3_b, motor4_a, motor4_b);
    end
end

// 监控LCD数据变化
always @(lcd_data) begin
    if ($time > 5000 && lcd_data != 8'h00) begin
        $display("LCD数据更新: 时间=%0t, 数据=0x%h ('%c')", $time, lcd_data, lcd_data);
    end
end

// 生成波形文件
initial begin
    $dumpfile("zhuhoujia2023111413_007_tb.vcd");
    $dumpvars(0, zhuhoujia2023111413_007_tb);
end

endmodule
