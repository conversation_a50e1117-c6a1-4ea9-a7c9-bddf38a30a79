`timescale 1ns / 1ps

module dht11_test_simple;

// Test signals
reg clk;
reg rst_n;
wire dht11_data;
wire [15:0] temperature;
wire [15:0] humidity;
wire data_valid;

// DHT11 data simulation
reg dht11_data_reg;
reg dht11_oe_sim;
assign dht11_data = dht11_oe_sim ? dht11_data_reg : 1'bz;

// Instantiate the module under test
zhuhoujia14131454_005 uut (
    .clk(clk),
    .rst_n(rst_n),
    .dht11_data(dht11_data),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(data_valid)
);

// Clock generation - 50MHz
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// State decode task
task decode_state;
    input [2:0] state;
    begin
        case (state)
            3'd0: $display("Current State: IDLE");
            3'd1: $display("Current State: START");
            3'd2: $display("Current State: RESPONSE");
            3'd3: $display("Current State: RECEIVE");
            3'd4: $display("Current State: FINISH");
            default: $display("Current State: UNKNOWN");
        endcase
    end
endtask

// Simplified DHT11 response simulation
task simple_dht11_response;
    begin
        // Wait for start signal
        #20000;
        
        // Simulate DHT11 response
        dht11_oe_sim = 1;
        dht11_data_reg = 0; 
        #1600; // 80us low response
        dht11_data_reg = 1; 
        #1600; // 80us high response
        
        // Send simplified data bits (only a few bits for demonstration)
        // Send humidity high byte (example: 0x3C = 60 percent)
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit7 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit6 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit5 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit4 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit3 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit2 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit1 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit0 = 0
        
        // Send humidity low byte (example: 0x00)
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // 8 zero bits
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;
        
        // Send temperature high byte (example: 0x19 = 25 degrees C)
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit7 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit6 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit5 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit4 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit3 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit2 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit1 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit0 = 1
        
        // Send temperature low byte (example: 0x05 = 0.5 degrees C)
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit7 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit6 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit5 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit4 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit3 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit2 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit1 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit0 = 1
        
        // Send checksum (example: 0x3C + 0x00 + 0x19 + 0x05 = 0x60)
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit7 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit6 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #1400; // bit5 = 1
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit4 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit3 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit2 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit1 = 0
        dht11_data_reg = 0; #1000; dht11_data_reg = 1; #560;  // bit0 = 0
        
        // End signal
        dht11_data_reg = 0; 
        #1000;
        dht11_oe_sim = 0;   // Release bus
    end
endtask

// Main test sequence
initial begin
    // Initialize
    rst_n = 0;
    dht11_data_reg = 1;
    dht11_oe_sim = 0;
    
    $display("========================================");
    $display("DHT11 Temperature Humidity Sensor Test Start");
    $display("========================================");
    
    // Release reset
    #100;
    rst_n = 1;
    #100;
    
    // Test 1: Basic functionality test
    $display("[Test 1: Basic DHT11 Communication Test]");
    
    // Start DHT11 response simulation
    simple_dht11_response();
    
    // Wait for data processing to complete
    #10000;
    
    $display("DHT11 Read Results:");
    $display("  Temperature: %d.%d degrees C (Raw: 0x%04h)", 
             temperature[15:8], temperature[7:0], temperature);
    $display("  Humidity: %d.%d percent (Raw: 0x%04h)", 
             humidity[15:8], humidity[7:0], humidity);
    $display("  Data Valid Flag: %b", data_valid);
    
    // Test 2: Reset function test
    $display("");
    $display("[Test 2: Reset Function Test]");
    $display("State before reset:");
    decode_state(uut.state);
    $display("Temperature: 0x%04h, Humidity: 0x%04h, Valid: %b", temperature, humidity, data_valid);
    
    rst_n = 0;
    #100;
    $display("State after reset:");
    decode_state(uut.state);
    $display("Temperature: 0x%04h, Humidity: 0x%04h, Valid: %b", temperature, humidity, data_valid);
    
    rst_n = 1;
    #100;
    
    $display("");
    $display("========================================");
    $display("DHT11 Temperature Humidity Sensor Test Complete");
    $display("========================================");
    $finish;
end

// State monitoring
always @(uut.state) begin
    if ($time > 200) begin
        $display("Time=%0t: DHT11 state changed", $time);
        decode_state(uut.state);
    end
end

// Data valid flag monitoring
always @(data_valid) begin
    if ($time > 200) begin
        if (data_valid) begin
            $display("Time=%0t: Data read complete", $time);
        end else begin
            $display("Time=%0t: Start new measurement cycle", $time);
        end
    end
end

// Generate waveform file
initial begin
    $dumpfile("dht11_test_simple.vcd");
    $dumpvars(0, dht11_test_simple);
end

endmodule
