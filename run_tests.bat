@echo off
echo ========================================
echo 智能小车模块测试脚本
echo ========================================

echo.
echo 测试1: 循线传感器模块
echo ----------------------------------------
iverilog -o zhuhoujia14131454_001_test zhuhoujia14131454_001_tb.v zhuhoujia14131454_001.v
if %errorlevel% equ 0 (
    echo 编译成功，运行测试...
    vvp zhuhoujia14131454_001_test
) else (
    echo 编译失败！
)

echo.
echo 测试2: 避障传感器模块
echo ----------------------------------------
iverilog -o zhuhoujia14131454_002_test zhuhoujia14131454_002_tb.v zhuhoujia14131454_002.v
if %errorlevel% equ 0 (
    echo 编译成功，运行测试...
    vvp zhuhoujia14131454_002_test
) else (
    echo 编译失败！
)

echo.
echo 测试3: 运动决策控制模块
echo ----------------------------------------
iverilog -o zhuhoujia14131454_003_test zhuhoujia14131454_003_tb.v zhuhoujia14131454_003.v
if %errorlevel% equ 0 (
    echo 编译成功，运行测试...
    vvp zhuhoujia14131454_003_test
) else (
    echo 编译失败！
)

echo.
echo 测试4: 电机驱动和时钟分频模块
echo ----------------------------------------
iverilog -o zhuhoujia14131454_004_test zhuhoujia14131454_004_tb.v zhuhoujia14131454_004.v
if %errorlevel% equ 0 (
    echo 编译成功，运行测试...
    vvp zhuhoujia14131454_004_test
) else (
    echo 编译失败！
)

echo.
echo 测试5: DHT11温湿度传感器模块
echo ----------------------------------------
iverilog -o zhuhoujia14131454_005_test zhuhoujia14131454_005_tb.v zhuhoujia14131454_005.v
if %errorlevel% equ 0 (
    echo 编译成功，运行测试...
    vvp zhuhoujia14131454_005_test
) else (
    echo 编译失败！
)

echo.
echo 测试6: LCD显示模块
echo ----------------------------------------
iverilog -o zhuhoujia14131454_006_test zhuhoujia14131454_006_tb.v zhuhoujia14131454_006.v
if %errorlevel% equ 0 (
    echo 编译成功，运行测试...
    vvp zhuhoujia14131454_006_test
) else (
    echo 编译失败！
)

echo.
echo 测试7: 系统顶层集成测试
echo ----------------------------------------
iverilog -o zhuhoujia14131454_007_test zhuhoujia14131454_007_tb.v zhuhoujia14131454_007.v zhuhoujia14131454_001.v zhuhoujia14131454_002.v zhuhoujia14131454_003.v zhuhoujia14131454_004.v zhuhoujia14131454_005.v zhuhoujia14131454_006.v
if %errorlevel% equ 0 (
    echo 编译成功，运行测试...
    vvp zhuhoujia14131454_007_test
) else (
    echo 编译失败！
)

echo.
echo ========================================
echo 所有测试完成！
echo 波形文件已生成，可用GTKWave查看
echo ========================================
pause
