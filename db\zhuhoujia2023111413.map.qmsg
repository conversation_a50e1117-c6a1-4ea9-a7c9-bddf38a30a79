{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1752856603222 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus Prime " "Running Quartus Prime Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752856603228 ""} { "Info" "IQEXE_START_BANNER_TIME" "Sat Jul 19 00:36:43 2025 " "Processing started: Sat Jul 19 00:36:43 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752856603228 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856603228 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off zhuhoujia2023111413 -c zhuhoujia2023111413 " "Command: quartus_map --read_settings_files=on --write_settings_files=off zhuhoujia2023111413 -c zhuhoujia2023111413" {  } {  } 0 0 "Command: %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856603228 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Analysis & Synthesis" 0 -1 1752856604377 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Analysis & Synthesis" 0 -1 1752856604377 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia14131454_001.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia14131454_001.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia14131454_001 " "Found entity 1: zhuhoujia14131454_001" {  } { { "zhuhoujia14131454_001.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_001.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752856611741 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856611741 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia14131454_007.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia14131454_007.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia14131454_007 " "Found entity 1: zhuhoujia14131454_007" {  } { { "zhuhoujia14131454_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_007.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752856611745 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856611745 ""}
{ "Info" "IVRFX_L3_VERI_OBJ_DIFF_ONLY_IN_CASE" "DISPLAY_TEXT display_text zhuhoujia14131454_006.v(20) " "Verilog HDL Declaration information at zhuhoujia14131454_006.v(20): object \"DISPLAY_TEXT\" differs only in case from object \"display_text\" in the same scope" {  } { { "zhuhoujia14131454_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 20 0 0 } }  } 0 10281 "Verilog HDL Declaration information at %3!s!: object \"%1!s!\" differs only in case from object \"%2!s!\" in the same scope" 1 0 "Analysis & Synthesis" 0 -1 1752856611747 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia14131454_006.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia14131454_006.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia14131454_006 " "Found entity 1: zhuhoujia14131454_006" {  } { { "zhuhoujia14131454_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752856611748 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856611748 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia14131454_005.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia14131454_005.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia14131454_005 " "Found entity 1: zhuhoujia14131454_005" {  } { { "zhuhoujia14131454_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_005.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752856611751 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856611751 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia14131454_004.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia14131454_004.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia14131454_004 " "Found entity 1: zhuhoujia14131454_004" {  } { { "zhuhoujia14131454_004.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_004.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752856611755 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856611755 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia14131454_003.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia14131454_003.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia14131454_003 " "Found entity 1: zhuhoujia14131454_003" {  } { { "zhuhoujia14131454_003.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_003.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752856611758 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856611758 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia14131454_002.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia14131454_002.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia14131454_002 " "Found entity 1: zhuhoujia14131454_002" {  } { { "zhuhoujia14131454_002.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_002.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752856611762 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856611762 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "zhuhoujia14131454_007 " "Elaborating entity \"zhuhoujia14131454_007\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Analysis & Synthesis" 0 -1 1752856611805 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia14131454_001 zhuhoujia14131454_001:line_follow " "Elaborating entity \"zhuhoujia14131454_001\" for hierarchy \"zhuhoujia14131454_001:line_follow\"" {  } { { "zhuhoujia14131454_007.v" "line_follow" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_007.v" 47 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752856611812 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia14131454_002 zhuhoujia14131454_002:obstacle_avoid " "Elaborating entity \"zhuhoujia14131454_002\" for hierarchy \"zhuhoujia14131454_002:obstacle_avoid\"" {  } { { "zhuhoujia14131454_007.v" "obstacle_avoid" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_007.v" 54 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752856611817 ""}
{ "Warning" "WVRFX_L2_HDL_OBJECT_ASSIGNED_NOT_READ" "obstacle_output zhuhoujia14131454_002.v(19) " "Verilog HDL or VHDL warning at zhuhoujia14131454_002.v(19): object \"obstacle_output\" assigned a value but never read" {  } { { "zhuhoujia14131454_002.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_002.v" 19 0 0 } }  } 0 10036 "Verilog HDL or VHDL warning at %2!s!: object \"%1!s!\" assigned a value but never read" 0 0 "Analysis & Synthesis" 0 -1 1752856611817 "|zhuhoujia14131454_007|zhuhoujia14131454_002:obstacle_avoid"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia14131454_003 zhuhoujia14131454_003:motor_ctrl " "Elaborating entity \"zhuhoujia14131454_003\" for hierarchy \"zhuhoujia14131454_003:motor_ctrl\"" {  } { { "zhuhoujia14131454_007.v" "motor_ctrl" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_007.v" 64 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752856611824 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia14131454_004 zhuhoujia14131454_004:motor_driver " "Elaborating entity \"zhuhoujia14131454_004\" for hierarchy \"zhuhoujia14131454_004:motor_driver\"" {  } { { "zhuhoujia14131454_007.v" "motor_driver" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_007.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752856611832 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia14131454_005 zhuhoujia14131454_005:dht11_sensor " "Elaborating entity \"zhuhoujia14131454_005\" for hierarchy \"zhuhoujia14131454_005:dht11_sensor\"" {  } { { "zhuhoujia14131454_007.v" "dht11_sensor" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_007.v" 90 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752856611845 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia14131454_005.v(50) " "Verilog HDL assignment warning at zhuhoujia14131454_005.v(50): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia14131454_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_005.v" 50 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752856611845 "|zhuhoujia14131454_007|zhuhoujia14131454_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia14131454_005.v(61) " "Verilog HDL assignment warning at zhuhoujia14131454_005.v(61): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia14131454_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_005.v" 61 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752856611846 "|zhuhoujia14131454_007|zhuhoujia14131454_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia14131454_005.v(71) " "Verilog HDL assignment warning at zhuhoujia14131454_005.v(71): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia14131454_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_005.v" 71 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752856611846 "|zhuhoujia14131454_007|zhuhoujia14131454_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 6 zhuhoujia14131454_005.v(80) " "Verilog HDL assignment warning at zhuhoujia14131454_005.v(80): truncated value with size 32 to match size of target (6)" {  } { { "zhuhoujia14131454_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_005.v" 80 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752856611846 "|zhuhoujia14131454_007|zhuhoujia14131454_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia14131454_005.v(83) " "Verilog HDL assignment warning at zhuhoujia14131454_005.v(83): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia14131454_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_005.v" 83 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752856611846 "|zhuhoujia14131454_007|zhuhoujia14131454_005:dht11_sensor"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia14131454_006 zhuhoujia14131454_006:lcd_display " "Elaborating entity \"zhuhoujia14131454_006\" for hierarchy \"zhuhoujia14131454_006:lcd_display\"" {  } { { "zhuhoujia14131454_007.v" "lcd_display" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_007.v" 102 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752856611861 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia14131454_006.v(59) " "Verilog HDL assignment warning at zhuhoujia14131454_006.v(59): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia14131454_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 59 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752856611862 "|zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia14131454_006.v(70) " "Verilog HDL assignment warning at zhuhoujia14131454_006.v(70): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia14131454_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 70 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752856611862 "|zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 4 zhuhoujia14131454_006.v(86) " "Verilog HDL assignment warning at zhuhoujia14131454_006.v(86): truncated value with size 32 to match size of target (4)" {  } { { "zhuhoujia14131454_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 86 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752856611863 "|zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia14131454_006.v(94) " "Verilog HDL assignment warning at zhuhoujia14131454_006.v(94): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia14131454_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 94 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752856611863 "|zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia14131454_006.v(104) " "Verilog HDL assignment warning at zhuhoujia14131454_006.v(104): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia14131454_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 104 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752856611863 "|zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "lcd_rw zhuhoujia14131454_006.v(42) " "Verilog HDL Always Construct warning at zhuhoujia14131454_006.v(42): inferring latch(es) for variable \"lcd_rw\", which holds its previous value in one or more paths through the always construct" {  } { { "zhuhoujia14131454_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 42 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752856611863 "|zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display"}
{ "Warning" "WVRFX_VDB_DRIVERLESS_NET" "display_text.data_a 0 zhuhoujia14131454_006.v(24) " "Net \"display_text.data_a\" at zhuhoujia14131454_006.v(24) has no driver or initial value, using a default initial value '0'" {  } { { "zhuhoujia14131454_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 24 0 0 } }  } 0 10030 "Net \"%1!s!\" at %3!s! has no driver or initial value, using a default initial value '%2!c!'" 0 0 "Analysis & Synthesis" 0 -1 1752856611864 "|zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display"}
{ "Warning" "WVRFX_VDB_DRIVERLESS_NET" "display_text.waddr_a 0 zhuhoujia14131454_006.v(24) " "Net \"display_text.waddr_a\" at zhuhoujia14131454_006.v(24) has no driver or initial value, using a default initial value '0'" {  } { { "zhuhoujia14131454_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 24 0 0 } }  } 0 10030 "Net \"%1!s!\" at %3!s! has no driver or initial value, using a default initial value '%2!c!'" 0 0 "Analysis & Synthesis" 0 -1 1752856611864 "|zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display"}
{ "Warning" "WVRFX_VDB_DRIVERLESS_NET" "display_text.we_a 0 zhuhoujia14131454_006.v(24) " "Net \"display_text.we_a\" at zhuhoujia14131454_006.v(24) has no driver or initial value, using a default initial value '0'" {  } { { "zhuhoujia14131454_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 24 0 0 } }  } 0 10030 "Net \"%1!s!\" at %3!s! has no driver or initial value, using a default initial value '%2!c!'" 0 0 "Analysis & Synthesis" 0 -1 1752856611864 "|zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "lcd_rw zhuhoujia14131454_006.v(42) " "Inferred latch for \"lcd_rw\" at zhuhoujia14131454_006.v(42)" {  } { { "zhuhoujia14131454_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856611864 "|zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display"}
{ "Info" "IINFER_UNINFERRED_RAM_SUMMARY" "1 " "Found 1 instances of uninferred RAM logic" { { "Info" "IINFER_RAM_UNINFERRED_DUE_TO_SIZE" "zhuhoujia14131454_006:lcd_display\|display_text " "RAM logic \"zhuhoujia14131454_006:lcd_display\|display_text\" is uninferred due to inappropriate RAM size" {  } { { "zhuhoujia14131454_006.v" "display_text" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_006.v" 24 -1 0 } }  } 0 276004 "RAM logic \"%1!s!\" is uninferred due to inappropriate RAM size" 0 0 "Design Software" 0 -1 1752856612208 ""}  } {  } 0 276014 "Found %1!d! instances of uninferred RAM logic" 0 0 "Analysis & Synthesis" 0 -1 1752856612208 ""}
{ "Critical Warning" "WCDB_CDB_LESS_INI_CONTENT" "16 10 E:/zhuhoujia2023111413/db/zhuhoujia2023111413.ram0_zhuhoujia14131454_006_e74913fb.hdl.mif " "Memory depth (16) in the design file differs from memory depth (10) in the Memory Initialization File \"E:/zhuhoujia2023111413/db/zhuhoujia2023111413.ram0_zhuhoujia14131454_006_e74913fb.hdl.mif\" -- setting initial value for remaining addresses to 0" {  } {  } 1 127005 "Memory depth (%1!d!) in the design file differs from memory depth (%2!d!) in the Memory Initialization File \"%3!s!\" -- setting initial value for remaining addresses to 0" 0 0 "Analysis & Synthesis" 0 -1 1752856612209 ""}
{ "Warning" "WMLS_MLS_STUCK_PIN_HDR" "" "Output pins are stuck at VCC or GND" { { "Warning" "WMLS_MLS_STUCK_PIN" "lcd_rw GND " "Pin \"lcd_rw\" is stuck at GND" {  } { { "zhuhoujia14131454_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_007.v" 26 -1 0 } }  } 0 13410 "Pin \"%1!s!\" is stuck at %2!s!" 0 0 "Design Software" 0 -1 1752856612476 "|zhuhoujia14131454_007|lcd_rw"} { "Warning" "WMLS_MLS_STUCK_PIN" "lcd_data\[6\] GND " "Pin \"lcd_data\[6\]\" is stuck at GND" {  } { { "zhuhoujia14131454_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_007.v" 29 -1 0 } }  } 0 13410 "Pin \"%1!s!\" is stuck at %2!s!" 0 0 "Design Software" 0 -1 1752856612476 "|zhuhoujia14131454_007|lcd_data[6]"} { "Warning" "WMLS_MLS_STUCK_PIN" "lcd_data\[7\] GND " "Pin \"lcd_data\[7\]\" is stuck at GND" {  } { { "zhuhoujia14131454_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia14131454_007.v" 29 -1 0 } }  } 0 13410 "Pin \"%1!s!\" is stuck at %2!s!" 0 0 "Design Software" 0 -1 1752856612476 "|zhuhoujia14131454_007|lcd_data[7]"}  } {  } 0 13024 "Output pins are stuck at VCC or GND" 0 0 "Analysis & Synthesis" 0 -1 1752856612476 ""}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Timing-Driven Synthesis is running" {  } {  } 0 286030 "Timing-Driven Synthesis is running" 0 0 "Analysis & Synthesis" 0 -1 1752856612553 ""}
{ "Info" "ISCL_SCL_LOST_FANOUT_MSG_HDR" "6 " "6 registers lost all their fanouts during netlist optimizations." {  } {  } 0 17049 "%1!d! registers lost all their fanouts during netlist optimizations." 0 0 "Analysis & Synthesis" 0 -1 1752856612760 ""}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "E:/zhuhoujia2023111413/zhuhoujia2023111413.map.smsg " "Generated suppressed messages file E:/zhuhoujia2023111413/zhuhoujia2023111413.map.smsg" {  } {  } 0 144001 "Generated suppressed messages file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856612794 ""}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "0 0 0 0 0 " "Adding 0 node(s), including 0 DDIO, 0 PLL, 0 transceiver and 0 LCELL" {  } {  } 0 16011 "Adding %1!d! node(s), including %2!d! DDIO, %3!d! PLL, %4!d! transceiver and %5!d! LCELL" 0 0 "Design Software" 0 -1 1752856612955 ""}  } {  } 0 16010 "Generating hard_block partition \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752856612955 ""}
{ "Info" "ICUT_CUT_TM_SUMMARY" "267 " "Implemented 267 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "8 " "Implemented 8 input pins" {  } {  } 0 21058 "Implemented %1!d! input pins" 0 0 "Design Software" 0 -1 1752856613100 ""} { "Info" "ICUT_CUT_TM_OPINS" "21 " "Implemented 21 output pins" {  } {  } 0 21059 "Implemented %1!d! output pins" 0 0 "Design Software" 0 -1 1752856613100 ""} { "Info" "ICUT_CUT_TM_BIDIRS" "1 " "Implemented 1 bidirectional pins" {  } {  } 0 21060 "Implemented %1!d! bidirectional pins" 0 0 "Design Software" 0 -1 1752856613100 ""} { "Info" "ICUT_CUT_TM_LCELLS" "237 " "Implemented 237 logic cells" {  } {  } 0 21061 "Implemented %1!d! logic cells" 0 0 "Design Software" 0 -1 1752856613100 ""}  } {  } 0 21057 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "Analysis & Synthesis" 0 -1 1752856613100 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 21 s Quartus Prime " "Quartus Prime Analysis & Synthesis was successful. 0 errors, 21 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4895 " "Peak virtual memory: 4895 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752856613112 ""} { "Info" "IQEXE_END_BANNER_TIME" "Sat Jul 19 00:36:53 2025 " "Processing ended: Sat Jul 19 00:36:53 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752856613112 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:10 " "Elapsed time: 00:00:10" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752856613112 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:18 " "Total CPU time (on all processors): 00:00:18" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752856613112 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856613112 ""}
