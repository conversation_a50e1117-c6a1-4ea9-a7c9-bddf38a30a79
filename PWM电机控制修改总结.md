# PWM电机控制和分频器集成修改总结

## 修改概述

根据您的要求，我已经将PWM调速功能和时钟分频器集成到您的智能小车代码中，并修正了电机驱动引脚配置。主要修改包括：

1. **电机驱动模块 (zhuhoujia2023111413_004.v)** - 集成PWM调速和分频器，使用8个引脚控制4个电机
2. **电机控制模块 (zhuhoujia2023111413_003.v)** - 输出电机控制命令和速度值
3. **顶层模块 (zhuhoujia2023111413_007.v)** - 更新接口连接
4. **LCD显示模块 (zhuhoujia2023111413_006.v)** - 显示"2023111413"

## 详细修改内容

### 1. 电机驱动模块 (zhuhoujia2023111413_004.v)

**新增功能：**
- PWM调速控制（8位精度，256级调速）
- 时钟分频器（1MHz和100Hz输出）
- 8种电机控制模式

**接口变化：**
```verilog
// 输入
input wire [3:0] motor_control, // 电机控制命令
input wire [7:0] speed_control, // 速度控制值

// 输出 - 8个电机驱动引脚
output reg motor1_a, motor1_b,  // 电机1（左前）
output reg motor2_a, motor2_b,  // 电机2（左后）
output reg motor3_a, motor3_b,  // 电机3（右前）
output reg motor4_a, motor4_b,  // 电机4（右后）
output reg clk_1m,              // 1MHz输出时钟
output reg clk_100hz            // 100Hz输出时钟
```

**电机控制命令：**
- `4'd0`: 停止
- `4'd1`: 前进
- `4'd2`: 后退
- `4'd3`: 左转
- `4'd4`: 右转
- `4'd5`: 缓左转
- `4'd6`: 缓右转
- `4'd7`: 急左转
- `4'd8`: 急右转

### 2. 电机控制模块 (zhuhoujia2023111413_003.v)

**新增输出：**
```verilog
output reg [3:0] motor_control, // 电机控制命令输出
output reg [7:0] speed_control  // 速度控制值输出
```

**控制逻辑映射：**
- `FORWARD_FULL` → 前进，速度200
- `FORWARD_SLOW` → 前进，速度100
- `LEFT_SLOW` → 缓左转，速度150
- `LEFT_FAST` → 急左转，速度180
- `RIGHT_SLOW` → 缓右转，速度150
- `RIGHT_FAST` → 急右转，速度180
- `BACK_FULL` → 后退，速度150
- `STOP` → 停止，速度0

### 3. 时钟分频器功能

**1MHz时钟生成：**
- 50MHz ÷ 50 = 1MHz
- 计数器范围：0-24

**100Hz时钟生成：**
- 50MHz ÷ 500000 = 100Hz
- 计数器范围：0-249999

### 4. PWM调速原理

**PWM生成：**
```verilog
motor_pwm[i] <= (pwm_counter < speed_control) ? 1'b1 : 1'b0;
```

**占空比计算：**
- 占空比 = speed_control / 256
- 例如：speed_control = 128 → 占空比 = 50%

### 5. LCD显示功能

LCD模块显示"2023111413"十个数字，循环显示每个字符。

**显示内容：**
- 字符串："2023111413"
- 字符数量：10个
- 显示方式：逐个字符循环显示
- 存储方式：ROM数组存储ASCII码

## 功能特点

### PWM调速优势
1. **精确控制**：256级速度调节
2. **平滑运行**：PWM控制减少电机抖动
3. **节能高效**：PWM控制降低功耗
4. **差速转向**：支持缓转和急转

### 分频器功能
1. **1MHz时钟**：可用于精确定时
2. **100Hz时钟**：可用于传感器采样或显示刷新

### 电机控制模式
1. **基本运动**：前进、后退、停止
2. **转向控制**：左转、右转
3. **精细转向**：缓转（差速）、急转（原地转向）

## 测试建议

1. **基本功能测试**：
   - 运行 `motor_pwm_test.v` 测试文件
   - 验证各种运动模式
   - 检查PWM波形

2. **速度测试**：
   - 测试不同速度值的电机响应
   - 验证PWM占空比变化

3. **转向测试**：
   - 测试缓转和急转的区别
   - 验证差速控制效果

4. **时钟测试**：
   - 验证1MHz和100Hz时钟输出
   - 检查分频精度

## 硬件连接建议

### 电机驱动器连接（8个引脚）
```
电机1（左前）: IN1 -> motor1_a, IN2 -> motor1_b
电机2（左后）: IN3 -> motor2_a, IN4 -> motor2_b
电机3（右前）: IN5 -> motor3_a, IN6 -> motor3_b
电机4（右后）: IN7 -> motor4_a, IN8 -> motor4_b
```

**电机控制逻辑：**
- 正转：A=1, B=0（配合PWM控制）
- 反转：A=0, B=1（配合PWM控制）
- 停止：A=0, B=0

### 时钟输出
```
1MHz时钟: clk_1m -> 外部设备时钟输入
100Hz时钟: clk_100hz -> 传感器采样时钟
```

## 预期效果

1. **电机运行**：四个轮子能够根据传感器输入进行精确的PWM调速控制
2. **循线功能**：支持多种转向模式，提高循线精度
3. **避障功能**：快速响应障碍物，执行相应的避障动作
4. **LCD显示**：持续显示"2023111413"字符
5. **时钟输出**：提供稳定的1MHz和100Hz时钟信号

通过这些修改，您的智能小车现在具备了完整的PWM调速功能和时钟分频器，能够实现更精确的运动控制。
