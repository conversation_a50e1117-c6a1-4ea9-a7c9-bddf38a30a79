 -- Copyright (C) 2018  Intel Corporation. All rights reserved.
 -- Your use of Intel Corporation's design tools, logic functions 
 -- and other software and tools, and its AMPP partner logic 
 -- functions, and any output files from any of the foregoing 
 -- (including device programming or simulation files), and any 
 -- associated documentation or information are expressly subject 
 -- to the terms and conditions of the Intel Program License 
 -- Subscription Agreement, the Intel Quartus Prime License Agreement,
 -- the Intel FPGA IP License Agreement, or other applicable license
 -- agreement, including, without limitation, that your use is for
 -- the sole purpose of programming logic devices manufactured by
 -- Intel and sold by Intel or its authorized distributors.  Please
 -- refer to the applicable agreement for further details.
 -- 
 -- This is a Quartus Prime output file. It is for reporting purposes only, and is
 -- not intended for use as a Quartus Prime input file. This file cannot be used
 -- to make Quartus Prime pin assignments - for instructions on how to make pin
 -- assignments, please see Quartus Prime help.
 ---------------------------------------------------------------------------------



 ---------------------------------------------------------------------------------
 -- NC            : No Connect. This pin has no internal connection to the device.
 -- DNU           : Do Not Use. This pin MUST NOT be connected.
 -- VCCINT        : Dedicated power pin, which MUST be connected to VCC  (1.2V).
 -- VCCIO         : Dedicated power pin, which MUST be connected to VCC
 --                 of its bank.
 --                  Bank 1:       2.5V
 --                  Bank 2:       2.5V
 --                  Bank 3:       2.5V
 --                  Bank 4:       2.5V
 --                  Bank 5:       2.5V
 --                  Bank 6:       2.5V
 --                  Bank 7:       2.5V
 --                  Bank 8:       2.5V
 -- GND           : Dedicated ground pin. Dedicated GND pins MUST be connected to GND.
 --                  It can also be used to report unused dedicated pins. The connection
 --                  on the board for unused dedicated pins depends on whether this will
 --                  be used in a future design. One example is device migration. When
 --                  using device migration, refer to the device pin-tables. If it is a
 --                  GND pin in the pin table or if it will not be used in a future design
 --                  for another purpose the it MUST be connected to GND. If it is an unused
 --                  dedicated pin, then it can be connected to a valid signal on the board
 --                  (low, high, or toggling) if that signal is required for a different
 --                  revision of the design.
 -- GND+          : Unused input pin. It can also be used to report unused dual-purpose pins.
 --                  This pin should be connected to GND. It may also be connected  to a
 --                  valid signal  on the board  (low, high, or toggling)  if that signal
 --                  is required for a different revision of the design.
 -- GND*          : Unused  I/O  pin. Connect each pin marked GND* directly to GND
 --                  or leave it unconnected.
 -- RESERVED      : Unused I/O pin, which MUST be left unconnected.
 -- RESERVED_INPUT    : Pin is tri-stated and should be connected to the board.
 -- RESERVED_INPUT_WITH_WEAK_PULLUP    : Pin is tri-stated with internal weak pull-up resistor.
 -- RESERVED_INPUT_WITH_BUS_HOLD       : Pin is tri-stated with bus-hold circuitry.
 -- RESERVED_OUTPUT_DRIVEN_HIGH        : Pin is output driven high.
 ---------------------------------------------------------------------------------



 ---------------------------------------------------------------------------------
 -- Pin directions (input, output or bidir) are based on device operating in user mode.
 ---------------------------------------------------------------------------------

Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
CHIP  "11"  ASSIGNED TO AN: EP4CE6E22C8

Pin Name/Usage               : Location  : Dir.   : I/O Standard      : Voltage : I/O Bank  : User Assignment
-------------------------------------------------------------------------------------------------------------
RESERVED_INPUT_WITH_WEAK_PULLUP : 1         :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 2         :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 3         :        :                   :         : 1         :                
GND                          : 4         : gnd    :                   :         :           :                
VCCINT                       : 5         : power  :                   : 1.2V    :           :                
~ALTERA_ASDO_DATA1~ / RESERVED_INPUT_WITH_WEAK_PULLUP : 6         : input  : 2.5 V             :         : 1         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 7         :        :                   :         : 1         :                
~ALTERA_FLASH_nCE_nCSO~ / RESERVED_INPUT_WITH_WEAK_PULLUP : 8         : input  : 2.5 V             :         : 1         : N              
nSTATUS                      : 9         :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 10        :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 11        :        :                   :         : 1         :                
~ALTERA_DCLK~                : 12        : output : 2.5 V             :         : 1         : N              
~ALTERA_DATA0~ / RESERVED_INPUT_WITH_WEAK_PULLUP : 13        : input  : 2.5 V             :         : 1         : N              
nCONFIG                      : 14        :        :                   :         : 1         :                
TDI                          : 15        : input  :                   :         : 1         :                
TCK                          : 16        : input  :                   :         : 1         :                
VCCIO1                       : 17        : power  :                   : 2.5V    : 1         :                
TMS                          : 18        : input  :                   :         : 1         :                
GND                          : 19        : gnd    :                   :         :           :                
TDO                          : 20        : output :                   :         : 1         :                
nCE                          : 21        :        :                   :         : 1         :                
GND                          : 22        : gnd    :                   :         :           :                
clk_50MHz                    : 23        : input  : 2.5 V             :         : 1         : N              
rst_n                        : 24        : input  : 2.5 V             :         : 2         : N              
obstacle_sensor[0]           : 25        : input  : 2.5 V             :         : 2         : N              
VCCIO2                       : 26        : power  :                   : 2.5V    : 2         :                
GND                          : 27        : gnd    :                   :         :           :                
motor_pwm_a[3]               : 28        : output : 2.5 V             :         : 2         : N              
VCCINT                       : 29        : power  :                   : 1.2V    :           :                
motor_pwm_b[1]               : 30        : output : 2.5 V             :         : 2         : N              
motor_pwm_a[0]               : 31        : output : 2.5 V             :         : 2         : N              
motor_dir[2]                 : 32        : output : 2.5 V             :         : 2         : N              
motor_dir[3]                 : 33        : output : 2.5 V             :         : 2         : N              
motor_pwm_b[0]               : 34        : output : 2.5 V             :         : 2         : N              
VCCA1                        : 35        : power  :                   : 2.5V    :           :                
GNDA1                        : 36        : gnd    :                   :         :           :                
VCCD_PLL1                    : 37        : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 38        :        :                   :         : 3         :                
motor_pwm_b[3]               : 39        : output : 2.5 V             :         : 3         : N              
VCCIO3                       : 40        : power  :                   : 2.5V    : 3         :                
GND                          : 41        : gnd    :                   :         :           :                
motor_pwm_b[2]               : 42        : output : 2.5 V             :         : 3         : N              
motor_dir[1]                 : 43        : output : 2.5 V             :         : 3         : N              
motor_dir[0]                 : 44        : output : 2.5 V             :         : 3         : N              
VCCINT                       : 45        : power  :                   : 1.2V    :           :                
motor_pwm_a[2]               : 46        : output : 2.5 V             :         : 3         : N              
VCCIO3                       : 47        : power  :                   : 2.5V    : 3         :                
GND                          : 48        : gnd    :                   :         :           :                
motor_pwm_a[1]               : 49        : output : 2.5 V             :         : 3         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 50        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 51        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 52        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 53        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 54        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 55        :        :                   :         : 4         :                
VCCIO4                       : 56        : power  :                   : 2.5V    : 4         :                
GND                          : 57        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 58        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 59        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 60        :        :                   :         : 4         :                
VCCINT                       : 61        : power  :                   : 1.2V    :           :                
VCCIO4                       : 62        : power  :                   : 2.5V    : 4         :                
GND                          : 63        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 64        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 65        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 66        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 67        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 68        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 69        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 70        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 71        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 72        :        :                   :         : 4         :                
lcd_rw                       : 73        : output : 2.5 V             :         : 5         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 74        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 75        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 76        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 77        :        :                   :         : 5         :                
VCCINT                       : 78        : power  :                   : 1.2V    :           :                
GND                          : 79        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 80        :        :                   :         : 5         :                
VCCIO5                       : 81        : power  :                   : 2.5V    : 5         :                
GND                          : 82        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 83        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 84        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 85        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 86        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 87        :        :                   :         : 5         :                
GND+                         : 88        :        :                   :         : 5         :                
GND+                         : 89        :        :                   :         : 5         :                
GND+                         : 90        :        :                   :         : 6         :                
GND+                         : 91        :        :                   :         : 6         :                
CONF_DONE                    : 92        :        :                   :         : 6         :                
VCCIO6                       : 93        : power  :                   : 2.5V    : 6         :                
MSEL0                        : 94        :        :                   :         : 6         :                
GND                          : 95        : gnd    :                   :         :           :                
MSEL1                        : 96        :        :                   :         : 6         :                
MSEL2                        : 97        :        :                   :         : 6         :                
lcd_data[1]                  : 98        : output : 2.5 V             :         : 6         : N              
lcd_rst                      : 99        : output : 2.5 V             :         : 6         : N              
lcd_data[4]                  : 100       : output : 2.5 V             :         : 6         : N              
~ALTERA_nCEO~ / RESERVED_OUTPUT_OPEN_DRAIN : 101       : output : 2.5 V             :         : 6         : N              
VCCINT                       : 102       : power  :                   : 1.2V    :           :                
lcd_data[3]                  : 103       : output : 2.5 V             :         : 6         : N              
lcd_data[5]                  : 104       : output : 2.5 V             :         : 6         : N              
lcd_data[6]                  : 105       : output : 2.5 V             :         : 6         : N              
dht11_data                   : 106       : bidir  : 2.5 V             :         : 6         : N              
VCCA2                        : 107       : power  :                   : 2.5V    :           :                
GNDA2                        : 108       : gnd    :                   :         :           :                
VCCD_PLL2                    : 109       : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 110       :        :                   :         : 7         :                
lcd_data[2]                  : 111       : output : 2.5 V             :         : 7         : N              
lcd_data[7]                  : 112       : output : 2.5 V             :         : 7         : N              
lcd_en                       : 113       : output : 2.5 V             :         : 7         : N              
lcd_data[0]                  : 114       : output : 2.5 V             :         : 7         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 115       :        :                   :         : 7         :                
VCCINT                       : 116       : power  :                   : 1.2V    :           :                
VCCIO7                       : 117       : power  :                   : 2.5V    : 7         :                
GND                          : 118       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 119       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 120       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 121       :        :                   :         : 7         :                
VCCIO7                       : 122       : power  :                   : 2.5V    : 7         :                
GND                          : 123       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 124       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 125       :        :                   :         : 7         :                
line_sensor[3]               : 126       : input  : 2.5 V             :         : 7         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 127       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 128       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 129       :        :                   :         : 8         :                
VCCIO8                       : 130       : power  :                   : 2.5V    : 8         :                
GND                          : 131       : gnd    :                   :         :           :                
line_sensor[1]               : 132       : input  : 2.5 V             :         : 8         : N              
line_sensor[0]               : 133       : input  : 2.5 V             :         : 8         : N              
VCCINT                       : 134       : power  :                   : 1.2V    :           :                
line_sensor[2]               : 135       : input  : 2.5 V             :         : 8         : N              
obstacle_sensor[1]           : 136       : input  : 2.5 V             :         : 8         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 137       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 138       :        :                   :         : 8         :                
VCCIO8                       : 139       : power  :                   : 2.5V    : 8         :                
GND                          : 140       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 141       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 142       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 143       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 144       :        :                   :         : 8         :                
GND                          : EPAD      :        :                   :         :           :                
