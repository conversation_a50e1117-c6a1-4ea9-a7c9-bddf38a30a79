 -- Copyright (C) 2018  Intel Corporation. All rights reserved.
 -- Your use of Intel Corporation's design tools, logic functions 
 -- and other software and tools, and its AMPP partner logic 
 -- functions, and any output files from any of the foregoing 
 -- (including device programming or simulation files), and any 
 -- associated documentation or information are expressly subject 
 -- to the terms and conditions of the Intel Program License 
 -- Subscription Agreement, the Intel Quartus Prime License Agreement,
 -- the Intel FPGA IP License Agreement, or other applicable license
 -- agreement, including, without limitation, that your use is for
 -- the sole purpose of programming logic devices manufactured by
 -- Intel and sold by Intel or its authorized distributors.  Please
 -- refer to the applicable agreement for further details.
 -- 
 -- This is a Quartus Prime output file. It is for reporting purposes only, and is
 -- not intended for use as a Quartus Prime input file. This file cannot be used
 -- to make Quartus Prime pin assignments - for instructions on how to make pin
 -- assignments, please see Quartus Prime help.
 ---------------------------------------------------------------------------------



 ---------------------------------------------------------------------------------
 -- NC            : No Connect. This pin has no internal connection to the device.
 -- DNU           : Do Not Use. This pin MUST NOT be connected.
 -- VCCINT        : Dedicated power pin, which MUST be connected to VCC  (1.2V).
 -- VCCIO         : Dedicated power pin, which MUST be connected to VCC
 --                 of its bank.
 --                  Bank 1:       2.5V
 --                  Bank 2:       2.5V
 --                  Bank 3:       2.5V
 --                  Bank 4:       2.5V
 --                  Bank 5:       2.5V
 --                  Bank 6:       2.5V
 --                  Bank 7:       2.5V
 --                  Bank 8:       2.5V
 -- GND           : Dedicated ground pin. Dedicated GND pins MUST be connected to GND.
 --                  It can also be used to report unused dedicated pins. The connection
 --                  on the board for unused dedicated pins depends on whether this will
 --                  be used in a future design. One example is device migration. When
 --                  using device migration, refer to the device pin-tables. If it is a
 --                  GND pin in the pin table or if it will not be used in a future design
 --                  for another purpose the it MUST be connected to GND. If it is an unused
 --                  dedicated pin, then it can be connected to a valid signal on the board
 --                  (low, high, or toggling) if that signal is required for a different
 --                  revision of the design.
 -- GND+          : Unused input pin. It can also be used to report unused dual-purpose pins.
 --                  This pin should be connected to GND. It may also be connected  to a
 --                  valid signal  on the board  (low, high, or toggling)  if that signal
 --                  is required for a different revision of the design.
 -- GND*          : Unused  I/O  pin. Connect each pin marked GND* directly to GND
 --                  or leave it unconnected.
 -- RESERVED      : Unused I/O pin, which MUST be left unconnected.
 -- RESERVED_INPUT    : Pin is tri-stated and should be connected to the board.
 -- RESERVED_INPUT_WITH_WEAK_PULLUP    : Pin is tri-stated with internal weak pull-up resistor.
 -- RESERVED_INPUT_WITH_BUS_HOLD       : Pin is tri-stated with bus-hold circuitry.
 -- RESERVED_OUTPUT_DRIVEN_HIGH        : Pin is output driven high.
 ---------------------------------------------------------------------------------



 ---------------------------------------------------------------------------------
 -- Pin directions (input, output or bidir) are based on device operating in user mode.
 ---------------------------------------------------------------------------------

Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
CHIP  "11"  ASSIGNED TO AN: EP4CE6E22C8

Pin Name/Usage               : Location  : Dir.   : I/O Standard      : Voltage : I/O Bank  : User Assignment
-------------------------------------------------------------------------------------------------------------
lcd_data[5]                  : 1         : output : 2.5 V             :         : 1         : N              
lcd_data[4]                  : 2         : output : 2.5 V             :         : 1         : N              
lcd_data[2]                  : 3         : output : 2.5 V             :         : 1         : N              
GND                          : 4         : gnd    :                   :         :           :                
VCCINT                       : 5         : power  :                   : 1.2V    :           :                
~ALTERA_ASDO_DATA1~ / RESERVED_INPUT_WITH_WEAK_PULLUP : 6         : input  : 2.5 V             :         : 1         : N              
lcd_en                       : 7         : output : 2.5 V             :         : 1         : N              
~ALTERA_FLASH_nCE_nCSO~ / RESERVED_INPUT_WITH_WEAK_PULLUP : 8         : input  : 2.5 V             :         : 1         : N              
nSTATUS                      : 9         :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 10        :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 11        :        :                   :         : 1         :                
~ALTERA_DCLK~                : 12        : output : 2.5 V             :         : 1         : N              
~ALTERA_DATA0~ / RESERVED_INPUT_WITH_WEAK_PULLUP : 13        : input  : 2.5 V             :         : 1         : N              
nCONFIG                      : 14        :        :                   :         : 1         :                
TDI                          : 15        : input  :                   :         : 1         :                
TCK                          : 16        : input  :                   :         : 1         :                
VCCIO1                       : 17        : power  :                   : 2.5V    : 1         :                
TMS                          : 18        : input  :                   :         : 1         :                
GND                          : 19        : gnd    :                   :         :           :                
TDO                          : 20        : output :                   :         : 1         :                
nCE                          : 21        :        :                   :         : 1         :                
GND                          : 22        : gnd    :                   :         :           :                
clk_50MHz                    : 23        : input  : 2.5 V             :         : 1         : N              
rst_n                        : 24        : input  : 2.5 V             :         : 2         : N              
obstacle_sensor[0]           : 25        : input  : 2.5 V             :         : 2         : N              
VCCIO2                       : 26        : power  :                   : 2.5V    : 2         :                
GND                          : 27        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 28        :        :                   :         : 2         :                
VCCINT                       : 29        : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 30        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 31        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 32        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 33        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 34        :        :                   :         : 2         :                
VCCA1                        : 35        : power  :                   : 2.5V    :           :                
GNDA1                        : 36        : gnd    :                   :         :           :                
VCCD_PLL1                    : 37        : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 38        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 39        :        :                   :         : 3         :                
VCCIO3                       : 40        : power  :                   : 2.5V    : 3         :                
GND                          : 41        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 42        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 43        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 44        :        :                   :         : 3         :                
VCCINT                       : 45        : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 46        :        :                   :         : 3         :                
VCCIO3                       : 47        : power  :                   : 2.5V    : 3         :                
GND                          : 48        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 49        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 50        :        :                   :         : 3         :                
lcd_data[6]                  : 51        : output : 2.5 V             :         : 3         : N              
motor_pwm[0]                 : 52        : output : 2.5 V             :         : 3         : N              
line_sensor[2]               : 53        : input  : 2.5 V             :         : 3         : N              
line_sensor[3]               : 54        : input  : 2.5 V             :         : 4         : N              
line_sensor[0]               : 55        : input  : 2.5 V             :         : 4         : N              
VCCIO4                       : 56        : power  :                   : 2.5V    : 4         :                
GND                          : 57        : gnd    :                   :         :           :                
motor_pwm[1]                 : 58        : output : 2.5 V             :         : 4         : N              
line_sensor[1]               : 59        : input  : 2.5 V             :         : 4         : N              
obstacle_sensor[1]           : 60        : input  : 2.5 V             :         : 4         : N              
VCCINT                       : 61        : power  :                   : 1.2V    :           :                
VCCIO4                       : 62        : power  :                   : 2.5V    : 4         :                
GND                          : 63        : gnd    :                   :         :           :                
motor_pwm[3]                 : 64        : output : 2.5 V             :         : 4         : N              
motor_pwm[2]                 : 65        : output : 2.5 V             :         : 4         : N              
motor_dir[3]                 : 66        : output : 2.5 V             :         : 4         : N              
motor_dir[2]                 : 67        : output : 2.5 V             :         : 4         : N              
lcd_rw                       : 68        : output : 2.5 V             :         : 4         : N              
motor_dir[0]                 : 69        : output : 2.5 V             :         : 4         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 70        :        :                   :         : 4         :                
motor_dir[1]                 : 71        : output : 2.5 V             :         : 4         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 72        :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 73        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 74        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 75        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 76        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 77        :        :                   :         : 5         :                
VCCINT                       : 78        : power  :                   : 1.2V    :           :                
GND                          : 79        : gnd    :                   :         :           :                
clk_1m                       : 80        : output : 2.5 V             :         : 5         : N              
VCCIO5                       : 81        : power  :                   : 2.5V    : 5         :                
GND                          : 82        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 83        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 84        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 85        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 86        :        :                   :         : 5         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 87        :        :                   :         : 5         :                
GND+                         : 88        :        :                   :         : 5         :                
GND+                         : 89        :        :                   :         : 5         :                
GND+                         : 90        :        :                   :         : 6         :                
GND+                         : 91        :        :                   :         : 6         :                
CONF_DONE                    : 92        :        :                   :         : 6         :                
VCCIO6                       : 93        : power  :                   : 2.5V    : 6         :                
MSEL0                        : 94        :        :                   :         : 6         :                
GND                          : 95        : gnd    :                   :         :           :                
MSEL1                        : 96        :        :                   :         : 6         :                
MSEL2                        : 97        :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 98        :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 99        :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 100       :        :                   :         : 6         :                
~ALTERA_nCEO~ / RESERVED_OUTPUT_OPEN_DRAIN : 101       : output : 2.5 V             :         : 6         : N              
VCCINT                       : 102       : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 103       :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 104       :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 105       :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 106       :        :                   :         : 6         :                
VCCA2                        : 107       : power  :                   : 2.5V    :           :                
GNDA2                        : 108       : gnd    :                   :         :           :                
VCCD_PLL2                    : 109       : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 110       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 111       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 112       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 113       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 114       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 115       :        :                   :         : 7         :                
VCCINT                       : 116       : power  :                   : 1.2V    :           :                
VCCIO7                       : 117       : power  :                   : 2.5V    : 7         :                
GND                          : 118       : gnd    :                   :         :           :                
lcd_data[7]                  : 119       : output : 2.5 V             :         : 7         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 120       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 121       :        :                   :         : 7         :                
VCCIO7                       : 122       : power  :                   : 2.5V    : 7         :                
GND                          : 123       : gnd    :                   :         :           :                
dht11_data                   : 124       : bidir  : 2.5 V             :         : 7         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 125       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 126       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 127       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 128       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 129       :        :                   :         : 8         :                
VCCIO8                       : 130       : power  :                   : 2.5V    : 8         :                
GND                          : 131       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 132       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 133       :        :                   :         : 8         :                
VCCINT                       : 134       : power  :                   : 1.2V    :           :                
clk_100hz                    : 135       : output : 2.5 V             :         : 8         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : 136       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 137       :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : 138       :        :                   :         : 8         :                
VCCIO8                       : 139       : power  :                   : 2.5V    : 8         :                
GND                          : 140       : gnd    :                   :         :           :                
lcd_rst                      : 141       : output : 2.5 V             :         : 8         : N              
lcd_data[3]                  : 142       : output : 2.5 V             :         : 8         : N              
lcd_data[0]                  : 143       : output : 2.5 V             :         : 8         : N              
lcd_data[1]                  : 144       : output : 2.5 V             :         : 8         : N              
GND                          : EPAD      :        :                   :         :           :                
