`timescale 1ns / 1ps

module lcd_2023111413_test;

// 测试信号
reg clk;
reg rst_n;
reg [15:0] temperature;
reg [15:0] humidity;
reg data_valid;
wire lcd_rst;
wire lcd_rw;
wire lcd_en;
wire [7:0] lcd_data;

// 实例化LCD显示模块
zhuhoujia2023111413_006 uut (
    .clk(clk),
    .rst_n(rst_n),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(data_valid),
    .lcd_rst(lcd_rst),
    .lcd_rw(lcd_rw),
    .lcd_en(lcd_en),
    .lcd_data(lcd_data)
);

// 时钟生成 - 50MHz
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// 字符显示监控
always @(posedge clk) begin
    if (lcd_data >= 8'h30 && lcd_data <= 8'h39 && $time > 5000) begin
        $display("时间=%0t: LCD显示字符='%c' (ASCII=0x%h)", $time, lcd_data, lcd_data);
    end
end

// 主测试序列
initial begin
    // 初始化
    rst_n = 0;
    temperature = 16'h1900;  // 25°C
    humidity = 16'h3C00;     // 60%
    data_valid = 1'b1;
    
    $display("========================================");
    $display("LCD显示\"2023111413\"测试开始");
    $display("========================================");
    
    // 释放复位
    #1000;
    rst_n = 1;
    #2000;
    
    $display("LCD状态监控:");
    $display("复位信号: %b", lcd_rst);
    $display("读写信号: %b", lcd_rw);
    
    // 等待LCD初始化完成
    #10000;
    
    // 监控字符显示过程
    $display("\n开始监控字符显示过程...");
    
    // 等待显示完整的"2023111413"字符串
    repeat(50) begin
        @(posedge clk);
        if (uut.state == uut.DISPLAY_TEXT) begin
            if (uut.delay_counter == 16'd0) begin
                $display("显示字符[%d]: '%c' (ASCII=0x%h)", 
                         uut.char_index, uut.display_text[uut.char_index], uut.display_text[uut.char_index]);
            end
        end
        #1000;
    end
    
    // 验证显示的字符串
    $display("\n========================================");
    $display("验证显示字符串内容:");
    $display("========================================");
    
    $display("预期显示: \"2023111413\"");
    $write("实际显示: \"");
    for (integer i = 0; i < 10; i = i + 1) begin
        $write("%c", uut.display_text[i]);
    end
    $display("\"");
    
    // 检查每个字符是否正确
    if (uut.display_text[0] == 8'h32 &&  // '2'
        uut.display_text[1] == 8'h30 &&  // '0'
        uut.display_text[2] == 8'h32 &&  // '2'
        uut.display_text[3] == 8'h33 &&  // '3'
        uut.display_text[4] == 8'h31 &&  // '1'
        uut.display_text[5] == 8'h31 &&  // '1'
        uut.display_text[6] == 8'h31 &&  // '1'
        uut.display_text[7] == 8'h34 &&  // '4'
        uut.display_text[8] == 8'h31 &&  // '1'
        uut.display_text[9] == 8'h33) begin // '3'
        $display("✓ 字符串验证通过！");
    end else begin
        $display("✗ 字符串验证失败！");
    end
    
    // 测试状态机转换
    $display("\n========================================");
    $display("测试状态机转换:");
    $display("========================================");
    
    repeat(20) begin
        @(posedge clk);
        case (uut.state)
            uut.RESET: $display("状态: RESET");
            uut.INIT: $display("状态: INIT");
            uut.DISPLAY_TEXT: begin
                if (uut.delay_counter % 1000 == 0) begin
                    $display("状态: DISPLAY_TEXT, 字符索引: %d", uut.char_index);
                end
            end
            uut.IDLE: $display("状态: IDLE");
        endcase
        #2000;
    end
    
    // 测试循环显示
    $display("\n========================================");
    $display("测试循环显示功能:");
    $display("========================================");
    
    // 等待一个完整的显示周期
    wait(uut.state == uut.IDLE);
    $display("第一轮显示完成，进入IDLE状态");
    
    wait(uut.state == uut.DISPLAY_TEXT);
    $display("开始第二轮显示");
    
    $display("\n========================================");
    $display("LCD显示\"2023111413\"测试完成");
    $display("========================================");
    $finish;
end

// 监控LCD控制信号变化
always @(lcd_rst or lcd_en) begin
    if ($time > 5000) begin
        $display("LCD控制信号变化: 时间=%0t, rst=%b, en=%b", $time, lcd_rst, lcd_en);
    end
end

// 生成波形文件
initial begin
    $dumpfile("lcd_2023111413_test.vcd");
    $dumpvars(0, lcd_2023111413_test);
end

endmodule
