`timescale 1ns / 1ps

module zhuhoujia2023111413_003_tb;

// 测试信号定义
reg clk;
reg rst_n;
reg [1:0] line_control;
reg [1:0] obstacle_control;
wire [3:0] motor_dir;
wire [7:0] motor_speed;
wire [1:0] final_control;

// 实例化被测试模块 - 电机控制模块
zhuhoujia2023111413_003 uut (
    .clk(clk),
    .rst_n(rst_n),
    .line_control(line_control),
    .obstacle_control(obstacle_control),
    .motor_dir(motor_dir),
    .motor_speed(motor_speed),
    .final_control(final_control)
);

// 时钟生成
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// 测试序列
initial begin
    // 初始化
    rst_n = 0;
    line_control = 2'b00;
    obstacle_control = 2'b00;
    
    #100;
    rst_n = 1;
    #20;
    
    // 测试用例1：无障碍，循线前进
    line_control = 2'b00;    // 前进
    obstacle_control = 2'b00; // 无障碍
    #40;
    $display("测试1 - 循线前进: final=%b, motor_dir=%b, speed=%d", final_control, motor_dir, motor_speed);
    
    // 测试用例2：无障碍，循线左转
    line_control = 2'b01;    // 左转
    obstacle_control = 2'b00; // 无障碍
    #40;
    $display("测试2 - 循线左转: final=%b, motor_dir=%b, speed=%d", final_control, motor_dir, motor_speed);
    
    // 测试用例3：避障优先级测试 - 右侧有障碍
    line_control = 2'b00;    // 循线要求前进
    obstacle_control = 2'b01; // 避障要求左转
    #40;
    $display("测试3 - 避障优先: final=%b, motor_dir=%b, speed=%d (应该左转)", final_control, motor_dir, motor_speed);
    
    // 测试用例4：前方障碍，需要后退
    line_control = 2'b01;    // 循线左转
    obstacle_control = 2'b11; // 前方障碍后退
    #40;
    $display("测试4 - 前方障碍: final=%b, motor_dir=%b, speed=%d (应该后退)", final_control, motor_dir, motor_speed);
    
    // 测试用例5：停止状态
    line_control = 2'b11;    // 停止
    obstacle_control = 2'b00; // 无障碍
    #40;
    $display("测试5 - 停止状态: final=%b, motor_dir=%b, speed=%d", final_control, motor_dir, motor_speed);
    
    $display("电机控制模块测试完成！");
    $finish;
end

initial begin
    $dumpfile("zhuhoujia2023111413_003_tb.vcd");
    $dumpvars(0, zhuhoujia2023111413_003_tb);
end

endmodule