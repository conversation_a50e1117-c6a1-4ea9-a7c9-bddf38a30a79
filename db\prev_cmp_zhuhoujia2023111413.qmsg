{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1752856432044 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus Prime " "Running Quartus Prime Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752856432049 ""} { "Info" "IQEXE_START_BANNER_TIME" "Sat Jul 19 00:33:51 2025 " "Processing started: Sat Jul 19 00:33:51 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752856432049 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856432049 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off zhuhoujia2023111413 -c zhuhoujia2023111413 " "Command: quartus_map --read_settings_files=on --write_settings_files=off zhuhoujia2023111413 -c zhuhoujia2023111413" {  } {  } 0 0 "Command: %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856432049 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Analysis & Synthesis" 0 -1 1752856433266 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Analysis & Synthesis" 0 -1 1752856433266 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_007.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_007.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia14131454_007 " "Found entity 1: zhuhoujia14131454_007" {  } { { "zhuhoujia2023111413_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752856440681 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856440681 ""}
{ "Warning" "WSGN_FILE_IS_MISSING" "zhuhoujia2023111413_006.v " "Can't analyze file -- file zhuhoujia2023111413_006.v is missing" {  } {  } 0 12019 "Can't analyze file -- file %1!s! is missing" 0 0 "Analysis & Synthesis" 0 -1 1752856440695 ""}
{ "Warning" "WSGN_FILE_IS_MISSING" "zhuhoujia2023111413_003.v " "Can't analyze file -- file zhuhoujia2023111413_003.v is missing" {  } {  } 0 12019 "Can't analyze file -- file %1!s! is missing" 0 0 "Analysis & Synthesis" 0 -1 1752856440708 ""}
{ "Warning" "WSGN_FILE_IS_MISSING" "zhuhoujia2023111413_004.v " "Can't analyze file -- file zhuhoujia2023111413_004.v is missing" {  } {  } 0 12019 "Can't analyze file -- file %1!s! is missing" 0 0 "Analysis & Synthesis" 0 -1 1752856440722 ""}
{ "Warning" "WSGN_FILE_IS_MISSING" "zhuhoujia2023111413_005.v " "Can't analyze file -- file zhuhoujia2023111413_005.v is missing" {  } {  } 0 12019 "Can't analyze file -- file %1!s! is missing" 0 0 "Analysis & Synthesis" 0 -1 1752856440734 ""}
{ "Warning" "WSGN_FILE_IS_MISSING" "zhuhoujia2023111413_001.v " "Can't analyze file -- file zhuhoujia2023111413_001.v is missing" {  } {  } 0 12019 "Can't analyze file -- file %1!s! is missing" 0 0 "Analysis & Synthesis" 0 -1 1752856440746 ""}
{ "Warning" "WSGN_FILE_IS_MISSING" "zhuhoujia2023111413_002.v " "Can't analyze file -- file zhuhoujia2023111413_002.v is missing" {  } {  } 0 12019 "Can't analyze file -- file %1!s! is missing" 0 0 "Analysis & Synthesis" 0 -1 1752856440758 ""}
{ "Error" "ESGN_TOP_ENTITY_IS_MISSING" "zhuhoujia2023111413_007 " "Top-level design entity \"zhuhoujia2023111413_007\" is undefined" {  } {  } 0 12007 "Top-level design entity \"%1!s!\" is undefined" 0 0 "Analysis & Synthesis" 0 -1 1752856440834 ""}
{ "Error" "EQEXE_ERROR_COUNT" "Analysis & Synthesis 1  7 s Quartus Prime " "Quartus Prime Analysis & Synthesis was unsuccessful. 1 error, 7 warnings" { { "Error" "EQEXE_END_PEAK_VSIZE_MEMORY" "4772 " "Peak virtual memory: 4772 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752856440955 ""} { "Error" "EQEXE_END_BANNER_TIME" "Sat Jul 19 00:34:00 2025 " "Processing ended: Sat Jul 19 00:34:00 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752856440955 ""} { "Error" "EQEXE_ELAPSED_TIME" "00:00:09 " "Elapsed time: 00:00:09" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752856440955 ""} { "Error" "EQEXE_ELAPSED_CPU_TIME" "00:00:17 " "Total CPU time (on all processors): 00:00:17" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752856440955 ""}  } {  } 0 0 "%6!s! %1!s! was unsuccessful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1752856440955 ""}
