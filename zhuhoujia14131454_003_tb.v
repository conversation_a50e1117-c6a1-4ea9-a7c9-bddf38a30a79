`timescale 1ns / 1ps

module zhuhoujia14131454_003_tb;

// 测试信号定义
reg clk;
reg rst_n;
reg [2:0] line_control;
reg [2:0] obstacle_control;
wire [2:0] final_control;
wire [3:0] motor_control;
wire [7:0] speed_control;

// 实例化被测试模块
zhuhoujia14131454_003 uut (
    .clk(clk),
    .rst_n(rst_n),
    .line_control(line_control),
    .obstacle_control(obstacle_control),
    .final_control(final_control),
    .motor_control(motor_control),
    .speed_control(speed_control)
);

// 时钟生成 - 50MHz
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// 控制信号解码任务
task decode_control_signal;
    input [2:0] signal;
    begin
        case (signal)
            3'b000: $display("FORWARD_FULL (全速前进)");
            3'b001: $display("FORWARD_SLOW (缓慢前进)");
            3'b010: $display("LEFT_SLOW (缓左转)");
            3'b011: $display("LEFT_FAST (急左转)");
            3'b100: $display("RIGHT_SLOW (缓右转)");
            3'b101: $display("RIGHT_FAST (急右转)");
            3'b110: $display("BACK_FULL (全速倒车)");
            3'b111: $display("STOP (停止)");
            default: $display("UNKNOWN (未知)");
        endcase
    end
endtask

// 电机控制命令解码任务
task decode_motor_command;
    input [3:0] command;
    begin
        $write("电机控制命令: ");
        case (command)
            4'd0: $display("停止");
            4'd1: $display("前进");
            4'd2: $display("后退");
            4'd3: $display("左转");
            4'd4: $display("右转");
            4'd5: $display("缓左转");
            4'd6: $display("缓右转");
            4'd7: $display("急左转");
            4'd8: $display("急右转");
            default: $display("未知命令");
        endcase
    end
endtask

// 测试场景任务
task test_control_scenario;
    input [2:0] line_input;
    input [2:0] obstacle_input;
    begin
        line_control = line_input;
        obstacle_control = obstacle_input;
        #100; // 等待信号稳定

        $write("循线控制: ");
        decode_control_signal(line_input);
        $write("避障控制: ");
        decode_control_signal(obstacle_input);
        $write("最终控制: ");
        decode_control_signal(final_control);
        decode_motor_command(motor_control);
        $display("速度控制值: %d", speed_control);
        $display("编码 - 最终:%b, 电机:%b, 速度:%d", final_control, motor_control, speed_control);
        $display("");
    end
endtask

// 主测试序列
initial begin
    // 初始化
    rst_n = 0;
    line_control = 3'b000;
    obstacle_control = 3'b000;
    
    $display("========================================");
    $display("运动决策控制模块测试开始");
    $display("测试避障优先级和控制信号映射");
    $display("========================================");
    
    // 释放复位
    #50;
    rst_n = 1;
    #50;
    
    // 测试1: 纯循线控制（无障碍）
    $display("【测试组1: 纯循线控制】");
    $display("=== 全速前进循线 ===");
    test_control_scenario(3'b000, 3'b000);
    $display("=== 缓慢前进循线 ===");
    test_control_scenario(3'b001, 3'b000);
    $display("=== 缓左转循线 ===");
    test_control_scenario(3'b010, 3'b000);
    $display("=== 急左转循线 ===");
    test_control_scenario(3'b011, 3'b000);
    $display("=== 缓右转循线 ===");
    test_control_scenario(3'b100, 3'b000);
    $display("=== 急右转循线 ===");
    test_control_scenario(3'b101, 3'b000);
    $display("=== 停止循线 ===");
    test_control_scenario(3'b111, 3'b000);

    // 测试2: 避障优先级测试
    $display("【测试组2: 避障优先级测试】");
    $display("=== 循线前进但需急左转避障 ===");
    test_control_scenario(3'b000, 3'b011);
    $display("=== 循线左转但需急右转避障 ===");
    test_control_scenario(3'b010, 3'b101);
    $display("=== 循线右转但需后退避障 ===");
    test_control_scenario(3'b100, 3'b110);
    $display("=== 循线停止但需缓慢前进避障 ===");
    test_control_scenario(3'b111, 3'b001);

    // 测试3: 复杂场景测试
    $display("【测试组3: 复杂场景测试】");
    $display("=== 缓慢前进遇停止障碍 ===");
    test_control_scenario(3'b001, 3'b111);
    $display("=== 急左转遇缓右转障碍 ===");
    test_control_scenario(3'b011, 3'b100);
    $display("=== 后退遇缓左转障碍 ===");
    test_control_scenario(3'b110, 3'b010);

    // 测试4: 边界条件测试
    $display("【测试组4: 边界条件测试】");
    $display("=== 双停止信号 ===");
    test_control_scenario(3'b111, 3'b111);
    $display("=== 双前进信号 ===");
    test_control_scenario(3'b000, 3'b000);
    
    // 测试5: 动态优先级切换
    $display("【测试组5: 动态优先级切换】");
    $display("模拟从循线到避障再回到循线的过程:");
    
    line_control = 3'b000; obstacle_control = 3'b000; #100; // 正常循线
    $display("步骤1: 正常循线前进 -> 最终控制:%b, 电机:%d, 速度:%d", 
             final_control, motor_control, speed_control);
    
    line_control = 3'b010; obstacle_control = 3'b000; #100; // 循线左转
    $display("步骤2: 循线需要左转 -> 最终控制:%b, 电机:%d, 速度:%d", 
             final_control, motor_control, speed_control);
    
    line_control = 3'b010; obstacle_control = 3'b101; #100; // 出现右侧障碍
    $display("步骤3: 出现右侧障碍 -> 最终控制:%b, 电机:%d, 速度:%d", 
             final_control, motor_control, speed_control);
    
    line_control = 3'b010; obstacle_control = 3'b000; #100; // 障碍消失
    $display("步骤4: 障碍消失恢复循线 -> 最终控制:%b, 电机:%d, 速度:%d", 
             final_control, motor_control, speed_control);
    
    // 测试6: 复位功能测试
    $display("【测试组6: 复位功能测试】");
    line_control = 3'b011; obstacle_control = 3'b101; #50;
    $display("复位前状态 -> 最终控制:%b, 电机:%d, 速度:%d", 
             final_control, motor_control, speed_control);
    
    rst_n = 0; #50;
    $display("复位中状态 -> 最终控制:%b, 电机:%d, 速度:%d", 
             final_control, motor_control, speed_control);
    
    rst_n = 1; #50;
    $display("复位后状态 -> 最终控制:%b, 电机:%d, 速度:%d", 
             final_control, motor_control, speed_control);
    
    $display("\n========================================");
    $display("运动决策控制模块测试完成");
    $display("========================================");
    $finish;
end

// 监控最终控制信号变化
always @(final_control) begin
    if ($time > 100) begin
        $display("时间=%0t: 最终控制信号变化为 %b", $time, final_control);
    end
end

// 监控电机控制命令变化
always @(motor_control) begin
    if ($time > 100) begin
        $display("时间=%0t: 电机控制命令变化为 %d", $time, motor_control);
    end
end

// 监控速度控制值变化
always @(speed_control) begin
    if ($time > 100) begin
        $display("时间=%0t: 速度控制值变化为 %d", $time, speed_control);
    end
end

// 生成波形文件
initial begin
    $dumpfile("zhuhoujia14131454_003_tb.vcd");
    $dumpvars(0, zhuhoujia14131454_003_tb);
end

endmodule
