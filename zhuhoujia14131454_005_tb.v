`timescale 1ns / 1ps

module zhuhoujia14131454_005_tb;

// 测试信号定义
reg clk;
reg rst_n;
wire dht11_data;
wire [15:0] temperature;
wire [15:0] humidity;
wire data_valid;

// DHT11数据模拟
reg dht11_data_reg;
reg dht11_oe_sim;
assign dht11_data = dht11_oe_sim ? dht11_data_reg : 1'bz;

// 任务中使用的变量声明
reg [39:0] send_data;
integer i;

// 实例化被测试模块
zhuhoujia14131454_005 uut (
    .clk(clk),
    .rst_n(rst_n),
    .dht11_data(dht11_data),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(data_valid)
);

// 时钟生成 - 50MHz
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// DHT11响应模拟任务
task simulate_dht11_response;
    input [15:0] temp_data;
    input [15:0] humi_data;
    begin
        
        // 组装要发送的数据：湿度高字节+湿度低字节+温度高字节+温度低字节+校验和
        send_data[39:32] = humi_data[15:8];  // 湿度高字节
        send_data[31:24] = humi_data[7:0];   // 湿度低字节
        send_data[23:16] = temp_data[15:8];  // 温度高字节
        send_data[15:8]  = temp_data[7:0];   // 温度低字节
        send_data[7:0]   = send_data[39:32] + send_data[31:24] + send_data[23:16] + send_data[15:8]; // 校验和
        
        $display("模拟DHT11发送数据:");
        $display("  湿度: %d.%d%%", humi_data[15:8], humi_data[7:0]);
        $display("  温度: %d.%d°C", temp_data[15:8], temp_data[7:0]);
        $display("  原始数据: %h", send_data);
        
        // 等待主机发送启动信号（使用延时代替wait）
        #20000; // 等待启动信号
        #5000;  // 等待响应状态
        
        // DHT11响应序列
        dht11_oe_sim = 1;
        dht11_data_reg = 0; // 80us低电平响应
        #1600;
        dht11_data_reg = 1; // 80us高电平响应
        #1600;
        
        // 发送40位数据
        for (i = 39; i >= 0; i = i - 1) begin
            dht11_data_reg = 0; // 50us低电平
            #1000;
            dht11_data_reg = 1; // 数据位：26-28us为0，70us为1
            if (send_data[i])
                #1400; // 发送'1'
            else
                #560;  // 发送'0'
        end
        
        dht11_data_reg = 0; // 结束信号
        #1000;
        dht11_oe_sim = 0;   // 释放总线
    end
endtask

// 状态解码任务
task decode_state;
    input [2:0] state;
    begin
        case (state)
            3'd0: $display("当前状态: IDLE (空闲)");
            3'd1: $display("当前状态: START (启动)");
            3'd2: $display("当前状态: RESPONSE (等待响应)");
            3'd3: $display("当前状态: RECEIVE (接收数据)");
            3'd4: $display("当前状态: FINISH (完成)");
            default: $display("当前状态: UNKNOWN (未知)");
        endcase
    end
endtask

// 主测试序列
initial begin
    // 初始化
    rst_n = 0;
    dht11_data_reg = 1;
    dht11_oe_sim = 0;
    
    $display("========================================");
    $display("DHT11温湿度传感器模块测试开始");
    $display("========================================");
    
    // 释放复位
    #100;
    rst_n = 1;
    #100;
    
    // 测试1: 正常温湿度读取
    $display("【测试1: 正常温湿度读取】");
    // 简化测试，不使用fork-join和wait语句
    simulate_dht11_response(16'h1905, 16'h3C00); // 25.5°C, 60.0%

    // 等待一段时间让数据处理完成
    #10000;

    $display("读取结果:");
    $display("  温度: %d.%d°C (原始值: 0x%04h)",
             temperature[15:8], temperature[7:0], temperature);
    $display("  湿度: %d.%d%% (原始值: 0x%04h)",
             humidity[15:8], humidity[7:0], humidity);
    $display("  数据有效标志: %b", data_valid);

    // 等待下一次测量周期
    #50000;
    $display("\n等待下一次测量周期...");

    // 测试2: 不同温湿度值
    $display("\n【测试2: 不同温湿度值测试】");
    simulate_dht11_response(16'h0F03, 16'h2D08); // 15.3°C, 45.8%

    #10000;

    $display("第二次读取结果:");
    $display("  温度: %d.%d°C", temperature[15:8], temperature[7:0]);
    $display("  湿度: %d.%d%%", humidity[15:8], humidity[7:0]);

    // 测试3: 极端值测试
    #50000;
    $display("\n【测试3: 极端值测试】");
    simulate_dht11_response(16'h0000, 16'h6300); // 0.0°C, 99.0%

    #10000;

    $display("极端值读取结果:");
    $display("  温度: %d.%d°C", temperature[15:8], temperature[7:0]);
    $display("  湿度: %d.%d%%", humidity[15:8], humidity[7:0]);

    // 测试4: 高温测试
    #50000;
    $display("\n【测试4: 高温测试】");
    simulate_dht11_response(16'h3200, 16'h1400); // 50.0°C, 20.0%

    #10000;

    $display("高温读取结果:");
    $display("  温度: %d.%d°C", temperature[15:8], temperature[7:0]);
    $display("  湿度: %d.%d%%", humidity[15:8], humidity[7:0]);
    
    // 测试5: 复位功能测试
    $display("\n【测试5: 复位功能测试】");
    $display("复位前状态:");
    decode_state(uut.state);
    $display("温度: 0x%04h, 湿度: 0x%04h, 有效: %b", temperature, humidity, data_valid);
    
    rst_n = 0;
    #100;
    $display("复位后状态:");
    decode_state(uut.state);
    $display("温度: 0x%04h, 湿度: 0x%04h, 有效: %b", temperature, humidity, data_valid);
    
    rst_n = 1;
    #100;
    
    $display("\n========================================");
    $display("DHT11温湿度传感器模块测试完成");
    $display("========================================");
    $finish;
end

// 状态监控
always @(uut.state) begin
    if ($time > 200) begin
        $display("时间=%0t: DHT11状态变化", $time);
        decode_state(uut.state);
    end
end

// 数据有效标志监控
always @(data_valid) begin
    if ($time > 200) begin
        if (data_valid) begin
            $display("时间=%0t: 数据读取完成", $time);
        end else begin
            $display("时间=%0t: 开始新的测量周期", $time);
        end
    end
end

// 定时器监控（仅在关键状态）
always @(posedge clk) begin
    if ($time > 200 && uut.timer % 10000 == 0) begin
        case (uut.state)
            3'd0: if (uut.timer > 990000) 
                     $display("时间=%0t: IDLE状态，定时器=%d，即将启动", $time, uut.timer);
            3'd1: if (uut.timer > 35000) 
                     $display("时间=%0t: START状态，定时器=%d，即将释放总线", $time, uut.timer);
            3'd3: if (uut.bit_counter < 40) 
                     $display("时间=%0t: RECEIVE状态，已接收%d位", $time, uut.bit_counter);
        endcase
    end
end

// 生成波形文件
initial begin
    $dumpfile("zhuhoujia14131454_005_tb.vcd");
    $dumpvars(0, zhuhoujia14131454_005_tb);
end

endmodule
