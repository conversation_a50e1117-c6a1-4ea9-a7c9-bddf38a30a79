|zhuhoujia2023111413_007
clk_50MHz => clk_50MHz.IN6
rst_n => rst_n.IN6
line_sensor[0] => line_sensor[0].IN1
line_sensor[1] => line_sensor[1].IN1
line_sensor[2] => line_sensor[2].IN1
line_sensor[3] => line_sensor[3].IN1
obstacle_sensor[0] => obstacle_sensor[0].IN1
obstacle_sensor[1] => obstacle_sensor[1].IN1
dht11_data <> zhuhoujia2023111413_005:dht11_sensor.dht11_data
motor_pwm[0] <= zhuhoujia2023111413_004:motor_driver.motor_pwm
motor_pwm[1] <= zhuhoujia2023111413_004:motor_driver.motor_pwm
motor_pwm[2] <= zhuhoujia2023111413_004:motor_driver.motor_pwm
motor_pwm[3] <= zhuhoujia2023111413_004:motor_driver.motor_pwm
motor_dir[0] <= zhuhoujia2023111413_004:motor_driver.motor_dir
motor_dir[1] <= zhuhoujia2023111413_004:motor_driver.motor_dir
motor_dir[2] <= zhuhoujia2023111413_004:motor_driver.motor_dir
motor_dir[3] <= zhuhoujia2023111413_004:motor_driver.motor_dir
clk_1m <= zhuhoujia2023111413_004:motor_driver.clk_1m
clk_100hz <= zhuhoujia2023111413_004:motor_driver.clk_100hz
lcd_rst <= zhuhoujia2023111413_006:lcd_display.lcd_rst
lcd_rw <= zhuhoujia2023111413_006:lcd_display.lcd_rw
lcd_en <= zhuhoujia2023111413_006:lcd_display.lcd_en
lcd_data[0] <= zhuhoujia2023111413_006:lcd_display.lcd_data
lcd_data[1] <= zhuhoujia2023111413_006:lcd_display.lcd_data
lcd_data[2] <= zhuhoujia2023111413_006:lcd_display.lcd_data
lcd_data[3] <= zhuhoujia2023111413_006:lcd_display.lcd_data
lcd_data[4] <= zhuhoujia2023111413_006:lcd_display.lcd_data
lcd_data[5] <= zhuhoujia2023111413_006:lcd_display.lcd_data
lcd_data[6] <= zhuhoujia2023111413_006:lcd_display.lcd_data
lcd_data[7] <= zhuhoujia2023111413_006:lcd_display.lcd_data


|zhuhoujia2023111413_007|zhuhoujia2023111413_001:line_follow
clk => control_signal[0]~reg0.CLK
clk => control_signal[1]~reg0.CLK
clk => control_signal[2]~reg0.CLK
rst_n => control_signal[0]~reg0.PRESET
rst_n => control_signal[1]~reg0.PRESET
rst_n => control_signal[2]~reg0.PRESET
line_sensor[0] => Decoder0.IN3
line_sensor[1] => Decoder0.IN2
line_sensor[2] => Decoder0.IN1
line_sensor[3] => Decoder0.IN0
control_signal[0] <= control_signal[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
control_signal[1] <= control_signal[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
control_signal[2] <= control_signal[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE


|zhuhoujia2023111413_007|zhuhoujia2023111413_002:obstacle_avoid
clk => control_signal[0]~reg0.CLK
clk => control_signal[1]~reg0.CLK
clk => control_signal[2]~reg0.CLK
rst_n => control_signal[0]~reg0.ACLR
rst_n => control_signal[1]~reg0.ACLR
rst_n => control_signal[2]~reg0.ACLR
obstacle_sensor[0] => Decoder0.IN1
obstacle_sensor[0] => control_signal[1]~reg0.DATAIN
obstacle_sensor[1] => Decoder0.IN0
obstacle_sensor[1] => control_signal[2]~reg0.DATAIN
control_signal[0] <= control_signal[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
control_signal[1] <= control_signal[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
control_signal[2] <= control_signal[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE


|zhuhoujia2023111413_007|zhuhoujia2023111413_003:motor_ctrl
clk => speed_control[0]~reg0.CLK
clk => speed_control[1]~reg0.CLK
clk => speed_control[2]~reg0.CLK
clk => speed_control[3]~reg0.CLK
clk => speed_control[4]~reg0.CLK
clk => speed_control[5]~reg0.CLK
clk => speed_control[6]~reg0.CLK
clk => speed_control[7]~reg0.CLK
clk => motor_control[0]~reg0.CLK
clk => motor_control[1]~reg0.CLK
clk => motor_control[2]~reg0.CLK
clk => motor_control[3]~reg0.CLK
clk => final_control[0]~reg0.CLK
clk => final_control[1]~reg0.CLK
clk => final_control[2]~reg0.CLK
rst_n => speed_control[0]~reg0.ACLR
rst_n => speed_control[1]~reg0.ACLR
rst_n => speed_control[2]~reg0.ACLR
rst_n => speed_control[3]~reg0.ACLR
rst_n => speed_control[4]~reg0.ACLR
rst_n => speed_control[5]~reg0.ACLR
rst_n => speed_control[6]~reg0.ACLR
rst_n => speed_control[7]~reg0.ACLR
rst_n => motor_control[0]~reg0.ACLR
rst_n => motor_control[1]~reg0.ACLR
rst_n => motor_control[2]~reg0.ACLR
rst_n => motor_control[3]~reg0.ACLR
rst_n => final_control[0]~reg0.PRESET
rst_n => final_control[1]~reg0.PRESET
rst_n => final_control[2]~reg0.PRESET
line_control[0] => final_control.DATAA
line_control[1] => final_control.DATAA
line_control[2] => final_control.DATAA
obstacle_control[0] => final_control.DATAB
obstacle_control[0] => Equal0.IN2
obstacle_control[1] => final_control.DATAB
obstacle_control[1] => Equal0.IN1
obstacle_control[2] => final_control.DATAB
obstacle_control[2] => Equal0.IN0
final_control[0] <= final_control[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
final_control[1] <= final_control[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
final_control[2] <= final_control[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
motor_control[0] <= motor_control[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
motor_control[1] <= motor_control[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
motor_control[2] <= motor_control[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
motor_control[3] <= motor_control[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
speed_control[0] <= speed_control[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
speed_control[1] <= speed_control[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
speed_control[2] <= speed_control[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
speed_control[3] <= speed_control[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
speed_control[4] <= speed_control[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
speed_control[5] <= speed_control[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
speed_control[6] <= speed_control[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
speed_control[7] <= speed_control[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE


|zhuhoujia2023111413_007|zhuhoujia2023111413_004:motor_driver
clk => motor_dir[0]~reg0.CLK
clk => motor_dir[1]~reg0.CLK
clk => motor_dir[2]~reg0.CLK
clk => motor_dir[3]~reg0.CLK
clk => motor_pwm[0]~reg0.CLK
clk => motor_pwm[1]~reg0.CLK
clk => motor_pwm[2]~reg0.CLK
clk => motor_pwm[3]~reg0.CLK
clk => clk_100hz~reg0.CLK
clk => counter_100hz[0].CLK
clk => counter_100hz[1].CLK
clk => counter_100hz[2].CLK
clk => counter_100hz[3].CLK
clk => counter_100hz[4].CLK
clk => counter_100hz[5].CLK
clk => counter_100hz[6].CLK
clk => counter_100hz[7].CLK
clk => counter_100hz[8].CLK
clk => counter_100hz[9].CLK
clk => counter_100hz[10].CLK
clk => counter_100hz[11].CLK
clk => counter_100hz[12].CLK
clk => counter_100hz[13].CLK
clk => counter_100hz[14].CLK
clk => counter_100hz[15].CLK
clk => counter_100hz[16].CLK
clk => counter_100hz[17].CLK
clk => counter_100hz[18].CLK
clk => counter_100hz[19].CLK
clk => clk_1m~reg0.CLK
clk => counter_1m[0].CLK
clk => counter_1m[1].CLK
clk => counter_1m[2].CLK
clk => counter_1m[3].CLK
clk => counter_1m[4].CLK
clk => counter_1m[5].CLK
clk => counter_1m[6].CLK
clk => counter_1m[7].CLK
clk => counter_1m[8].CLK
clk => counter_1m[9].CLK
clk => counter_1m[10].CLK
clk => counter_1m[11].CLK
clk => counter_1m[12].CLK
clk => counter_1m[13].CLK
clk => counter_1m[14].CLK
clk => counter_1m[15].CLK
clk => counter_1m[16].CLK
clk => counter_1m[17].CLK
clk => counter_1m[18].CLK
clk => counter_1m[19].CLK
clk => counter_1m[20].CLK
clk => counter_1m[21].CLK
clk => counter_1m[22].CLK
clk => counter_1m[23].CLK
clk => counter_1m[24].CLK
clk => pwm_counter[0].CLK
clk => pwm_counter[1].CLK
clk => pwm_counter[2].CLK
clk => pwm_counter[3].CLK
clk => pwm_counter[4].CLK
clk => pwm_counter[5].CLK
clk => pwm_counter[6].CLK
clk => pwm_counter[7].CLK
rst_n => clk_1m~reg0.ACLR
rst_n => counter_1m[0].ACLR
rst_n => counter_1m[1].ACLR
rst_n => counter_1m[2].ACLR
rst_n => counter_1m[3].ACLR
rst_n => counter_1m[4].ACLR
rst_n => counter_1m[5].ACLR
rst_n => counter_1m[6].ACLR
rst_n => counter_1m[7].ACLR
rst_n => counter_1m[8].ACLR
rst_n => counter_1m[9].ACLR
rst_n => counter_1m[10].ACLR
rst_n => counter_1m[11].ACLR
rst_n => counter_1m[12].ACLR
rst_n => counter_1m[13].ACLR
rst_n => counter_1m[14].ACLR
rst_n => counter_1m[15].ACLR
rst_n => counter_1m[16].ACLR
rst_n => counter_1m[17].ACLR
rst_n => counter_1m[18].ACLR
rst_n => counter_1m[19].ACLR
rst_n => counter_1m[20].ACLR
rst_n => counter_1m[21].ACLR
rst_n => counter_1m[22].ACLR
rst_n => counter_1m[23].ACLR
rst_n => counter_1m[24].ACLR
rst_n => motor_dir[0]~reg0.ACLR
rst_n => motor_dir[1]~reg0.ACLR
rst_n => motor_dir[2]~reg0.ACLR
rst_n => motor_dir[3]~reg0.ACLR
rst_n => motor_pwm[0]~reg0.ACLR
rst_n => motor_pwm[1]~reg0.ACLR
rst_n => motor_pwm[2]~reg0.ACLR
rst_n => motor_pwm[3]~reg0.ACLR
rst_n => clk_100hz~reg0.ACLR
rst_n => counter_100hz[0].ACLR
rst_n => counter_100hz[1].ACLR
rst_n => counter_100hz[2].ACLR
rst_n => counter_100hz[3].ACLR
rst_n => counter_100hz[4].ACLR
rst_n => counter_100hz[5].ACLR
rst_n => counter_100hz[6].ACLR
rst_n => counter_100hz[7].ACLR
rst_n => counter_100hz[8].ACLR
rst_n => counter_100hz[9].ACLR
rst_n => counter_100hz[10].ACLR
rst_n => counter_100hz[11].ACLR
rst_n => counter_100hz[12].ACLR
rst_n => counter_100hz[13].ACLR
rst_n => counter_100hz[14].ACLR
rst_n => counter_100hz[15].ACLR
rst_n => counter_100hz[16].ACLR
rst_n => counter_100hz[17].ACLR
rst_n => counter_100hz[18].ACLR
rst_n => counter_100hz[19].ACLR
rst_n => pwm_counter[0].ACLR
rst_n => pwm_counter[1].ACLR
rst_n => pwm_counter[2].ACLR
rst_n => pwm_counter[3].ACLR
rst_n => pwm_counter[4].ACLR
rst_n => pwm_counter[5].ACLR
rst_n => pwm_counter[6].ACLR
rst_n => pwm_counter[7].ACLR
motor_control[0] => Mux0.IN19
motor_control[0] => Mux1.IN19
motor_control[0] => Mux2.IN19
motor_control[0] => Mux3.IN19
motor_control[0] => Decoder0.IN3
motor_control[1] => Mux0.IN18
motor_control[1] => Mux1.IN18
motor_control[1] => Mux2.IN18
motor_control[1] => Mux3.IN18
motor_control[1] => Decoder0.IN2
motor_control[2] => Mux0.IN17
motor_control[2] => Mux1.IN17
motor_control[2] => Mux2.IN17
motor_control[2] => Mux3.IN17
motor_control[2] => Decoder0.IN1
motor_control[3] => Mux0.IN16
motor_control[3] => Mux1.IN16
motor_control[3] => Mux2.IN16
motor_control[3] => Mux3.IN16
motor_control[3] => Decoder0.IN0
speed_control[0] => LessThan2.IN8
speed_control[1] => LessThan2.IN7
speed_control[1] => LessThan3.IN8
speed_control[2] => LessThan2.IN6
speed_control[2] => LessThan3.IN7
speed_control[3] => LessThan2.IN5
speed_control[3] => LessThan3.IN6
speed_control[4] => LessThan2.IN4
speed_control[4] => LessThan3.IN5
speed_control[5] => LessThan2.IN3
speed_control[5] => LessThan3.IN4
speed_control[6] => LessThan2.IN2
speed_control[6] => LessThan3.IN3
speed_control[7] => LessThan2.IN1
speed_control[7] => LessThan3.IN2
motor_pwm[0] <= motor_pwm[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
motor_pwm[1] <= motor_pwm[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
motor_pwm[2] <= motor_pwm[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
motor_pwm[3] <= motor_pwm[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
motor_dir[0] <= motor_dir[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
motor_dir[1] <= motor_dir[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
motor_dir[2] <= motor_dir[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
motor_dir[3] <= motor_dir[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
clk_1m <= clk_1m~reg0.DB_MAX_OUTPUT_PORT_TYPE
clk_100hz <= clk_100hz~reg0.DB_MAX_OUTPUT_PORT_TYPE


|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor
clk => dht11_oe.CLK
clk => dht11_out.CLK
clk => data_valid~reg0.CLK
clk => humidity[0]~reg0.CLK
clk => humidity[1]~reg0.CLK
clk => humidity[2]~reg0.CLK
clk => humidity[3]~reg0.CLK
clk => humidity[4]~reg0.CLK
clk => humidity[5]~reg0.CLK
clk => humidity[6]~reg0.CLK
clk => humidity[7]~reg0.CLK
clk => humidity[8]~reg0.CLK
clk => humidity[9]~reg0.CLK
clk => humidity[10]~reg0.CLK
clk => humidity[11]~reg0.CLK
clk => humidity[12]~reg0.CLK
clk => humidity[13]~reg0.CLK
clk => humidity[14]~reg0.CLK
clk => humidity[15]~reg0.CLK
clk => temperature[0]~reg0.CLK
clk => temperature[1]~reg0.CLK
clk => temperature[2]~reg0.CLK
clk => temperature[3]~reg0.CLK
clk => temperature[4]~reg0.CLK
clk => temperature[5]~reg0.CLK
clk => temperature[6]~reg0.CLK
clk => temperature[7]~reg0.CLK
clk => temperature[8]~reg0.CLK
clk => temperature[9]~reg0.CLK
clk => temperature[10]~reg0.CLK
clk => temperature[11]~reg0.CLK
clk => temperature[12]~reg0.CLK
clk => temperature[13]~reg0.CLK
clk => temperature[14]~reg0.CLK
clk => temperature[15]~reg0.CLK
clk => received_data[8].CLK
clk => received_data[9].CLK
clk => received_data[10].CLK
clk => received_data[11].CLK
clk => received_data[12].CLK
clk => received_data[13].CLK
clk => received_data[14].CLK
clk => received_data[15].CLK
clk => received_data[16].CLK
clk => received_data[17].CLK
clk => received_data[18].CLK
clk => received_data[19].CLK
clk => received_data[20].CLK
clk => received_data[21].CLK
clk => received_data[22].CLK
clk => received_data[23].CLK
clk => received_data[24].CLK
clk => received_data[25].CLK
clk => received_data[26].CLK
clk => received_data[27].CLK
clk => received_data[28].CLK
clk => received_data[29].CLK
clk => received_data[30].CLK
clk => received_data[31].CLK
clk => received_data[32].CLK
clk => received_data[33].CLK
clk => received_data[34].CLK
clk => received_data[35].CLK
clk => received_data[36].CLK
clk => received_data[37].CLK
clk => received_data[38].CLK
clk => received_data[39].CLK
clk => bit_counter[0].CLK
clk => bit_counter[1].CLK
clk => bit_counter[2].CLK
clk => bit_counter[3].CLK
clk => bit_counter[4].CLK
clk => bit_counter[5].CLK
clk => timer[0].CLK
clk => timer[1].CLK
clk => timer[2].CLK
clk => timer[3].CLK
clk => timer[4].CLK
clk => timer[5].CLK
clk => timer[6].CLK
clk => timer[7].CLK
clk => timer[8].CLK
clk => timer[9].CLK
clk => timer[10].CLK
clk => timer[11].CLK
clk => timer[12].CLK
clk => timer[13].CLK
clk => timer[14].CLK
clk => timer[15].CLK
clk => timer[16].CLK
clk => timer[17].CLK
clk => timer[18].CLK
clk => timer[19].CLK
clk => state~6.DATAIN
rst_n => dht11_oe.ACLR
rst_n => dht11_out.PRESET
rst_n => data_valid~reg0.ACLR
rst_n => humidity[0]~reg0.ACLR
rst_n => humidity[1]~reg0.ACLR
rst_n => humidity[2]~reg0.ACLR
rst_n => humidity[3]~reg0.ACLR
rst_n => humidity[4]~reg0.ACLR
rst_n => humidity[5]~reg0.ACLR
rst_n => humidity[6]~reg0.ACLR
rst_n => humidity[7]~reg0.ACLR
rst_n => humidity[8]~reg0.ACLR
rst_n => humidity[9]~reg0.ACLR
rst_n => humidity[10]~reg0.ACLR
rst_n => humidity[11]~reg0.ACLR
rst_n => humidity[12]~reg0.ACLR
rst_n => humidity[13]~reg0.ACLR
rst_n => humidity[14]~reg0.ACLR
rst_n => humidity[15]~reg0.ACLR
rst_n => temperature[0]~reg0.ACLR
rst_n => temperature[1]~reg0.ACLR
rst_n => temperature[2]~reg0.ACLR
rst_n => temperature[3]~reg0.ACLR
rst_n => temperature[4]~reg0.ACLR
rst_n => temperature[5]~reg0.ACLR
rst_n => temperature[6]~reg0.ACLR
rst_n => temperature[7]~reg0.ACLR
rst_n => temperature[8]~reg0.ACLR
rst_n => temperature[9]~reg0.ACLR
rst_n => temperature[10]~reg0.ACLR
rst_n => temperature[11]~reg0.ACLR
rst_n => temperature[12]~reg0.ACLR
rst_n => temperature[13]~reg0.ACLR
rst_n => temperature[14]~reg0.ACLR
rst_n => temperature[15]~reg0.ACLR
rst_n => received_data[8].ACLR
rst_n => received_data[9].ACLR
rst_n => received_data[10].ACLR
rst_n => received_data[11].ACLR
rst_n => received_data[12].ACLR
rst_n => received_data[13].ACLR
rst_n => received_data[14].ACLR
rst_n => received_data[15].ACLR
rst_n => received_data[16].ACLR
rst_n => received_data[17].ACLR
rst_n => received_data[18].ACLR
rst_n => received_data[19].ACLR
rst_n => received_data[20].ACLR
rst_n => received_data[21].ACLR
rst_n => received_data[22].ACLR
rst_n => received_data[23].ACLR
rst_n => received_data[24].ACLR
rst_n => received_data[25].ACLR
rst_n => received_data[26].ACLR
rst_n => received_data[27].ACLR
rst_n => received_data[28].ACLR
rst_n => received_data[29].ACLR
rst_n => received_data[30].ACLR
rst_n => received_data[31].ACLR
rst_n => received_data[32].ACLR
rst_n => received_data[33].ACLR
rst_n => received_data[34].ACLR
rst_n => received_data[35].ACLR
rst_n => received_data[36].ACLR
rst_n => received_data[37].ACLR
rst_n => received_data[38].ACLR
rst_n => received_data[39].ACLR
rst_n => bit_counter[0].ACLR
rst_n => bit_counter[1].ACLR
rst_n => bit_counter[2].ACLR
rst_n => bit_counter[3].ACLR
rst_n => bit_counter[4].ACLR
rst_n => bit_counter[5].ACLR
rst_n => timer[0].ACLR
rst_n => timer[1].ACLR
rst_n => timer[2].ACLR
rst_n => timer[3].ACLR
rst_n => timer[4].ACLR
rst_n => timer[5].ACLR
rst_n => timer[6].ACLR
rst_n => timer[7].ACLR
rst_n => timer[8].ACLR
rst_n => timer[9].ACLR
rst_n => timer[10].ACLR
rst_n => timer[11].ACLR
rst_n => timer[12].ACLR
rst_n => timer[13].ACLR
rst_n => timer[14].ACLR
rst_n => timer[15].ACLR
rst_n => timer[16].ACLR
rst_n => timer[17].ACLR
rst_n => timer[18].ACLR
rst_n => timer[19].ACLR
rst_n => state~8.DATAIN
dht11_data <> dht11_data
temperature[0] <= temperature[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[1] <= temperature[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[2] <= temperature[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[3] <= temperature[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[4] <= temperature[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[5] <= temperature[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[6] <= temperature[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[7] <= temperature[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[8] <= temperature[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[9] <= temperature[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[10] <= temperature[10]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[11] <= temperature[11]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[12] <= temperature[12]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[13] <= temperature[13]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[14] <= temperature[14]~reg0.DB_MAX_OUTPUT_PORT_TYPE
temperature[15] <= temperature[15]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[0] <= humidity[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[1] <= humidity[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[2] <= humidity[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[3] <= humidity[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[4] <= humidity[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[5] <= humidity[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[6] <= humidity[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[7] <= humidity[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[8] <= humidity[8]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[9] <= humidity[9]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[10] <= humidity[10]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[11] <= humidity[11]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[12] <= humidity[12]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[13] <= humidity[13]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[14] <= humidity[14]~reg0.DB_MAX_OUTPUT_PORT_TYPE
humidity[15] <= humidity[15]~reg0.DB_MAX_OUTPUT_PORT_TYPE
data_valid <= data_valid~reg0.DB_MAX_OUTPUT_PORT_TYPE


|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display
clk => char_index[0].CLK
clk => char_index[1].CLK
clk => char_index[2].CLK
clk => delay_counter[0].CLK
clk => delay_counter[1].CLK
clk => delay_counter[2].CLK
clk => delay_counter[3].CLK
clk => delay_counter[4].CLK
clk => delay_counter[5].CLK
clk => delay_counter[6].CLK
clk => delay_counter[7].CLK
clk => delay_counter[8].CLK
clk => delay_counter[9].CLK
clk => delay_counter[10].CLK
clk => delay_counter[11].CLK
clk => delay_counter[12].CLK
clk => delay_counter[13].CLK
clk => delay_counter[14].CLK
clk => delay_counter[15].CLK
clk => lcd_data[0]~reg0.CLK
clk => lcd_data[1]~reg0.CLK
clk => lcd_data[2]~reg0.CLK
clk => lcd_data[3]~reg0.CLK
clk => lcd_data[4]~reg0.CLK
clk => lcd_data[5]~reg0.CLK
clk => lcd_data[6]~reg0.CLK
clk => lcd_data[7]~reg0.CLK
clk => lcd_en~reg0.CLK
clk => lcd_rst~reg0.CLK
clk => state~5.DATAIN
rst_n => char_index[0].ACLR
rst_n => char_index[1].ACLR
rst_n => char_index[2].ACLR
rst_n => delay_counter[0].ACLR
rst_n => delay_counter[1].ACLR
rst_n => delay_counter[2].ACLR
rst_n => delay_counter[3].ACLR
rst_n => delay_counter[4].ACLR
rst_n => delay_counter[5].ACLR
rst_n => delay_counter[6].ACLR
rst_n => delay_counter[7].ACLR
rst_n => delay_counter[8].ACLR
rst_n => delay_counter[9].ACLR
rst_n => delay_counter[10].ACLR
rst_n => delay_counter[11].ACLR
rst_n => delay_counter[12].ACLR
rst_n => delay_counter[13].ACLR
rst_n => delay_counter[14].ACLR
rst_n => delay_counter[15].ACLR
rst_n => lcd_data[0]~reg0.ACLR
rst_n => lcd_data[1]~reg0.ACLR
rst_n => lcd_data[2]~reg0.ACLR
rst_n => lcd_data[3]~reg0.ACLR
rst_n => lcd_data[4]~reg0.ACLR
rst_n => lcd_data[5]~reg0.ACLR
rst_n => lcd_data[6]~reg0.ACLR
rst_n => lcd_data[7]~reg0.ACLR
rst_n => lcd_en~reg0.ACLR
rst_n => lcd_rst~reg0.ACLR
rst_n => state~7.DATAIN
temperature[0] => ~NO_FANOUT~
temperature[1] => ~NO_FANOUT~
temperature[2] => ~NO_FANOUT~
temperature[3] => ~NO_FANOUT~
temperature[4] => ~NO_FANOUT~
temperature[5] => ~NO_FANOUT~
temperature[6] => ~NO_FANOUT~
temperature[7] => ~NO_FANOUT~
temperature[8] => ~NO_FANOUT~
temperature[9] => ~NO_FANOUT~
temperature[10] => ~NO_FANOUT~
temperature[11] => ~NO_FANOUT~
temperature[12] => ~NO_FANOUT~
temperature[13] => ~NO_FANOUT~
temperature[14] => ~NO_FANOUT~
temperature[15] => ~NO_FANOUT~
humidity[0] => ~NO_FANOUT~
humidity[1] => ~NO_FANOUT~
humidity[2] => ~NO_FANOUT~
humidity[3] => ~NO_FANOUT~
humidity[4] => ~NO_FANOUT~
humidity[5] => ~NO_FANOUT~
humidity[6] => ~NO_FANOUT~
humidity[7] => ~NO_FANOUT~
humidity[8] => ~NO_FANOUT~
humidity[9] => ~NO_FANOUT~
humidity[10] => ~NO_FANOUT~
humidity[11] => ~NO_FANOUT~
humidity[12] => ~NO_FANOUT~
humidity[13] => ~NO_FANOUT~
humidity[14] => ~NO_FANOUT~
humidity[15] => ~NO_FANOUT~
data_valid => ~NO_FANOUT~
lcd_rst <= lcd_rst~reg0.DB_MAX_OUTPUT_PORT_TYPE
lcd_rw <= <GND>
lcd_en <= lcd_en~reg0.DB_MAX_OUTPUT_PORT_TYPE
lcd_data[0] <= lcd_data[0]~reg0.DB_MAX_OUTPUT_PORT_TYPE
lcd_data[1] <= lcd_data[1]~reg0.DB_MAX_OUTPUT_PORT_TYPE
lcd_data[2] <= lcd_data[2]~reg0.DB_MAX_OUTPUT_PORT_TYPE
lcd_data[3] <= lcd_data[3]~reg0.DB_MAX_OUTPUT_PORT_TYPE
lcd_data[4] <= lcd_data[4]~reg0.DB_MAX_OUTPUT_PORT_TYPE
lcd_data[5] <= lcd_data[5]~reg0.DB_MAX_OUTPUT_PORT_TYPE
lcd_data[6] <= lcd_data[6]~reg0.DB_MAX_OUTPUT_PORT_TYPE
lcd_data[7] <= lcd_data[7]~reg0.DB_MAX_OUTPUT_PORT_TYPE


