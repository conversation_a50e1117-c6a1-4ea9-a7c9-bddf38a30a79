module zhuhoujia2023111413_006(
    input clk,
    input rst_n,
    input [15:0] temperature,    // 保留接口兼容性
    input [15:0] humidity,       // 保留接口兼容性
    input data_valid,            // 保留接口兼容性
    output reg lcd_rst,
    output reg lcd_rw,
    output reg lcd_en,
    output reg [7:0] lcd_data
);

reg [3:0] state;
reg [15:0] delay_counter;
reg [2:0] char_index;

// LCD状态定义
parameter RESET = 4'd0;
parameter INIT = 4'd1;
parameter DISPLAY_2023 = 4'd2;
parameter IDLE = 4'd3;

// "2023"字符的ASCII码
parameter CHAR_2 = 8'h32;  // '2' = 0x32
parameter CHAR_0 = 8'h30;  // '0' = 0x30
parameter CHAR_3 = 8'h33;  // '3' = 0x33

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        state <= RESET;
        lcd_rst <= 1'b0;
        lcd_rw <= 1'b0;
        lcd_en <= 1'b0;
        lcd_data <= 8'h00;
        delay_counter <= 16'd0;
        char_index <= 3'd0;
    end else begin
        case (state)
            RESET: begin
                if (delay_counter > 16'd1000) begin
                    lcd_rst <= 1'b1;
                    state <= INIT;
                    delay_counter <= 16'd0;
                end else begin
                    delay_counter <= delay_counter + 1;
                end
            end

            INIT: begin
                // LCD初始化序列
                if (delay_counter > 16'd500) begin
                    state <= DISPLAY_2023;
                    delay_counter <= 16'd0;
                    char_index <= 3'd0;
                end else begin
                    delay_counter <= delay_counter + 1;
                    // 简化的初始化命令
                    lcd_data <= 8'h38;  // 8位数据，2行显示
                    lcd_en <= ~lcd_en;
                end
            end

            DISPLAY_2023: begin
                if (delay_counter > 16'd1000) begin  // 每个字符显示间隔
                    case (char_index)
                        3'd0: lcd_data <= CHAR_2;  // 显示 '2'
                        3'd1: lcd_data <= CHAR_0;  // 显示 '0'
                        3'd2: lcd_data <= CHAR_2;  // 显示 '2'
                        3'd3: lcd_data <= CHAR_3;  // 显示 '3'
                        default: lcd_data <= CHAR_2;
                    endcase

                    lcd_en <= ~lcd_en;  // 切换使能信号

                    if (char_index < 3'd3) begin
                        char_index <= char_index + 1;
                    end else begin
                        char_index <= 3'd0;  // 循环显示
                    end

                    delay_counter <= 16'd0;
                end else begin
                    delay_counter <= delay_counter + 1;
                end
            end

            IDLE: begin
                state <= DISPLAY_2023;  // 持续显示2023
            end

            default: begin
                state <= RESET;
            end
        endcase
    end
end

endmodule
