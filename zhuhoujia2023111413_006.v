module zhuhoujia2023111413_006(
    input clk,
    input rst_n,
    input [15:0] temperature,
    input [15:0] humidity,
    input data_valid,
    output reg lcd_rst,
    output reg lcd_rw,
    output reg lcd_en,
    output reg [7:0] lcd_data
);

reg [3:0] state;
reg [15:0] delay_counter;
reg [7:0] char_counter;

parameter RESET = 4'd0;
parameter INIT = 4'd1;
parameter DISPLAY_TEMP = 4'd2;
parameter DISPLAY_HUM = 4'd3;
parameter IDLE = 4'd4;

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        state <= RESET;
        lcd_rst <= 1'b0;
        lcd_rw <= 1'b0;
        lcd_en <= 1'b0;
        lcd_data <= 8'h00;
        delay_counter <= 16'd0;
        char_counter <= 8'd0;
    end else begin
        case (state)
            RESET: begin
                if (delay_counter > 16'd1000) begin
                    lcd_rst <= 1'b1;
                    state <= INIT;
                    delay_counter <= 16'd0;
                end else begin
                    delay_counter <= delay_counter + 1;
                end
            end
            
            INIT: begin
                // LCD初始化序列
                if (delay_counter > 16'd500) begin
                    state <= DISPLAY_TEMP;
                    delay_counter <= 16'd0;
                end else begin
                    delay_counter <= delay_counter + 1;
                    // 简化的初始化命令
                    lcd_data <= 8'h38;  // 8位数据，2行显示
                    lcd_en <= ~lcd_en;
                end
            end
            
            DISPLAY_TEMP: begin
                if (data_valid) begin
                    // 显示温度数据
                    lcd_data <= temperature[7:0];
                    lcd_en <= ~lcd_en;
                    state <= DISPLAY_HUM;
                end
            end
            
            DISPLAY_HUM: begin
                if (data_valid) begin
                    // 显示湿度数据
                    lcd_data <= humidity[7:0];
                    lcd_en <= ~lcd_en;
                    state <= IDLE;
                end
            end
            
            IDLE: begin
                state <= DISPLAY_TEMP;
            end
        endcase
    end
end

endmodule
