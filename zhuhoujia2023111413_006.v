module zhuhoujia2023111413_006(
    input clk,
    input rst_n,
    input [15:0] temperature,    // 保留接口兼容性
    input [15:0] humidity,       // 保留接口兼容性
    input data_valid,            // 保留接口兼容性
    output reg lcd_rst,
    output reg lcd_rw,
    output reg lcd_en,
    output reg [7:0] lcd_data
);

reg [3:0] state;
reg [15:0] delay_counter;
reg [3:0] char_index;  // 4位支持10个字符（0-9）

// LCD状态定义
parameter RESET = 4'd0;
parameter INIT = 4'd1;
parameter DISPLAY_TEXT = 4'd2;
parameter IDLE = 4'd3;

// 定义要显示的字符串 "2023111413"
reg [7:0] display_text [0:9];  // 10个字符的存储空间

// 初始化显示文本
initial begin
    display_text[0] = 8'h32;  // '2'
    display_text[1] = 8'h30;  // '0'
    display_text[2] = 8'h32;  // '2'
    display_text[3] = 8'h33;  // '3'
    display_text[4] = 8'h31;  // '1'
    display_text[5] = 8'h31;  // '1'
    display_text[6] = 8'h31;  // '1'
    display_text[7] = 8'h34;  // '4'
    display_text[8] = 8'h31;  // '1'
    display_text[9] = 8'h33;  // '3'
end

parameter TEXT_LENGTH = 10;  // 文本总长度

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        state <= RESET;
        lcd_rst <= 1'b0;
        lcd_rw <= 1'b0;
        lcd_en <= 1'b0;
        lcd_data <= 8'h00;
        delay_counter <= 16'd0;
        char_index <= 4'd0;
    end else begin
        case (state)
            RESET: begin
                if (delay_counter > 16'd1000) begin
                    lcd_rst <= 1'b1;
                    state <= INIT;
                    delay_counter <= 16'd0;
                end else begin
                    delay_counter <= delay_counter + 1;
                end
            end

            INIT: begin
                // LCD初始化序列
                if (delay_counter > 16'd500) begin
                    state <= DISPLAY_TEXT;
                    delay_counter <= 16'd0;
                    char_index <= 4'd0;
                end else begin
                    delay_counter <= delay_counter + 1;
                    // 简化的初始化命令
                    lcd_data <= 8'h38;  // 8位数据，2行显示
                    lcd_en <= ~lcd_en;
                end
            end

            DISPLAY_TEXT: begin
                if (delay_counter > 16'd2000) begin  // 每个字符显示间隔（增加间隔以便观察）
                    // 从ROM中读取当前字符
                    lcd_data <= display_text[char_index];

                    lcd_en <= ~lcd_en;  // 切换使能信号

                    // 移动到下一个字符
                    if (char_index < (TEXT_LENGTH - 1)) begin
                        char_index <= char_index + 1;
                    end else begin
                        char_index <= 4'd0;  // 循环显示
                        state <= IDLE;       // 显示完一轮后进入空闲状态
                    end

                    delay_counter <= 16'd0;
                end else begin
                    delay_counter <= delay_counter + 1;
                end
            end

            IDLE: begin
                if (delay_counter > 16'd10000) begin  // 空闲一段时间后重新开始显示
                    state <= DISPLAY_TEXT;
                    char_index <= 4'd0;
                    delay_counter <= 16'd0;
                end else begin
                    delay_counter <= delay_counter + 1;
                end
            end

            default: begin
                state <= RESET;
            end
        endcase
    end
end

endmodule
