`timescale 1ns / 1ps

module zhuhoujia2023111413_002_tb;

// 测试信号定义
reg clk;
reg rst_n;
reg [1:0] obstacle_sensor;
wire [1:0] control_signal;

// 实例化被测试模块 - 避障控制模块
zhuhoujia2023111413_002 uut (
    .clk(clk),
    .rst_n(rst_n),
    .obstacle_sensor(obstacle_sensor),
    .control_signal(control_signal)
);

// 时钟生成 - 50MHz时钟
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// 测试序列
initial begin
    // 初始化
    rst_n = 0;
    obstacle_sensor = 2'b00;
    
    #100;
    rst_n = 1;
    #20;
    
    // 测试用例1：无障碍物，正常前进
    obstacle_sensor = 2'b00;
    #40;
    $display("测试1 - 无障碍: obstacle_sensor=%b, control_signal=%b (期望:00-前进)", obstacle_sensor, control_signal);
    
    // 测试用例2：右侧有障碍，左转避障
    obstacle_sensor = 2'b01;
    #40;
    $display("测试2 - 右侧障碍: obstacle_sensor=%b, control_signal=%b (期望:01-左转)", obstacle_sensor, control_signal);
    
    // 测试用例3：左侧有障碍，右转避障
    obstacle_sensor = 2'b10;
    #40;
    $display("测试3 - 左侧障碍: obstacle_sensor=%b, control_signal=%b (期望:10-右转)", obstacle_sensor, control_signal);
    
    // 测试用例4：前方有障碍，后退
    obstacle_sensor = 2'b11;
    #40;
    $display("测试4 - 前方障碍: obstacle_sensor=%b, control_signal=%b (期望:11-后退)", obstacle_sensor, control_signal);
    
    // 测试复位功能
    rst_n = 0;
    #40;
    $display("测试5 - 复位状态: control_signal=%b (期望:00-前进)", control_signal);
    
    $display("避障模块测试完成！");
    $finish;
end

initial begin
    $dumpfile("zhuhoujia2023111413_002_tb.vcd");
    $dumpvars(0, zhuhoujia2023111413_002_tb);
end

endmodule