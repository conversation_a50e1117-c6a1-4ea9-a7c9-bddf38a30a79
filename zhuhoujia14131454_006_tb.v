`timescale 1ns / 1ps

module zhuhoujia14131454_006_tb;

// 测试信号定义
reg clk;
reg rst_n;
reg [15:0] temperature;
reg [15:0] humidity;
reg data_valid;
wire lcd_rst;
wire lcd_rw;
wire lcd_en;
wire [7:0] lcd_data;

// 计数器变量声明
integer char_count;
integer i;
integer prev_char_index;
integer cycle_count;
integer en_toggle_count;
reg prev_lcd_en;

// 实例化被测试模块
zhuhoujia14131454_006 uut (
    .clk(clk),
    .rst_n(rst_n),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(data_valid),
    .lcd_rst(lcd_rst),
    .lcd_rw(lcd_rw),
    .lcd_en(lcd_en),
    .lcd_data(lcd_data)
);

// 时钟生成 - 50MHz
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// 状态解码任务
task decode_state;
    input [3:0] state;
    begin
        case (state)
            4'd0: $display("当前状态: RESET (复位)");
            4'd1: $display("当前状态: INIT (初始化)");
            4'd2: $display("当前状态: DISPLAY_TEXT (显示文本)");
            4'd3: $display("当前状态: IDLE (空闲)");
            default: $display("当前状态: UNKNOWN (未知)");
        endcase
    end
endtask

// 字符显示监控任务
task monitor_character_display;
    begin
        if (lcd_data >= 8'h20 && lcd_data <= 8'h7E) begin // 可打印ASCII字符
            $display("显示字符[%d]: '%c' (ASCII=0x%02h)", uut.char_index, lcd_data, lcd_data);
        end else begin
            $display("显示数据[%d]: 0x%02h (非字符)", uut.char_index, lcd_data);
        end
    end
endtask

// 主测试序列
initial begin
    // 初始化
    rst_n = 0;
    temperature = 16'h1905;  // 25.5°C (保持兼容性)
    humidity = 16'h3C00;     // 60.0%
    data_valid = 1'b1;
    
    $display("========================================");
    $display("LCD显示模块测试开始");
    $display("目标显示字符串: \"2023111413\"");
    $display("========================================");
    
    // 释放复位
    #200;
    rst_n = 1;
    #200;
    
    $display("【测试1: LCD初始化过程】");
    decode_state(uut.state);
    $display("LCD复位信号: %b", lcd_rst);
    $display("LCD读写信号: %b", lcd_rw);
    $display("LCD使能信号: %b", lcd_en);
    $display("LCD数据: 0x%02h", lcd_data);
    
    // 等待初始化完成
    wait(uut.state == uut.DISPLAY_TEXT);
    #100;
    $display("\nLCD初始化完成，开始显示文本");
    decode_state(uut.state);
    
    // 测试2: 监控完整的字符显示过程
    $display("\n【测试2: 字符显示过程监控】");
    $display("监控\"2023111413\"的逐字符显示:");
    
    // 监控一个完整的显示周期
    char_count = 0;
    while (char_count < 10) begin
        @(posedge clk);
        if (uut.delay_counter == 16'd0 && uut.state == uut.DISPLAY_TEXT) begin
            monitor_character_display();
            char_count = char_count + 1;
        end
    end
    
    // 测试3: 验证字符串内容
    $display("\n【测试3: 验证显示字符串内容】");
    $display("预期字符串: \"2023111413\"");
    $write("实际字符串: \"");
    for (i = 0; i < 10; i = i + 1) begin
        $write("%c", uut.display_text[i]);
    end
    $display("\"");
    
    // 验证每个字符
    reg [7:0] expected_chars [0:9];
    expected_chars[0] = 8'h32; // '2'
    expected_chars[1] = 8'h30; // '0'
    expected_chars[2] = 8'h32; // '2'
    expected_chars[3] = 8'h33; // '3'
    expected_chars[4] = 8'h31; // '1'
    expected_chars[5] = 8'h31; // '1'
    expected_chars[6] = 8'h31; // '1'
    expected_chars[7] = 8'h34; // '4'
    expected_chars[8] = 8'h31; // '1'
    expected_chars[9] = 8'h33; // '3'
    
    reg verification_passed = 1;
    for (i = 0; i < 10; i = i + 1) begin
        if (uut.display_text[i] != expected_chars[i]) begin
            $display("错误: 字符[%d] 期望=0x%02h('%c'), 实际=0x%02h('%c')", 
                     i, expected_chars[i], expected_chars[i], 
                     uut.display_text[i], uut.display_text[i]);
            verification_passed = 0;
        end
    end
    
    if (verification_passed) begin
        $display("✓ 字符串验证通过！");
    end else begin
        $display("✗ 字符串验证失败！");
    end
    
    // 测试4: 状态机转换测试
    $display("\n【测试4: 状态机转换测试】");
    
    // 等待进入IDLE状态
    wait(uut.state == uut.IDLE);
    $display("进入IDLE状态");
    decode_state(uut.state);
    
    // 等待重新开始显示
    wait(uut.state == uut.DISPLAY_TEXT);
    $display("重新开始显示文本");
    decode_state(uut.state);
    
    // 测试5: 循环显示测试
    $display("\n【测试5: 循环显示测试】");
    $display("验证字符索引的循环:");
    
    // 观察字符索引的变化
    prev_char_index = uut.char_index;
    cycle_count = 0;
    
    repeat(25) begin // 观察2.5个显示周期
        @(posedge clk);
        if (uut.char_index != prev_char_index) begin
            $display("字符索引变化: %d -> %d", prev_char_index, uut.char_index);
            if (prev_char_index == 9 && uut.char_index == 0) begin
                cycle_count = cycle_count + 1;
                $display("完成第%d个显示周期", cycle_count);
            end
            prev_char_index = uut.char_index;
        end
    end
    
    // 测试6: LCD控制信号测试
    $display("\n【测试6: LCD控制信号测试】");
    
    // 监控使能信号切换
    prev_lcd_en = lcd_en;
    en_toggle_count = 0;
    
    repeat(1000) begin
        @(posedge clk);
        if (lcd_en != prev_lcd_en) begin
            en_toggle_count = en_toggle_count + 1;
            prev_lcd_en = lcd_en;
        end
    end
    
    $display("LCD使能信号切换次数: %d", en_toggle_count);
    $display("当前LCD控制信号状态:");
    $display("  复位信号: %b", lcd_rst);
    $display("  读写信号: %b (0=写, 1=读)", lcd_rw);
    $display("  使能信号: %b", lcd_en);
    $display("  数据总线: 0x%02h", lcd_data);
    
    // 测试7: 复位功能测试
    $display("\n【测试7: 复位功能测试】");
    
    $display("复位前状态:");
    decode_state(uut.state);
    $display("字符索引: %d", uut.char_index);
    $display("延时计数器: %d", uut.delay_counter);
    
    rst_n = 0;
    #100;
    
    $display("复位中状态:");
    decode_state(uut.state);
    $display("LCD复位信号: %b", lcd_rst);
    $display("字符索引: %d", uut.char_index);
    $display("延时计数器: %d", uut.delay_counter);
    
    rst_n = 1;
    #100;
    
    $display("复位后状态:");
    decode_state(uut.state);
    
    $display("\n========================================");
    $display("LCD显示模块测试完成");
    $display("========================================");
    $finish;
end

// 状态变化监控
always @(uut.state) begin
    if ($time > 400) begin
        $display("时间=%0t: LCD状态变化", $time);
        decode_state(uut.state);
    end
end

// 字符显示监控
always @(lcd_data) begin
    if ($time > 400 && uut.state == uut.DISPLAY_TEXT) begin
        if (lcd_data >= 8'h30 && lcd_data <= 8'h39) begin // 数字字符
            $display("时间=%0t: 显示数字字符 '%c'", $time, lcd_data);
        end
    end
end

// LCD使能信号监控
always @(lcd_en) begin
    if ($time > 400) begin
        $display("时间=%0t: LCD使能信号切换到 %b", $time, lcd_en);
    end
end

// 生成波形文件
initial begin
    $dumpfile("zhuhoujia14131454_006_tb.vcd");
    $dumpvars(0, zhuhoujia14131454_006_tb);
end

endmodule
