module zhuhoujia2023111413_002(
    input clk,
    input rst_n,
    input [1:0] obstacle_sensor,
    output reg [2:0] control_signal  // 扩展为3位
);

// 控制信号定义
parameter FORWARD_FULL  = 3'b000;  // 全速前进
parameter FORWARD_SLOW  = 3'b001;  // 缓慢前进
parameter LEFT_SLOW     = 3'b010;  // 缓左转
parameter LEFT_FAST     = 3'b011;  // 急左转
parameter RIGHT_SLOW    = 3'b100;  // 缓右转
parameter RIGHT_FAST    = 3'b101;  // 急右转
parameter BACK_FULL     = 3'b110;  // 全速倒车
parameter STOP          = 3'b111;  // 停止

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        control_signal <= FORWARD_FULL;
    end else begin
        case (obstacle_sensor)
            2'b00: control_signal <= FORWARD_FULL;  // 无障碍，全速前进
            2'b01: control_signal <= LEFT_FAST;     // 右侧障碍，急左转
            2'b10: control_signal <= RIGHT_FAST;    // 左侧障碍，急右转
            2'b11: control_signal <= BACK_FULL;     // 前方障碍，全速后退
            default: control_signal <= FORWARD_FULL;
        endcase
    end
end

endmodule
