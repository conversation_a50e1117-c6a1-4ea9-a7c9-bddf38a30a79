module zhuhoujia2023111413_002(
    input clk,
    input rst_n,
    input [1:0] obstacle_sensor,
    output reg [2:0] control_signal  // 扩展为3位
);

// 避障传感器滤波和防抖动
reg [1:0] obs_reg1, obs_reg2, obs_reg3;
reg [1:0] obs_filtered;
reg [15:0] obs_debounce_counter;

// 避障传感器数据滤波
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        obs_reg1 <= 2'b00;
        obs_reg2 <= 2'b00;
        obs_reg3 <= 2'b00;
        obs_filtered <= 2'b00;
        obs_debounce_counter <= 16'd0;
    end else begin
        obs_reg1 <= obstacle_sensor;
        obs_reg2 <= obs_reg1;
        obs_reg3 <= obs_reg2;

        // 多数表决滤波
        if ((obs_reg1 == obs_reg2) && (obs_reg2 == obs_reg3)) begin
            if (obs_debounce_counter >= 16'd500) begin  // 避障需要更快响应
                obs_filtered <= obs_reg3;
                obs_debounce_counter <= 16'd0;
            end else begin
                obs_debounce_counter <= obs_debounce_counter + 1;
            end
        end else begin
            obs_debounce_counter <= 16'd0;
        end
    end
end

// 控制信号定义
parameter FORWARD_FULL  = 3'b000;  // 全速前进
parameter FORWARD_SLOW  = 3'b001;  // 缓慢前进
parameter LEFT_SLOW     = 3'b010;  // 缓左转
parameter LEFT_FAST     = 3'b011;  // 急左转
parameter RIGHT_SLOW    = 3'b100;  // 缓右转
parameter RIGHT_FAST    = 3'b101;  // 急右转
parameter BACK_FULL     = 3'b110;  // 全速倒车
parameter STOP          = 3'b111;  // 停止

// 将3位控制信号转换为2位输出以兼容现有接口
reg [1:0] obstacle_output;

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        control_signal <= FORWARD_FULL;
        obstacle_output <= 2'b00;
    end else begin
        case (obs_filtered)  // 使用滤波后的传感器数据
            2'b00: begin
                control_signal <= FORWARD_FULL;  // 无障碍，全速前进
                obstacle_output <= 2'b00;
            end
            2'b01: begin
                control_signal <= LEFT_FAST;     // 右侧障碍，急左转
                obstacle_output <= 2'b01;
            end
            2'b10: begin
                control_signal <= RIGHT_FAST;    // 左侧障碍，急右转
                obstacle_output <= 2'b10;
            end
            2'b11: begin
                control_signal <= BACK_FULL;     // 前方障碍，全速后退
                obstacle_output <= 2'b11;
            end
            default: begin
                control_signal <= FORWARD_FULL;
                obstacle_output <= 2'b00;
            end
        endcase
    end
end

endmodule
