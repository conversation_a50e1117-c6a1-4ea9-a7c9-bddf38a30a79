Analysis & Synthesis report for zhuhoujia2023111413
Sat Jul 19 00:36:53 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Analysis & Synthesis Summary
  3. Analysis & Synthesis Settings
  4. Parallel Compilation
  5. Analysis & Synthesis Source Files Read
  6. Analysis & Synthesis Resource Usage Summary
  7. Analysis & Synthesis Resource Utilization by Entity
  8. State Machine - |zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display|state
  9. State Machine - |zhuhoujia14131454_007|zhuhoujia14131454_005:dht11_sensor|state
 10. Registers Removed During Synthesis
 11. General Register Statistics
 12. Inverted Register Statistics
 13. Multiplexer Restructuring Statistics (Restructuring Performed)
 14. Parameter Settings for User Entity Instance: zhuhoujia14131454_001:line_follow
 15. Parameter Settings for User Entity Instance: zhuhoujia14131454_002:obstacle_avoid
 16. Parameter Settings for User Entity Instance: zhuhoujia14131454_003:motor_ctrl
 17. Parameter Settings for User Entity Instance: zhuhoujia14131454_005:dht11_sensor
 18. Parameter Settings for User Entity Instance: zhuhoujia14131454_006:lcd_display
 19. Port Connectivity Checks: "zhuhoujia14131454_003:motor_ctrl"
 20. Post-Synthesis Netlist Statistics for Top Partition
 21. Elapsed Time Per Partition
 22. Analysis & Synthesis Messages
 23. Analysis & Synthesis Suppressed Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+-------------------------------------------------------------------------------+
; Analysis & Synthesis Summary                                                  ;
+---------------------------------+---------------------------------------------+
; Analysis & Synthesis Status     ; Successful - Sat Jul 19 00:36:53 2025       ;
; Quartus Prime Version           ; 18.1.0 Build 625 09/12/2018 SJ Lite Edition ;
; Revision Name                   ; zhuhoujia2023111413                         ;
; Top-level Entity Name           ; zhuhoujia14131454_007                       ;
; Family                          ; Cyclone V                                   ;
; Logic utilization (in ALMs)     ; N/A                                         ;
; Total registers                 ; 148                                         ;
; Total pins                      ; 30                                          ;
; Total virtual pins              ; 0                                           ;
; Total block memory bits         ; 0                                           ;
; Total DSP Blocks                ; 0                                           ;
; Total HSSI RX PCSs              ; 0                                           ;
; Total HSSI PMA RX Deserializers ; 0                                           ;
; Total HSSI TX PCSs              ; 0                                           ;
; Total HSSI PMA TX Serializers   ; 0                                           ;
; Total PLLs                      ; 0                                           ;
; Total DLLs                      ; 0                                           ;
+---------------------------------+---------------------------------------------+


+-------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Settings                                                                                                 ;
+---------------------------------------------------------------------------------+-----------------------+---------------------+
; Option                                                                          ; Setting               ; Default Value       ;
+---------------------------------------------------------------------------------+-----------------------+---------------------+
; Device                                                                          ; 5CGXFC7C7F23C8        ;                     ;
; Top-level entity name                                                           ; zhuhoujia14131454_007 ; zhuhoujia2023111413 ;
; Family name                                                                     ; Cyclone V             ; Cyclone V           ;
; Use smart compilation                                                           ; Off                   ; Off                 ;
; Enable parallel Assembler and Timing Analyzer during compilation                ; On                    ; On                  ;
; Enable compact report table                                                     ; Off                   ; Off                 ;
; Restructure Multiplexers                                                        ; Auto                  ; Auto                ;
; MLAB Add Timing Constraints For Mixed-Port Feed-Through Mode Setting Don't Care ; Off                   ; Off                 ;
; Create Debugging Nodes for IP Cores                                             ; Off                   ; Off                 ;
; Preserve fewer node names                                                       ; On                    ; On                  ;
; Intel FPGA IP Evaluation Mode                                                   ; Enable                ; Enable              ;
; Verilog Version                                                                 ; Verilog_2001          ; Verilog_2001        ;
; VHDL Version                                                                    ; VHDL_1993             ; VHDL_1993           ;
; State Machine Processing                                                        ; Auto                  ; Auto                ;
; Safe State Machine                                                              ; Off                   ; Off                 ;
; Extract Verilog State Machines                                                  ; On                    ; On                  ;
; Extract VHDL State Machines                                                     ; On                    ; On                  ;
; Ignore Verilog initial constructs                                               ; Off                   ; Off                 ;
; Iteration limit for constant Verilog loops                                      ; 5000                  ; 5000                ;
; Iteration limit for non-constant Verilog loops                                  ; 250                   ; 250                 ;
; Add Pass-Through Logic to Inferred RAMs                                         ; On                    ; On                  ;
; Infer RAMs from Raw Logic                                                       ; On                    ; On                  ;
; Parallel Synthesis                                                              ; On                    ; On                  ;
; DSP Block Balancing                                                             ; Auto                  ; Auto                ;
; NOT Gate Push-Back                                                              ; On                    ; On                  ;
; Power-Up Don't Care                                                             ; On                    ; On                  ;
; Remove Redundant Logic Cells                                                    ; Off                   ; Off                 ;
; Remove Duplicate Registers                                                      ; On                    ; On                  ;
; Ignore CARRY Buffers                                                            ; Off                   ; Off                 ;
; Ignore CASCADE Buffers                                                          ; Off                   ; Off                 ;
; Ignore GLOBAL Buffers                                                           ; Off                   ; Off                 ;
; Ignore ROW GLOBAL Buffers                                                       ; Off                   ; Off                 ;
; Ignore LCELL Buffers                                                            ; Off                   ; Off                 ;
; Ignore SOFT Buffers                                                             ; On                    ; On                  ;
; Limit AHDL Integers to 32 Bits                                                  ; Off                   ; Off                 ;
; Optimization Technique                                                          ; Balanced              ; Balanced            ;
; Carry Chain Length                                                              ; 70                    ; 70                  ;
; Auto Carry Chains                                                               ; On                    ; On                  ;
; Auto Open-Drain Pins                                                            ; On                    ; On                  ;
; Perform WYSIWYG Primitive Resynthesis                                           ; Off                   ; Off                 ;
; Auto ROM Replacement                                                            ; On                    ; On                  ;
; Auto RAM Replacement                                                            ; On                    ; On                  ;
; Auto DSP Block Replacement                                                      ; On                    ; On                  ;
; Auto Shift Register Replacement                                                 ; Auto                  ; Auto                ;
; Allow Shift Register Merging across Hierarchies                                 ; Auto                  ; Auto                ;
; Auto Clock Enable Replacement                                                   ; On                    ; On                  ;
; Strict RAM Replacement                                                          ; Off                   ; Off                 ;
; Allow Synchronous Control Signals                                               ; On                    ; On                  ;
; Force Use of Synchronous Clear Signals                                          ; Off                   ; Off                 ;
; Auto Resource Sharing                                                           ; Off                   ; Off                 ;
; Allow Any RAM Size For Recognition                                              ; Off                   ; Off                 ;
; Allow Any ROM Size For Recognition                                              ; Off                   ; Off                 ;
; Allow Any Shift Register Size For Recognition                                   ; Off                   ; Off                 ;
; Use LogicLock Constraints during Resource Balancing                             ; On                    ; On                  ;
; Ignore translate_off and synthesis_off directives                               ; Off                   ; Off                 ;
; Timing-Driven Synthesis                                                         ; On                    ; On                  ;
; Report Parameter Settings                                                       ; On                    ; On                  ;
; Report Source Assignments                                                       ; On                    ; On                  ;
; Report Connectivity Checks                                                      ; On                    ; On                  ;
; Ignore Maximum Fan-Out Assignments                                              ; Off                   ; Off                 ;
; Synchronization Register Chain Length                                           ; 3                     ; 3                   ;
; Power Optimization During Synthesis                                             ; Normal compilation    ; Normal compilation  ;
; HDL message level                                                               ; Level2                ; Level2              ;
; Suppress Register Optimization Related Messages                                 ; Off                   ; Off                 ;
; Number of Removed Registers Reported in Synthesis Report                        ; 5000                  ; 5000                ;
; Number of Swept Nodes Reported in Synthesis Report                              ; 5000                  ; 5000                ;
; Number of Inverted Registers Reported in Synthesis Report                       ; 100                   ; 100                 ;
; Clock MUX Protection                                                            ; On                    ; On                  ;
; Auto Gated Clock Conversion                                                     ; Off                   ; Off                 ;
; Block Design Naming                                                             ; Auto                  ; Auto                ;
; SDC constraint protection                                                       ; Off                   ; Off                 ;
; Synthesis Effort                                                                ; Auto                  ; Auto                ;
; Shift Register Replacement - Allow Asynchronous Clear Signal                    ; On                    ; On                  ;
; Pre-Mapping Resynthesis Optimization                                            ; Off                   ; Off                 ;
; Analysis & Synthesis Message Level                                              ; Medium                ; Medium              ;
; Disable Register Merging Across Hierarchies                                     ; Auto                  ; Auto                ;
; Resource Aware Inference For Block RAM                                          ; On                    ; On                  ;
; Automatic Parallel Synthesis                                                    ; On                    ; On                  ;
; Partial Reconfiguration Bitstream ID                                            ; Off                   ; Off                 ;
+---------------------------------------------------------------------------------+-----------------------+---------------------+


+------------------------------------------+
; Parallel Compilation                     ;
+----------------------------+-------------+
; Processors                 ; Number      ;
+----------------------------+-------------+
; Number detected on machine ; 20          ;
; Maximum allowed            ; 14          ;
;                            ;             ;
; Average used               ; 1.00        ;
; Maximum used               ; 14          ;
;                            ;             ;
; Usage by Processor         ; % Time Used ;
;     Processor 1            ; 100.0%      ;
;     Processors 2-14        ;   0.0%      ;
+----------------------------+-------------+


+----------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Source Files Read                                                                                                 ;
+----------------------------------+-----------------+------------------------+------------------------------------------------+---------+
; File Name with User-Entered Path ; Used in Netlist ; File Type              ; File Name with Absolute Path                   ; Library ;
+----------------------------------+-----------------+------------------------+------------------------------------------------+---------+
; zhuhoujia14131454_001.v          ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia14131454_001.v ;         ;
; zhuhoujia14131454_007.v          ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia14131454_007.v ;         ;
; zhuhoujia14131454_006.v          ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia14131454_006.v ;         ;
; zhuhoujia14131454_005.v          ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia14131454_005.v ;         ;
; zhuhoujia14131454_004.v          ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia14131454_004.v ;         ;
; zhuhoujia14131454_003.v          ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia14131454_003.v ;         ;
; zhuhoujia14131454_002.v          ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia14131454_002.v ;         ;
+----------------------------------+-----------------+------------------------+------------------------------------------------+---------+


+---------------------------------------------------------------+
; Analysis & Synthesis Resource Usage Summary                   ;
+---------------------------------------------+-----------------+
; Resource                                    ; Usage           ;
+---------------------------------------------+-----------------+
; Estimate of Logic utilization (ALMs needed) ; 129             ;
;                                             ;                 ;
; Combinational ALUT usage for logic          ; 221             ;
;     -- 7 input functions                    ; 0               ;
;     -- 6 input functions                    ; 36              ;
;     -- 5 input functions                    ; 31              ;
;     -- 4 input functions                    ; 34              ;
;     -- <=3 input functions                  ; 120             ;
;                                             ;                 ;
; Dedicated logic registers                   ; 148             ;
;                                             ;                 ;
; I/O pins                                    ; 30              ;
;                                             ;                 ;
; Total DSP Blocks                            ; 0               ;
;                                             ;                 ;
; Maximum fan-out node                        ; clk_50MHz~input ;
; Maximum fan-out                             ; 148             ;
; Total fan-out                               ; 1369            ;
; Average fan-out                             ; 3.18            ;
+---------------------------------------------+-----------------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Resource Utilization by Entity                                                                                                                                                                                                     ;
+-------------------------------------------+---------------------+---------------------------+-------------------+------------+------+--------------+-------------------------------------------------------------+-----------------------+--------------+
; Compilation Hierarchy Node                ; Combinational ALUTs ; Dedicated Logic Registers ; Block Memory Bits ; DSP Blocks ; Pins ; Virtual Pins ; Full Hierarchy Name                                         ; Entity Name           ; Library Name ;
+-------------------------------------------+---------------------+---------------------------+-------------------+------------+------+--------------+-------------------------------------------------------------+-----------------------+--------------+
; |zhuhoujia14131454_007                    ; 221 (0)             ; 148 (0)                   ; 0                 ; 0          ; 30   ; 0            ; |zhuhoujia14131454_007                                      ; zhuhoujia14131454_007 ; work         ;
;    |zhuhoujia14131454_001:line_follow|    ; 3 (3)               ; 3 (3)                     ; 0                 ; 0          ; 0    ; 0            ; |zhuhoujia14131454_007|zhuhoujia14131454_001:line_follow    ; zhuhoujia14131454_001 ; work         ;
;    |zhuhoujia14131454_002:obstacle_avoid| ; 1 (1)               ; 3 (3)                     ; 0                 ; 0          ; 0    ; 0            ; |zhuhoujia14131454_007|zhuhoujia14131454_002:obstacle_avoid ; zhuhoujia14131454_002 ; work         ;
;    |zhuhoujia14131454_003:motor_ctrl|     ; 13 (13)             ; 14 (14)                   ; 0                 ; 0          ; 0    ; 0            ; |zhuhoujia14131454_007|zhuhoujia14131454_003:motor_ctrl     ; zhuhoujia14131454_003 ; work         ;
;    |zhuhoujia14131454_004:motor_driver|   ; 86 (86)             ; 63 (63)                   ; 0                 ; 0          ; 0    ; 0            ; |zhuhoujia14131454_007|zhuhoujia14131454_004:motor_driver   ; zhuhoujia14131454_004 ; work         ;
;    |zhuhoujia14131454_005:dht11_sensor|   ; 58 (58)             ; 33 (33)                   ; 0                 ; 0          ; 0    ; 0            ; |zhuhoujia14131454_007|zhuhoujia14131454_005:dht11_sensor   ; zhuhoujia14131454_005 ; work         ;
;    |zhuhoujia14131454_006:lcd_display|    ; 60 (60)             ; 32 (32)                   ; 0                 ; 0          ; 0    ; 0            ; |zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display    ; zhuhoujia14131454_006 ; work         ;
+-------------------------------------------+---------------------+---------------------------+-------------------+------------+------+--------------+-------------------------------------------------------------+-----------------------+--------------+
Note: For table entries with two numbers listed, the numbers in parentheses indicate the number of resources of the given type used by the specific entity alone. The numbers listed outside of parentheses indicate the total resources of the given type used by the specific entity and all of its sub-entities in the hierarchy.


Encoding Type:  One-Hot
+---------------------------------------------------------------------------------+
; State Machine - |zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display|state  ;
+--------------------+------------+--------------------+------------+-------------+
; Name               ; state.IDLE ; state.DISPLAY_TEXT ; state.INIT ; state.RESET ;
+--------------------+------------+--------------------+------------+-------------+
; state.RESET        ; 0          ; 0                  ; 0          ; 0           ;
; state.INIT         ; 0          ; 0                  ; 1          ; 1           ;
; state.DISPLAY_TEXT ; 0          ; 1                  ; 0          ; 1           ;
; state.IDLE         ; 1          ; 0                  ; 0          ; 1           ;
+--------------------+------------+--------------------+------------+-------------+


Encoding Type:  One-Hot
+-------------------------------------------------------------------------------------------+
; State Machine - |zhuhoujia14131454_007|zhuhoujia14131454_005:dht11_sensor|state           ;
+----------------+---------------+----------------+-------------+------------+--------------+
; Name           ; state.RECEIVE ; state.RESPONSE ; state.START ; state.IDLE ; state.FINISH ;
+----------------+---------------+----------------+-------------+------------+--------------+
; state.IDLE     ; 0             ; 0              ; 0           ; 0          ; 0            ;
; state.START    ; 0             ; 0              ; 1           ; 1          ; 0            ;
; state.RESPONSE ; 0             ; 1              ; 0           ; 1          ; 0            ;
; state.RECEIVE  ; 1             ; 0              ; 0           ; 1          ; 0            ;
; state.FINISH   ; 0             ; 0              ; 0           ; 1          ; 1            ;
+----------------+---------------+----------------+-------------+------------+--------------+


+--------------------------------------------------------------------------------------------+
; Registers Removed During Synthesis                                                         ;
+---------------------------------------------------+----------------------------------------+
; Register name                                     ; Reason for Removal                     ;
+---------------------------------------------------+----------------------------------------+
; zhuhoujia14131454_003:motor_ctrl|speed_control[0] ; Stuck at GND due to stuck port data_in ;
; zhuhoujia14131454_006:lcd_display|lcd_data[6,7]   ; Stuck at GND due to stuck port data_in ;
; zhuhoujia14131454_006:lcd_display|state~8         ; Lost fanout                            ;
; zhuhoujia14131454_006:lcd_display|state~9         ; Lost fanout                            ;
; zhuhoujia14131454_006:lcd_display|state~10        ; Lost fanout                            ;
; zhuhoujia14131454_006:lcd_display|state~11        ; Lost fanout                            ;
; zhuhoujia14131454_005:dht11_sensor|state~9        ; Lost fanout                            ;
; zhuhoujia14131454_005:dht11_sensor|state~10       ; Lost fanout                            ;
; Total Number of Removed Registers = 9             ;                                        ;
+---------------------------------------------------+----------------------------------------+


+------------------------------------------------------+
; General Register Statistics                          ;
+----------------------------------------------+-------+
; Statistic                                    ; Value ;
+----------------------------------------------+-------+
; Total registers                              ; 148   ;
; Number of registers using Synchronous Clear  ; 81    ;
; Number of registers using Synchronous Load   ; 0     ;
; Number of registers using Asynchronous Clear ; 148   ;
; Number of registers using Asynchronous Load  ; 0     ;
; Number of registers using Clock Enable       ; 35    ;
; Number of registers using Preset             ; 0     ;
+----------------------------------------------+-------+


+---------------------------------------------------------------+
; Inverted Register Statistics                                  ;
+-----------------------------------------------------+---------+
; Inverted Register                                   ; Fan out ;
+-----------------------------------------------------+---------+
; zhuhoujia14131454_003:motor_ctrl|final_control[2]   ; 11      ;
; zhuhoujia14131454_003:motor_ctrl|final_control[1]   ; 10      ;
; zhuhoujia14131454_003:motor_ctrl|final_control[0]   ; 9       ;
; zhuhoujia14131454_005:dht11_sensor|dht11_out        ; 1       ;
; zhuhoujia14131454_001:line_follow|control_signal[2] ; 1       ;
; zhuhoujia14131454_001:line_follow|control_signal[1] ; 1       ;
; zhuhoujia14131454_001:line_follow|control_signal[0] ; 1       ;
; Total number of inverted registers = 7              ;         ;
+-----------------------------------------------------+---------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Multiplexer Restructuring Statistics (Restructuring Performed)                                                                                                                          ;
+--------------------+-----------+---------------+----------------------+------------------------+------------+---------------------------------------------------------------------------+
; Multiplexer Inputs ; Bus Width ; Baseline Area ; Area if Restructured ; Saving if Restructured ; Registered ; Example Multiplexer Output                                                ;
+--------------------+-----------+---------------+----------------------+------------------------+------------+---------------------------------------------------------------------------+
; 5:1                ; 3 bits    ; 9 LEs         ; 3 LEs                ; 6 LEs                  ; Yes        ; |zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display|lcd_data[7]      ;
; 5:1                ; 5 bits    ; 15 LEs        ; 5 LEs                ; 10 LEs                 ; Yes        ; |zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display|lcd_data[2]      ;
; 5:1                ; 6 bits    ; 18 LEs        ; 6 LEs                ; 12 LEs                 ; Yes        ; |zhuhoujia14131454_007|zhuhoujia14131454_005:dht11_sensor|bit_counter[3]  ;
; 8:1                ; 16 bits   ; 80 LEs        ; 0 LEs                ; 80 LEs                 ; Yes        ; |zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display|delay_counter[3] ;
; 8:1                ; 4 bits    ; 20 LEs        ; 8 LEs                ; 12 LEs                 ; Yes        ; |zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display|char_index[1]    ;
; 10:1               ; 20 bits   ; 120 LEs       ; 0 LEs                ; 120 LEs                ; Yes        ; |zhuhoujia14131454_007|zhuhoujia14131454_005:dht11_sensor|timer[6]        ;
; 5:1                ; 2 bits    ; 6 LEs         ; 4 LEs                ; 2 LEs                  ; Yes        ; |zhuhoujia14131454_007|zhuhoujia14131454_005:dht11_sensor|dht11_out       ;
; 8:1                ; 4 bits    ; 20 LEs        ; 8 LEs                ; 12 LEs                 ; No         ; |zhuhoujia14131454_007|zhuhoujia14131454_006:lcd_display|Selector2        ;
; 9:1                ; 5 bits    ; 30 LEs        ; 20 LEs               ; 10 LEs                 ; No         ; |zhuhoujia14131454_007|zhuhoujia14131454_005:dht11_sensor|Selector4       ;
+--------------------+-----------+---------------+----------------------+------------------------+------------+---------------------------------------------------------------------------+


+--------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: zhuhoujia14131454_001:line_follow ;
+----------------+-------+-------------------------------------------------------+
; Parameter Name ; Value ; Type                                                  ;
+----------------+-------+-------------------------------------------------------+
; FORWARD_FULL   ; 000   ; Unsigned Binary                                       ;
; FORWARD_SLOW   ; 001   ; Unsigned Binary                                       ;
; LEFT_SLOW      ; 010   ; Unsigned Binary                                       ;
; LEFT_FAST      ; 011   ; Unsigned Binary                                       ;
; RIGHT_SLOW     ; 100   ; Unsigned Binary                                       ;
; RIGHT_FAST     ; 101   ; Unsigned Binary                                       ;
; BACK_FULL      ; 110   ; Unsigned Binary                                       ;
; STOP           ; 111   ; Unsigned Binary                                       ;
+----------------+-------+-------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+-----------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: zhuhoujia14131454_002:obstacle_avoid ;
+----------------+-------+----------------------------------------------------------+
; Parameter Name ; Value ; Type                                                     ;
+----------------+-------+----------------------------------------------------------+
; FORWARD_FULL   ; 000   ; Unsigned Binary                                          ;
; FORWARD_SLOW   ; 001   ; Unsigned Binary                                          ;
; LEFT_SLOW      ; 010   ; Unsigned Binary                                          ;
; LEFT_FAST      ; 011   ; Unsigned Binary                                          ;
; RIGHT_SLOW     ; 100   ; Unsigned Binary                                          ;
; RIGHT_FAST     ; 101   ; Unsigned Binary                                          ;
; BACK_FULL      ; 110   ; Unsigned Binary                                          ;
; STOP           ; 111   ; Unsigned Binary                                          ;
+----------------+-------+----------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+-------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: zhuhoujia14131454_003:motor_ctrl ;
+----------------+-------+------------------------------------------------------+
; Parameter Name ; Value ; Type                                                 ;
+----------------+-------+------------------------------------------------------+
; FORWARD_FULL   ; 000   ; Unsigned Binary                                      ;
; FORWARD_SLOW   ; 001   ; Unsigned Binary                                      ;
; LEFT_SLOW      ; 010   ; Unsigned Binary                                      ;
; LEFT_FAST      ; 011   ; Unsigned Binary                                      ;
; RIGHT_SLOW     ; 100   ; Unsigned Binary                                      ;
; RIGHT_FAST     ; 101   ; Unsigned Binary                                      ;
; BACK_FULL      ; 110   ; Unsigned Binary                                      ;
; STOP           ; 111   ; Unsigned Binary                                      ;
+----------------+-------+------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+---------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: zhuhoujia14131454_005:dht11_sensor ;
+----------------+----------------------+-----------------------------------------+
; Parameter Name ; Value                ; Type                                    ;
+----------------+----------------------+-----------------------------------------+
; WAIT_TIME      ; 11110100001001000000 ; Unsigned Binary                         ;
; START_LOW      ; 1001110001000000     ; Unsigned Binary                         ;
; START_HIGH     ; 0000011001000000     ; Unsigned Binary                         ;
; IDLE           ; 000                  ; Unsigned Binary                         ;
; START          ; 001                  ; Unsigned Binary                         ;
; RESPONSE       ; 010                  ; Unsigned Binary                         ;
; RECEIVE        ; 011                  ; Unsigned Binary                         ;
; FINISH         ; 100                  ; Unsigned Binary                         ;
+----------------+----------------------+-----------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+--------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: zhuhoujia14131454_006:lcd_display ;
+----------------+-------+-------------------------------------------------------+
; Parameter Name ; Value ; Type                                                  ;
+----------------+-------+-------------------------------------------------------+
; RESET          ; 0000  ; Unsigned Binary                                       ;
; INIT           ; 0001  ; Unsigned Binary                                       ;
; DISPLAY_TEXT   ; 0010  ; Unsigned Binary                                       ;
; IDLE           ; 0011  ; Unsigned Binary                                       ;
; TEXT_LENGTH    ; 10    ; Signed Integer                                        ;
+----------------+-------+-------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+-------------------------------------------------------------------------------------------------------------------------+
; Port Connectivity Checks: "zhuhoujia14131454_003:motor_ctrl"                                                            ;
+---------------+--------+----------+-------------------------------------------------------------------------------------+
; Port          ; Type   ; Severity ; Details                                                                             ;
+---------------+--------+----------+-------------------------------------------------------------------------------------+
; final_control ; Output ; Info     ; Connected to dangling logic. Logic that only feeds a dangling port will be removed. ;
+---------------+--------+----------+-------------------------------------------------------------------------------------+


+-----------------------------------------------------+
; Post-Synthesis Netlist Statistics for Top Partition ;
+-----------------------+-----------------------------+
; Type                  ; Count                       ;
+-----------------------+-----------------------------+
; arriav_ff             ; 148                         ;
;     CLR               ; 52                          ;
;     CLR SCLR          ; 61                          ;
;     ENA CLR           ; 15                          ;
;     ENA CLR SCLR      ; 20                          ;
; arriav_io_obuf        ; 1                           ;
; arriav_lcell_comb     ; 222                         ;
;     arith             ; 88                          ;
;         1 data inputs ; 87                          ;
;         2 data inputs ; 1                           ;
;     normal            ; 134                         ;
;         0 data inputs ; 1                           ;
;         1 data inputs ; 2                           ;
;         2 data inputs ; 13                          ;
;         3 data inputs ; 17                          ;
;         4 data inputs ; 34                          ;
;         5 data inputs ; 31                          ;
;         6 data inputs ; 36                          ;
; boundary_port         ; 30                          ;
;                       ;                             ;
; Max LUT depth         ; 3.40                        ;
; Average LUT depth     ; 2.31                        ;
+-----------------------+-----------------------------+


+-------------------------------+
; Elapsed Time Per Partition    ;
+----------------+--------------+
; Partition Name ; Elapsed Time ;
+----------------+--------------+
; Top            ; 00:00:00     ;
+----------------+--------------+


+-------------------------------+
; Analysis & Synthesis Messages ;
+-------------------------------+
Info: *******************************************************************
Info: Running Quartus Prime Analysis & Synthesis
    Info: Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
    Info: Processing started: Sat Jul 19 00:36:43 2025
Info: Command: quartus_map --read_settings_files=on --write_settings_files=off zhuhoujia2023111413 -c zhuhoujia2023111413
Warning (18236): Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance.
Info (20030): Parallel compilation is enabled and will use 14 of the 14 processors detected
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia14131454_001.v
    Info (12023): Found entity 1: zhuhoujia14131454_001 File: E:/zhuhoujia2023111413/zhuhoujia14131454_001.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia14131454_007.v
    Info (12023): Found entity 1: zhuhoujia14131454_007 File: E:/zhuhoujia2023111413/zhuhoujia14131454_007.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia14131454_006.v
    Info (12023): Found entity 1: zhuhoujia14131454_006 File: E:/zhuhoujia2023111413/zhuhoujia14131454_006.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia14131454_005.v
    Info (12023): Found entity 1: zhuhoujia14131454_005 File: E:/zhuhoujia2023111413/zhuhoujia14131454_005.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia14131454_004.v
    Info (12023): Found entity 1: zhuhoujia14131454_004 File: E:/zhuhoujia2023111413/zhuhoujia14131454_004.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia14131454_003.v
    Info (12023): Found entity 1: zhuhoujia14131454_003 File: E:/zhuhoujia2023111413/zhuhoujia14131454_003.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia14131454_002.v
    Info (12023): Found entity 1: zhuhoujia14131454_002 File: E:/zhuhoujia2023111413/zhuhoujia14131454_002.v Line: 1
Info (12127): Elaborating entity "zhuhoujia14131454_007" for the top level hierarchy
Info (12128): Elaborating entity "zhuhoujia14131454_001" for hierarchy "zhuhoujia14131454_001:line_follow" File: E:/zhuhoujia2023111413/zhuhoujia14131454_007.v Line: 47
Info (12128): Elaborating entity "zhuhoujia14131454_002" for hierarchy "zhuhoujia14131454_002:obstacle_avoid" File: E:/zhuhoujia2023111413/zhuhoujia14131454_007.v Line: 54
Warning (10036): Verilog HDL or VHDL warning at zhuhoujia14131454_002.v(19): object "obstacle_output" assigned a value but never read File: E:/zhuhoujia2023111413/zhuhoujia14131454_002.v Line: 19
Info (12128): Elaborating entity "zhuhoujia14131454_003" for hierarchy "zhuhoujia14131454_003:motor_ctrl" File: E:/zhuhoujia2023111413/zhuhoujia14131454_007.v Line: 64
Info (12128): Elaborating entity "zhuhoujia14131454_004" for hierarchy "zhuhoujia14131454_004:motor_driver" File: E:/zhuhoujia2023111413/zhuhoujia14131454_007.v Line: 81
Info (12128): Elaborating entity "zhuhoujia14131454_005" for hierarchy "zhuhoujia14131454_005:dht11_sensor" File: E:/zhuhoujia2023111413/zhuhoujia14131454_007.v Line: 90
Warning (10230): Verilog HDL assignment warning at zhuhoujia14131454_005.v(50): truncated value with size 32 to match size of target (20) File: E:/zhuhoujia2023111413/zhuhoujia14131454_005.v Line: 50
Warning (10230): Verilog HDL assignment warning at zhuhoujia14131454_005.v(61): truncated value with size 32 to match size of target (20) File: E:/zhuhoujia2023111413/zhuhoujia14131454_005.v Line: 61
Warning (10230): Verilog HDL assignment warning at zhuhoujia14131454_005.v(71): truncated value with size 32 to match size of target (20) File: E:/zhuhoujia2023111413/zhuhoujia14131454_005.v Line: 71
Warning (10230): Verilog HDL assignment warning at zhuhoujia14131454_005.v(80): truncated value with size 32 to match size of target (6) File: E:/zhuhoujia2023111413/zhuhoujia14131454_005.v Line: 80
Warning (10230): Verilog HDL assignment warning at zhuhoujia14131454_005.v(83): truncated value with size 32 to match size of target (20) File: E:/zhuhoujia2023111413/zhuhoujia14131454_005.v Line: 83
Info (12128): Elaborating entity "zhuhoujia14131454_006" for hierarchy "zhuhoujia14131454_006:lcd_display" File: E:/zhuhoujia2023111413/zhuhoujia14131454_007.v Line: 102
Warning (10230): Verilog HDL assignment warning at zhuhoujia14131454_006.v(59): truncated value with size 32 to match size of target (16) File: E:/zhuhoujia2023111413/zhuhoujia14131454_006.v Line: 59
Warning (10230): Verilog HDL assignment warning at zhuhoujia14131454_006.v(70): truncated value with size 32 to match size of target (16) File: E:/zhuhoujia2023111413/zhuhoujia14131454_006.v Line: 70
Warning (10230): Verilog HDL assignment warning at zhuhoujia14131454_006.v(86): truncated value with size 32 to match size of target (4) File: E:/zhuhoujia2023111413/zhuhoujia14131454_006.v Line: 86
Warning (10230): Verilog HDL assignment warning at zhuhoujia14131454_006.v(94): truncated value with size 32 to match size of target (16) File: E:/zhuhoujia2023111413/zhuhoujia14131454_006.v Line: 94
Warning (10230): Verilog HDL assignment warning at zhuhoujia14131454_006.v(104): truncated value with size 32 to match size of target (16) File: E:/zhuhoujia2023111413/zhuhoujia14131454_006.v Line: 104
Warning (10240): Verilog HDL Always Construct warning at zhuhoujia14131454_006.v(42): inferring latch(es) for variable "lcd_rw", which holds its previous value in one or more paths through the always construct File: E:/zhuhoujia2023111413/zhuhoujia14131454_006.v Line: 42
Warning (10030): Net "display_text.data_a" at zhuhoujia14131454_006.v(24) has no driver or initial value, using a default initial value '0' File: E:/zhuhoujia2023111413/zhuhoujia14131454_006.v Line: 24
Warning (10030): Net "display_text.waddr_a" at zhuhoujia14131454_006.v(24) has no driver or initial value, using a default initial value '0' File: E:/zhuhoujia2023111413/zhuhoujia14131454_006.v Line: 24
Warning (10030): Net "display_text.we_a" at zhuhoujia14131454_006.v(24) has no driver or initial value, using a default initial value '0' File: E:/zhuhoujia2023111413/zhuhoujia14131454_006.v Line: 24
Info (10041): Inferred latch for "lcd_rw" at zhuhoujia14131454_006.v(42) File: E:/zhuhoujia2023111413/zhuhoujia14131454_006.v Line: 42
Info (276014): Found 1 instances of uninferred RAM logic
    Info (276004): RAM logic "zhuhoujia14131454_006:lcd_display|display_text" is uninferred due to inappropriate RAM size File: E:/zhuhoujia2023111413/zhuhoujia14131454_006.v Line: 24
Critical Warning (127005): Memory depth (16) in the design file differs from memory depth (10) in the Memory Initialization File "E:/zhuhoujia2023111413/db/zhuhoujia2023111413.ram0_zhuhoujia14131454_006_e74913fb.hdl.mif" -- setting initial value for remaining addresses to 0
Warning (13024): Output pins are stuck at VCC or GND
    Warning (13410): Pin "lcd_rw" is stuck at GND File: E:/zhuhoujia2023111413/zhuhoujia14131454_007.v Line: 26
    Warning (13410): Pin "lcd_data[6]" is stuck at GND File: E:/zhuhoujia2023111413/zhuhoujia14131454_007.v Line: 29
    Warning (13410): Pin "lcd_data[7]" is stuck at GND File: E:/zhuhoujia2023111413/zhuhoujia14131454_007.v Line: 29
Info (286030): Timing-Driven Synthesis is running
Info (17049): 6 registers lost all their fanouts during netlist optimizations.
Info (144001): Generated suppressed messages file E:/zhuhoujia2023111413/zhuhoujia2023111413.map.smsg
Info (16010): Generating hard_block partition "hard_block:auto_generated_inst"
    Info (16011): Adding 0 node(s), including 0 DDIO, 0 PLL, 0 transceiver and 0 LCELL
Info (21057): Implemented 267 device resources after synthesis - the final resource count might be different
    Info (21058): Implemented 8 input pins
    Info (21059): Implemented 21 output pins
    Info (21060): Implemented 1 bidirectional pins
    Info (21061): Implemented 237 logic cells
Info: Quartus Prime Analysis & Synthesis was successful. 0 errors, 21 warnings
    Info: Peak virtual memory: 4895 megabytes
    Info: Processing ended: Sat Jul 19 00:36:53 2025
    Info: Elapsed time: 00:00:10
    Info: Total CPU time (on all processors): 00:00:18


+------------------------------------------+
; Analysis & Synthesis Suppressed Messages ;
+------------------------------------------+
The suppressed messages can be found in E:/zhuhoujia2023111413/zhuhoujia2023111413.map.smsg.


