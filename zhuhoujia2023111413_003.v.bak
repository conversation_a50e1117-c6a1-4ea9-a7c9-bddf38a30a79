module motor_control_module(
    input clk,
    input rst_n,
    input [1:0] line_control,
    input [1:0] obstacle_control,
    output reg [3:0] motor_dir,
    output reg [7:0] motor_speed,
    output reg [1:0] final_control
);

parameter FORWARD = 2'b00;
parameter LEFT    = 2'b01;
parameter RIGHT   = 2'b10;
parameter STOP    = 2'b11;

// 电机方向定义：[左前, 左后, 右前, 右后]
parameter MOTOR_FORWARD = 4'b1010;
parameter MOTOR_LEFT    = 4'b0010;
parameter MOTOR_RIGHT   = 4'b1000;
parameter MOTOR_BACK    = 4'b0101;
parameter MOTOR_STOP    = 4'b0000;

// 优先级控制：避障优先于循线
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        final_control <= STOP;
        motor_dir <= MOTOR_STOP;
        motor_speed <= 8'd0;
    end else begin
        // 优先考虑避障
        if (obstacle_control != FORWARD) begin
            final_control <= obstacle_control;
        end else begin
            final_control <= line_control;
        end
        
        // 根据最终控制信号设置电机
        case (final_control)
            FORWARD: begin
                motor_dir <= MOTOR_FORWARD;
                motor_speed <= 8'd200;  // 正常前进速度
            end
            LEFT: begin
                motor_dir <= MOTOR_LEFT;
                motor_speed <= 8'd150;  // 转弯速度
            end
            RIGHT: begin
                motor_dir <= MOTOR_RIGHT;
                motor_speed <= 8'd150;  // 转弯速度
            end
            STOP: begin
                motor_dir <= MOTOR_STOP;
                motor_speed <= 8'd0;
            end
            default: begin
                motor_dir <= MOTOR_STOP;
                motor_speed <= 8'd0;
            end
        endcase
    end
end

endmodule
