module zhuhoujia14131454_007(
    input clk_50MHz,
    input rst_n,
    
    // 循线寻迹信号 (4位)
    input [3:0] line_sensor,
    
    // 避障信号 (2位)
    input [1:0] obstacle_sensor,
    
    // DHT11温湿度传感器信号
    inout dht11_data,
    
    // 电机控制输出 - 8个引脚控制4个电机
    output motor1_a, motor1_b,  // 电机1（左前）
    output motor2_a, motor2_b,  // 电机2（左后）
    output motor3_a, motor3_b,  // 电机3（右前）
    output motor4_a, motor4_b,  // 电机4（右后）

    // 时钟输出
    output clk_1m,           // 1MHz输出时钟
    output clk_100hz,        // 100Hz输出时钟
    
    // LCD显示接口
    output lcd_rst,
    output lcd_rw,
    output lcd_en,
    output [7:0] lcd_data
);

// 内部信号定义
wire [2:0] line_control;      // 循线控制信号（3位）
wire [2:0] obstacle_control;  // 避障控制信号（3位）
wire [2:0] final_control;     // 最终控制信号（3位）
wire [3:0] motor_control;     // 电机控制命令
wire [7:0] speed_control;     // 速度控制值
wire [15:0] temperature;
wire [15:0] humidity;
wire dht11_valid;

// 模块实例化保持不变，只是信号位宽改变
zhuhoujia14131454_001 line_follow(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .line_sensor(line_sensor),
    .control_signal(line_control)
);

zhuhoujia14131454_002 obstacle_avoid(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .obstacle_sensor(obstacle_sensor),
    .control_signal(obstacle_control)
);

zhuhoujia14131454_003 motor_ctrl(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .line_control(line_control),           // 直接使用3位信号
    .obstacle_control(obstacle_control),   // 直接使用3位信号
    .final_control(final_control),
    .motor_control(motor_control),         // 电机控制命令输出
    .speed_control(speed_control)          // 速度控制值输出
);

zhuhoujia14131454_004 motor_driver(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .motor_control(motor_control),         // 电机控制命令输入
    .speed_control(speed_control),         // 速度控制值输入
    .motor1_a(motor1_a),                   // 电机1 A端
    .motor1_b(motor1_b),                   // 电机1 B端
    .motor2_a(motor2_a),                   // 电机2 A端
    .motor2_b(motor2_b),                   // 电机2 B端
    .motor3_a(motor3_a),                   // 电机3 A端
    .motor3_b(motor3_b),                   // 电机3 B端
    .motor4_a(motor4_a),                   // 电机4 A端
    .motor4_b(motor4_b),                   // 电机4 B端
    .clk_1m(clk_1m),                       // 1MHz时钟输出
    .clk_100hz(clk_100hz)                  // 100Hz时钟输出
);

zhuhoujia14131454_005 dht11_sensor(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .dht11_data(dht11_data),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(dht11_valid)
);

zhuhoujia14131454_006 lcd_display(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(dht11_valid),
    .lcd_rst(lcd_rst),
    .lcd_rw(lcd_rw),
    .lcd_en(lcd_en),
    .lcd_data(lcd_data)
);

endmodule
