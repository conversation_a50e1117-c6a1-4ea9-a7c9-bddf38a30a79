`timescale 1ns / 1ps

module zhuhoujia14131454_001_tb;

// 测试信号定义
reg clk;
reg rst_n;
reg [3:0] line_sensor;
wire [2:0] control_signal;

// 实例化被测试模块
zhuhoujia14131454_001 uut (
    .clk(clk),
    .rst_n(rst_n),
    .line_sensor(line_sensor),
    .control_signal(control_signal)
);

// 时钟生成 - 50MHz
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// 控制信号解码任务
task decode_control_signal;
    input [2:0] signal;
    begin
        case (signal)
            3'b000: $display("控制信号: FORWARD_FULL (全速前进)");
            3'b001: $display("控制信号: FORWARD_SLOW (缓慢前进)");
            3'b010: $display("控制信号: LEFT_SLOW (缓左转)");
            3'b011: $display("控制信号: LEFT_FAST (急左转)");
            3'b100: $display("控制信号: RIGHT_SLOW (缓右转)");
            3'b101: $display("控制信号: RIGHT_FAST (急右转)");
            3'b110: $display("控制信号: BACK_FULL (全速倒车)");
            3'b111: $display("控制信号: STOP (停止)");
            default: $display("控制信号: UNKNOWN (未知)");
        endcase
    end
endtask

// 测试场景任务
task test_line_scenario;
    input [3:0] sensor_input;
    input string scenario_name;
    begin
        line_sensor = sensor_input;
        #100; // 等待信号稳定
        
        $display("=== %s ===", scenario_name);
        $display("传感器输入: %b", sensor_input);
        decode_control_signal(control_signal);
        $display("输出编码: %b", control_signal);
        $display("");
    end
endtask

// 主测试序列
initial begin
    // 初始化
    rst_n = 0;
    line_sensor = 4'b0000;
    
    $display("========================================");
    $display("循线传感器模块测试开始");
    $display("传感器排列: [左外, 左内, 右内, 右外]");
    $display("========================================");
    
    // 释放复位
    #50;
    rst_n = 1;
    #50;
    
    // 测试理想循线状态
    test_line_scenario(4'b0110, "理想循线状态");
    
    // 测试轻微偏移
    test_line_scenario(4'b1110, "轻微左偏");
    test_line_scenario(4'b0111, "轻微右偏");
    test_line_scenario(4'b1010, "左中传感器检测");
    test_line_scenario(4'b0101, "右中传感器检测");
    
    // 测试中等偏移
    test_line_scenario(4'b1100, "中等左偏");
    test_line_scenario(4'b0011, "中等右偏");
    test_line_scenario(4'b0010, "左中单独检测");
    test_line_scenario(4'b0100, "右中单独检测");
    
    // 测试严重偏移
    test_line_scenario(4'b1000, "严重左偏");
    test_line_scenario(4'b0001, "严重右偏");
    
    // 测试复杂情况
    test_line_scenario(4'b1001, "两端传感器检测");
    test_line_scenario(4'b1011, "左侧多传感器");
    test_line_scenario(4'b1101, "右侧多传感器");
    
    // 测试特殊情况
    test_line_scenario(4'b1111, "全部检测到");
    test_line_scenario(4'b0000, "丢失线路");
    
    // 测试其他组合
    test_line_scenario(4'b1110, "其他组合1");
    test_line_scenario(4'b0111, "其他组合2");
    
    // 测试复位功能
    $display("=== 测试复位功能 ===");
    line_sensor = 4'b1111;
    #50;
    rst_n = 0;
    #50;
    $display("复位后控制信号: %b (应为111-STOP)", control_signal);
    rst_n = 1;
    #50;
    decode_control_signal(control_signal);
    
    // 测试动态变化
    $display("\n=== 测试动态循线过程 ===");
    $display("模拟小车从左偏到正常再到右偏的过程:");
    
    line_sensor = 4'b1000; #100; // 严重左偏
    $display("步骤1: 严重左偏 -> %b", control_signal);
    
    line_sensor = 4'b1100; #100; // 中等左偏
    $display("步骤2: 中等左偏 -> %b", control_signal);
    
    line_sensor = 4'b1110; #100; // 轻微左偏
    $display("步骤3: 轻微左偏 -> %b", control_signal);
    
    line_sensor = 4'b0110; #100; // 正常循线
    $display("步骤4: 正常循线 -> %b", control_signal);
    
    line_sensor = 4'b0111; #100; // 轻微右偏
    $display("步骤5: 轻微右偏 -> %b", control_signal);
    
    line_sensor = 4'b0011; #100; // 中等右偏
    $display("步骤6: 中等右偏 -> %b", control_signal);
    
    line_sensor = 4'b0001; #100; // 严重右偏
    $display("步骤7: 严重右偏 -> %b", control_signal);
    
    $display("\n========================================");
    $display("循线传感器模块测试完成");
    $display("========================================");
    $finish;
end

// 监控控制信号变化
always @(control_signal) begin
    if ($time > 100) begin
        $display("时间=%0t: 控制信号变化为 %b", $time, control_signal);
    end
end

// 生成波形文件
initial begin
    $dumpfile("zhuhoujia14131454_001_tb.vcd");
    $dumpvars(0, zhuhoujia14131454_001_tb);
end

endmodule
