`timescale 1ns / 1ps

module zhuhoujia2023111413_004_tb;

// 测试信号定义
reg clk;
reg rst_n;
reg [7:0] speed_control;
wire [3:0] motor_pwm;

// 实例化被测试模块 - PWM生成器
zhuhoujia2023111413_004 uut (
    .clk(clk),
    .rst_n(rst_n),
    .speed_control(speed_control),
    .motor_pwm(motor_pwm)
);

// 时钟生成 - 高频时钟用于PWM生成
initial begin
    clk = 0;
    forever #5 clk = ~clk; // 100MHz时钟
end

// PWM占空比计算任务
task check_pwm_duty;
    input [7:0] expected_duty;
    integer high_count, total_count;
    real actual_duty;
    begin
        high_count = 0;
        total_count = 0;
        
        // 统计一个PWM周期内的高电平时间
        repeat(256) begin
            @(posedge clk);
            if (motor_pwm[0]) high_count = high_count + 1;
            total_count = total_count + 1;
        end
        
        actual_duty = (high_count * 100.0) / total_count;
        $display("期望占空比: %d%%, 实际占空比: %.1f%%, PWM输出: %b", 
                 (expected_duty * 100) / 256, actual_duty, motor_pwm);
    end
endtask

// 测试序列
initial begin
    // 初始化
    rst_n = 0;
    speed_control = 8'd0;
    
    #100;
    rst_n = 1;
    #20;
    
    // 测试用例1：最小速度（占空比0%）
    speed_control = 8'd0;
    #20;
    $display("测试1 - 最小速度:");
    check_pwm_duty(8'd0);
    
    // 测试用例2：25%占空比
    speed_control = 8'd64;
    #20;
    $display("测试2 - 25%%占空比:");
    check_pwm_duty(8'd64);
    
    // 测试用例3：50%占空比
    speed_control = 8'd128;
    #20;
    $display("测试3 - 50%%占空比:");
    check_pwm_duty(8'd128);
    
    // 测试用例4：75%占空比
    speed_control = 8'd192;
    #20;
    $display("测试4 - 75%%占空比:");
    check_pwm_duty(8'd192);
    
    // 测试用例5：最大速度（占空比100%）
    speed_control = 8'd255;
    #20;
    $display("测试5 - 最大速度:");
    check_pwm_duty(8'd255);
    
    // 测试复位功能
    rst_n = 0;
    #40;
    $display("测试6 - 复位状态: motor_pwm=%b (期望:0000)", motor_pwm);
    
    $display("PWM生成模块测试完成！");
    $finish;
end

initial begin
    $dumpfile("zhuhoujia2023111413_004_tb.vcd");
    $dumpvars(0, zhuhoujia2023111413_004_tb);
end

endmodule