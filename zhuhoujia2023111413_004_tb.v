`timescale 1ns / 1ps

module zhuhoujia2023111413_004_tb;

// 测试信号定义
reg clk;
reg rst_n;
reg [7:0] speed_control;
reg [3:0] motor_dir;        // 添加电机方向控制信号
wire [3:0] motor_pwm;

// 实例化被测试模块 - PWM生成器
zhuhoujia2023111413_004 uut (
    .clk(clk),
    .rst_n(rst_n),
    .speed_control(speed_control),
    .motor_dir(motor_dir),      // 连接电机方向信号
    .motor_pwm(motor_pwm)
);

// 时钟生成 - 高频时钟用于PWM生成
initial begin
    clk = 0;
    forever #5 clk = ~clk; // 100MHz时钟
end

// PWM占空比计算任务 - 检查所有4个电机的PWM输出
task check_pwm_duty;
    input [7:0] expected_duty;
    input [3:0] expected_dir;
    integer high_count[0:3], total_count;
    real actual_duty[0:3];
    integer i;
    begin
        // 初始化计数器
        for (i = 0; i < 4; i = i + 1) begin
            high_count[i] = 0;
        end
        total_count = 0;

        // 统计一个PWM周期内的高电平时间
        repeat(256) begin
            @(posedge clk);
            for (i = 0; i < 4; i = i + 1) begin
                if (motor_pwm[i]) high_count[i] = high_count[i] + 1;
            end
            total_count = total_count + 1;
        end

        // 计算并显示每个电机的占空比
        $display("电机方向: %b, 速度控制: %d", expected_dir, expected_duty);
        for (i = 0; i < 4; i = i + 1) begin
            actual_duty[i] = (high_count[i] * 100.0) / total_count;
            if (expected_dir[i]) begin
                $display("  电机%d: 期望占空比: %d%%, 实际占空比: %.1f%%",
                         i, (expected_duty * 100) / 256, actual_duty[i]);
            end else begin
                $display("  电机%d: 停止状态, 实际占空比: %.1f%% (期望: 0%%)",
                         i, actual_duty[i]);
            end
        end
        $display("  PWM输出状态: %b", motor_pwm);
        $display("");
    end
endtask

// 测试序列
initial begin
    // 初始化
    rst_n = 0;
    speed_control = 8'd0;
    motor_dir = 4'b0000;

    #100;
    rst_n = 1;
    #20;

    $display("=== PWM生成模块测试开始 ===");
    $display("");

    // 测试用例1：前进运动 - 所有轮子正转
    motor_dir = 4'b1111;  // 所有轮子正转
    speed_control = 8'd128;  // 50%速度
    #20;
    $display("测试1 - 前进运动 (所有轮正转):");
    check_pwm_duty(8'd128, 4'b1111);

    // 测试用例2：左转运动 - 左轮反转，右轮正转
    motor_dir = 4'b0011;  // 左轮停止，右轮正转
    speed_control = 8'd150;  // 60%速度
    #20;
    $display("测试2 - 左转运动 (左轮停止，右轮正转):");
    check_pwm_duty(8'd150, 4'b0011);

    // 测试用例3：右转运动 - 左轮正转，右轮反转
    motor_dir = 4'b1100;  // 左轮正转，右轮停止
    speed_control = 8'd150;  // 60%速度
    #20;
    $display("测试3 - 右转运动 (左轮正转，右轮停止):");
    check_pwm_duty(8'd150, 4'b1100);

    // 测试用例4：后退运动 - 所有轮子反转
    motor_dir = 4'b0000;  // 所有轮子停止（反转在实际硬件中通过方向控制实现）
    speed_control = 8'd100;  // 40%速度
    #20;
    $display("测试4 - 后退运动 (所有轮停止):");
    check_pwm_duty(8'd100, 4'b0000);

    // 测试用例5：停止状态
    motor_dir = 4'b0000;  // 所有轮子停止
    speed_control = 8'd0;   // 0%速度
    #20;
    $display("测试5 - 停止状态:");
    check_pwm_duty(8'd0, 4'b0000);

    // 测试用例6：最大速度前进
    motor_dir = 4'b1111;  // 所有轮子正转
    speed_control = 8'd255; // 100%速度
    #20;
    $display("测试6 - 最大速度前进:");
    check_pwm_duty(8'd255, 4'b1111);

    // 测试复位功能
    rst_n = 0;
    #40;
    $display("测试7 - 复位状态: motor_pwm=%b (期望:0000)", motor_pwm);

    $display("=== PWM生成模块测试完成！===");
    $finish;
end

initial begin
    $dumpfile("zhuhoujia2023111413_004_tb.vcd");
    $dumpvars(0, zhuhoujia2023111413_004_tb);
end

endmodule