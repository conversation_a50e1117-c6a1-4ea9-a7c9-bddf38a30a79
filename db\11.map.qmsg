{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1752810009076 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus Prime " "Running Quartus Prime Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752810009082 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Jul 18 11:40:09 2025 " "Processing started: Fri Jul 18 11:40:09 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752810009082 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752810009082 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off 11 -c 11 " "Command: quartus_map --read_settings_files=on --write_settings_files=off 11 -c 11" {  } {  } 0 0 "Command: %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752810009082 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Analysis & Synthesis" 0 -1 1752810009895 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Analysis & Synthesis" 0 -1 1752810009895 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_001.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_001.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_001 " "Found entity 1: zhuhoujia2023111413_001" {  } { { "zhuhoujia2023111413_001.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752810016081 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752810016081 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_002.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_002.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_002 " "Found entity 1: zhuhoujia2023111413_002" {  } { { "zhuhoujia2023111413_002.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_002.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752810016081 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752810016081 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_003.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_003.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_003 " "Found entity 1: zhuhoujia2023111413_003" {  } { { "zhuhoujia2023111413_003.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_003.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752810016084 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752810016084 ""}
{ "Error" "EVRFX_VERI_SYNTAX_ERROR" "\"endmodule\";  expecting a description zhuhoujia2023111413_004.v(348) " "Verilog HDL syntax error at zhuhoujia2023111413_004.v(348) near text: \"endmodule\";  expecting a description. Check for and fix any syntax errors that appear immediately before or at the specified keyword. The Intel FPGA Knowledge Database contains many articles with specific details on how to resolve this error. Visit the Knowledge Database at https://www.altera.com/support/support-resources/knowledge-base/search.html and search for this specific error message number." {  } { { "zhuhoujia2023111413_004.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v" 348 0 0 } }  } 0 10170 "Verilog HDL syntax error at %2!s! near text: %1!s!. Check for and fix any syntax errors that appear immediately before or at the specified keyword. The Intel FPGA Knowledge Database contains many articles with specific details on how to resolve this error. Visit the Knowledge Database at https://www.altera.com/support/support-resources/knowledge-base/search.html and search for this specific error message number." 0 0 "Analysis & Synthesis" 0 -1 1752810016088 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_004.v 0 0 " "Found 0 design units, including 0 entities, in source file zhuhoujia2023111413_004.v" {  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752810016088 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_005.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_005.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_005 " "Found entity 1: zhuhoujia2023111413_005" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752810016090 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752810016090 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_006.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_006.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_006 " "Found entity 1: zhuhoujia2023111413_006" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752810016093 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752810016093 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_007.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_007.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_007 " "Found entity 1: zhuhoujia2023111413_007" {  } { { "zhuhoujia2023111413_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752810016097 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752810016097 ""}
{ "Error" "EQEXE_ERROR_COUNT" "Analysis & Synthesis 1  1  Quartus Prime " "Quartus Prime Analysis & Synthesis was unsuccessful. 1 error, 1 warning" { { "Error" "EQEXE_END_PEAK_VSIZE_MEMORY" "4711 " "Peak virtual memory: 4711 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752810016136 ""} { "Error" "EQEXE_END_BANNER_TIME" "Fri Jul 18 11:40:16 2025 " "Processing ended: Fri Jul 18 11:40:16 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752810016136 ""} { "Error" "EQEXE_ELAPSED_TIME" "00:00:07 " "Elapsed time: 00:00:07" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752810016136 ""} { "Error" "EQEXE_ELAPSED_CPU_TIME" "00:00:14 " "Total CPU time (on all processors): 00:00:14" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752810016136 ""}  } {  } 0 0 "%6!s! %1!s! was unsuccessful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1752810016136 ""}
