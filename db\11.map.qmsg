{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1752687950207 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus Prime " "Running Quartus Prime Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752687950210 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 17 01:45:50 2025 " "Processing started: Thu Jul 17 01:45:50 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752687950210 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752687950210 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off 11 -c 11 " "Command: quartus_map --read_settings_files=on --write_settings_files=off 11 -c 11" {  } {  } 0 0 "Command: %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752687950213 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Analysis & Synthesis" 0 -1 1752687951012 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Analysis & Synthesis" 0 -1 1752687951012 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_001.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_001.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_001 " "Found entity 1: zhuhoujia2023111413_001" {  } { { "zhuhoujia2023111413_001.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752687956983 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752687956983 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_002.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_002.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_002 " "Found entity 1: zhuhoujia2023111413_002" {  } { { "zhuhoujia2023111413_002.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_002.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752687956986 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752687956986 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_003.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_003.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_003 " "Found entity 1: zhuhoujia2023111413_003" {  } { { "zhuhoujia2023111413_003.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_003.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752687956988 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752687956988 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_004.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_004.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_004 " "Found entity 1: zhuhoujia2023111413_004" {  } { { "zhuhoujia2023111413_004.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752687956991 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752687956991 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_005.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_005.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_005 " "Found entity 1: zhuhoujia2023111413_005" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752687956994 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752687956994 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_006.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_006.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_006 " "Found entity 1: zhuhoujia2023111413_006" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752687956996 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752687956996 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_007.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_007.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_007 " "Found entity 1: zhuhoujia2023111413_007" {  } { { "zhuhoujia2023111413_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752687956997 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752687956997 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "zhuhoujia2023111413_007 " "Elaborating entity \"zhuhoujia2023111413_007\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Analysis & Synthesis" 0 -1 1752687957028 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_001 zhuhoujia2023111413_001:line_follow " "Elaborating entity \"zhuhoujia2023111413_001\" for hierarchy \"zhuhoujia2023111413_001:line_follow\"" {  } { { "zhuhoujia2023111413_007.v" "line_follow" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 40 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752687957035 ""}
{ "Warning" "WVRFX_VERI_CASE_ITEM_EXPRS_OVERLAP" "zhuhoujia2023111413_001.v(40) " "Verilog HDL Case Statement warning at zhuhoujia2023111413_001.v(40): case item expression covers a value already covered by a previous case item" {  } { { "zhuhoujia2023111413_001.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v" 40 0 0 } }  } 0 10272 "Verilog HDL Case Statement warning at %1!s!: case item expression covers a value already covered by a previous case item" 0 0 "Analysis & Synthesis" 0 -1 1752687957035 "|zhuhoujia2023111413_007|zhuhoujia2023111413_001:line_follow"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_002 zhuhoujia2023111413_002:obstacle_avoid " "Elaborating entity \"zhuhoujia2023111413_002\" for hierarchy \"zhuhoujia2023111413_002:obstacle_avoid\"" {  } { { "zhuhoujia2023111413_007.v" "obstacle_avoid" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 47 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752687957038 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_003 zhuhoujia2023111413_003:motor_ctrl " "Elaborating entity \"zhuhoujia2023111413_003\" for hierarchy \"zhuhoujia2023111413_003:motor_ctrl\"" {  } { { "zhuhoujia2023111413_007.v" "motor_ctrl" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 57 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752687957041 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_004 zhuhoujia2023111413_004:pwm_gen " "Elaborating entity \"zhuhoujia2023111413_004\" for hierarchy \"zhuhoujia2023111413_004:pwm_gen\"" {  } { { "zhuhoujia2023111413_007.v" "pwm_gen" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 64 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752687957044 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 8 zhuhoujia2023111413_004.v(16) " "Verilog HDL assignment warning at zhuhoujia2023111413_004.v(16): truncated value with size 32 to match size of target (8)" {  } { { "zhuhoujia2023111413_004.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v" 16 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752687957047 "|zhuhoujia2023111413_007|zhuhoujia2023111413_004:pwm_gen"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_005 zhuhoujia2023111413_005:dht11_sensor " "Elaborating entity \"zhuhoujia2023111413_005\" for hierarchy \"zhuhoujia2023111413_005:dht11_sensor\"" {  } { { "zhuhoujia2023111413_007.v" "dht11_sensor" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 73 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752687957047 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia2023111413_005.v(50) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(50): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 50 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752687957047 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia2023111413_005.v(61) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(61): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 61 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752687957047 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia2023111413_005.v(71) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(71): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 71 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752687957047 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 6 zhuhoujia2023111413_005.v(80) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(80): truncated value with size 32 to match size of target (6)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 80 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752687957047 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia2023111413_005.v(83) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(83): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 83 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752687957047 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_006 zhuhoujia2023111413_006:lcd_display " "Elaborating entity \"zhuhoujia2023111413_006\" for hierarchy \"zhuhoujia2023111413_006:lcd_display\"" {  } { { "zhuhoujia2023111413_007.v" "lcd_display" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 85 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752687957047 ""}
{ "Warning" "WVRFX_L2_HDL_OBJECT_ASSIGNED_NOT_READ" "char_counter zhuhoujia2023111413_006.v(15) " "Verilog HDL or VHDL warning at zhuhoujia2023111413_006.v(15): object \"char_counter\" assigned a value but never read" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 15 0 0 } }  } 0 10036 "Verilog HDL or VHDL warning at %2!s!: object \"%1!s!\" assigned a value but never read" 0 0 "Analysis & Synthesis" 0 -1 1752687957050 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia2023111413_006.v(40) " "Verilog HDL assignment warning at zhuhoujia2023111413_006.v(40): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 40 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752687957050 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia2023111413_006.v(50) " "Verilog HDL assignment warning at zhuhoujia2023111413_006.v(50): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 50 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752687957050 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "lcd_rw zhuhoujia2023111413_006.v(23) " "Verilog HDL Always Construct warning at zhuhoujia2023111413_006.v(23): inferring latch(es) for variable \"lcd_rw\", which holds its previous value in one or more paths through the always construct" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 23 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752687957050 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "lcd_rw zhuhoujia2023111413_006.v(23) " "Inferred latch for \"lcd_rw\" at zhuhoujia2023111413_006.v(23)" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 23 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752687957050 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Info" "IMLS_MLS_PRESET_POWER_UP" "" "Registers with preset signals will power-up high" {  } { { "zhuhoujia2023111413_003.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_003.v" 70 -1 0 } } { "zhuhoujia2023111413_001.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v" 22 -1 0 } } { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 19 -1 0 } }  } 0 13000 "Registers with preset signals will power-up high" 0 0 "Analysis & Synthesis" 0 -1 1752687957390 ""}
{ "Info" "IMLS_MLS_DEV_CLRN_SETS_REGISTERS" "" "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" {  } {  } 0 13003 "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" 0 0 "Analysis & Synthesis" 0 -1 1752687957390 ""}
{ "Warning" "WMLS_MLS_STUCK_PIN_HDR" "" "Output pins are stuck at VCC or GND" { { "Warning" "WMLS_MLS_STUCK_PIN" "lcd_rw GND " "Pin \"lcd_rw\" is stuck at GND" {  } { { "zhuhoujia2023111413_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 20 -1 0 } }  } 0 13410 "Pin \"%1!s!\" is stuck at %2!s!" 0 0 "Design Software" 0 -1 1752687957448 "|zhuhoujia2023111413_007|lcd_rw"}  } {  } 0 13024 "Output pins are stuck at VCC or GND" 0 0 "Analysis & Synthesis" 0 -1 1752687957448 ""}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Timing-Driven Synthesis is running" {  } {  } 0 286030 "Timing-Driven Synthesis is running" 0 0 "Analysis & Synthesis" 0 -1 1752687957498 ""}
{ "Info" "ISCL_SCL_LOST_FANOUT_MSG_HDR" "5 " "5 registers lost all their fanouts during netlist optimizations." {  } {  } 0 17049 "%1!d! registers lost all their fanouts during netlist optimizations." 0 0 "Analysis & Synthesis" 0 -1 1752687957844 ""}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "0 0 0 0 0 " "Adding 0 node(s), including 0 DDIO, 0 PLL, 0 transceiver and 0 LCELL" {  } {  } 0 16011 "Adding %1!d! node(s), including %2!d! DDIO, %3!d! PLL, %4!d! transceiver and %5!d! LCELL" 0 0 "Design Software" 0 -1 1752687957926 ""}  } {  } 0 16010 "Generating hard_block partition \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752687957926 ""}
{ "Info" "ICUT_CUT_TM_SUMMARY" "232 " "Implemented 232 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "8 " "Implemented 8 input pins" {  } {  } 0 21058 "Implemented %1!d! input pins" 0 0 "Design Software" 0 -1 1752687957952 ""} { "Info" "ICUT_CUT_TM_OPINS" "19 " "Implemented 19 output pins" {  } {  } 0 21059 "Implemented %1!d! output pins" 0 0 "Design Software" 0 -1 1752687957952 ""} { "Info" "ICUT_CUT_TM_BIDIRS" "1 " "Implemented 1 bidirectional pins" {  } {  } 0 21060 "Implemented %1!d! bidirectional pins" 0 0 "Design Software" 0 -1 1752687957952 ""} { "Info" "ICUT_CUT_TM_LCELLS" "204 " "Implemented 204 logic cells" {  } {  } 0 21061 "Implemented %1!d! logic cells" 0 0 "Design Software" 0 -1 1752687957952 ""}  } {  } 0 21057 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "Analysis & Synthesis" 0 -1 1752687957952 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 14 s Quartus Prime " "Quartus Prime Analysis & Synthesis was successful. 0 errors, 14 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4834 " "Peak virtual memory: 4834 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752687957965 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 17 01:45:57 2025 " "Processing ended: Thu Jul 17 01:45:57 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752687957965 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:07 " "Elapsed time: 00:00:07" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752687957965 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:14 " "Total CPU time (on all processors): 00:00:14" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752687957965 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1752687957965 ""}
