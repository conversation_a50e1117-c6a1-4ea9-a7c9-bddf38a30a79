{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1752754342977 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus Prime " "Running Quartus Prime Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1752754342981 ""} { "Info" "IQEXE_START_BANNER_TIME" "Thu Jul 17 20:12:22 2025 " "Processing started: Thu Jul 17 20:12:22 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1752754342981 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752754342981 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off 11 -c 11 " "Command: quartus_map --read_settings_files=on --write_settings_files=off 11 -c 11" {  } {  } 0 0 "Command: %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752754342981 ""}
{ "Warning" "WQCU_PARALLEL_USER_SHOULD_SPECIFY_NUM_PROC" "" "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." {  } {  } 0 18236 "Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance." 0 0 "Analysis & Synthesis" 0 -1 1752754343986 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Analysis & Synthesis" 0 -1 1752754343986 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_001.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_001.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_001 " "Found entity 1: zhuhoujia2023111413_001" {  } { { "zhuhoujia2023111413_001.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752754351209 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752754351209 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_002.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_002.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_002 " "Found entity 1: zhuhoujia2023111413_002" {  } { { "zhuhoujia2023111413_002.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_002.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752754351212 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752754351212 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_003.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_003.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_003 " "Found entity 1: zhuhoujia2023111413_003" {  } { { "zhuhoujia2023111413_003.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_003.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752754351216 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752754351216 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_004.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_004.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_004 " "Found entity 1: zhuhoujia2023111413_004" {  } { { "zhuhoujia2023111413_004.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752754351219 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752754351219 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_005.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_005.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_005 " "Found entity 1: zhuhoujia2023111413_005" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752754351222 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752754351222 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_006.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_006.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_006 " "Found entity 1: zhuhoujia2023111413_006" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752754351225 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752754351225 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "zhuhoujia2023111413_007.v 1 1 " "Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_007.v" { { "Info" "ISGN_ENTITY_NAME" "1 zhuhoujia2023111413_007 " "Found entity 1: zhuhoujia2023111413_007" {  } { { "zhuhoujia2023111413_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1752754351228 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1752754351228 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "zhuhoujia2023111413_007 " "Elaborating entity \"zhuhoujia2023111413_007\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Analysis & Synthesis" 0 -1 1752754351267 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_001 zhuhoujia2023111413_001:line_follow " "Elaborating entity \"zhuhoujia2023111413_001\" for hierarchy \"zhuhoujia2023111413_001:line_follow\"" {  } { { "zhuhoujia2023111413_007.v" "line_follow" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 41 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752754351277 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia2023111413_001.v(32) " "Verilog HDL assignment warning at zhuhoujia2023111413_001.v(32): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia2023111413_001.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v" 32 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752754351277 "|zhuhoujia2023111413_007|zhuhoujia2023111413_001:line_follow"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_002 zhuhoujia2023111413_002:obstacle_avoid " "Elaborating entity \"zhuhoujia2023111413_002\" for hierarchy \"zhuhoujia2023111413_002:obstacle_avoid\"" {  } { { "zhuhoujia2023111413_007.v" "obstacle_avoid" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 48 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752754351283 ""}
{ "Warning" "WVRFX_L2_HDL_OBJECT_ASSIGNED_NOT_READ" "obstacle_output zhuhoujia2023111413_002.v(51) " "Verilog HDL or VHDL warning at zhuhoujia2023111413_002.v(51): object \"obstacle_output\" assigned a value but never read" {  } { { "zhuhoujia2023111413_002.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_002.v" 51 0 0 } }  } 0 10036 "Verilog HDL or VHDL warning at %2!s!: object \"%1!s!\" assigned a value but never read" 0 0 "Analysis & Synthesis" 0 -1 1752754351283 "|zhuhoujia2023111413_007|zhuhoujia2023111413_002:obstacle_avoid"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia2023111413_002.v(32) " "Verilog HDL assignment warning at zhuhoujia2023111413_002.v(32): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia2023111413_002.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_002.v" 32 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752754351283 "|zhuhoujia2023111413_007|zhuhoujia2023111413_002:obstacle_avoid"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_003 zhuhoujia2023111413_003:motor_ctrl " "Elaborating entity \"zhuhoujia2023111413_003\" for hierarchy \"zhuhoujia2023111413_003:motor_ctrl\"" {  } { { "zhuhoujia2023111413_007.v" "motor_ctrl" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 58 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752754351289 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia2023111413_003.v(57) " "Verilog HDL assignment warning at zhuhoujia2023111413_003.v(57): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia2023111413_003.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_003.v" 57 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752754351289 "|zhuhoujia2023111413_007|zhuhoujia2023111413_003:motor_ctrl"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_004 zhuhoujia2023111413_004:pwm_gen " "Elaborating entity \"zhuhoujia2023111413_004\" for hierarchy \"zhuhoujia2023111413_004:pwm_gen\"" {  } { { "zhuhoujia2023111413_007.v" "pwm_gen" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 67 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752754351296 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 10 zhuhoujia2023111413_004.v(24) " "Verilog HDL assignment warning at zhuhoujia2023111413_004.v(24): truncated value with size 32 to match size of target (10)" {  } { { "zhuhoujia2023111413_004.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v" 24 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752754351296 "|zhuhoujia2023111413_007|zhuhoujia2023111413_004:pwm_gen"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 8 zhuhoujia2023111413_004.v(37) " "Verilog HDL assignment warning at zhuhoujia2023111413_004.v(37): truncated value with size 32 to match size of target (8)" {  } { { "zhuhoujia2023111413_004.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v" 37 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752754351296 "|zhuhoujia2023111413_007|zhuhoujia2023111413_004:pwm_gen"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_005 zhuhoujia2023111413_005:dht11_sensor " "Elaborating entity \"zhuhoujia2023111413_005\" for hierarchy \"zhuhoujia2023111413_005:dht11_sensor\"" {  } { { "zhuhoujia2023111413_007.v" "dht11_sensor" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 76 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752754351305 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia2023111413_005.v(50) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(50): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 50 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752754351307 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia2023111413_005.v(61) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(61): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 61 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752754351307 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia2023111413_005.v(71) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(71): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 71 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752754351307 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 6 zhuhoujia2023111413_005.v(80) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(80): truncated value with size 32 to match size of target (6)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 80 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752754351307 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 20 zhuhoujia2023111413_005.v(83) " "Verilog HDL assignment warning at zhuhoujia2023111413_005.v(83): truncated value with size 32 to match size of target (20)" {  } { { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 83 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752754351307 "|zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "zhuhoujia2023111413_006 zhuhoujia2023111413_006:lcd_display " "Elaborating entity \"zhuhoujia2023111413_006\" for hierarchy \"zhuhoujia2023111413_006:lcd_display\"" {  } { { "zhuhoujia2023111413_007.v" "lcd_display" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 88 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752754351308 ""}
{ "Warning" "WVRFX_L2_HDL_OBJECT_ASSIGNED_NOT_READ" "char_counter zhuhoujia2023111413_006.v(15) " "Verilog HDL or VHDL warning at zhuhoujia2023111413_006.v(15): object \"char_counter\" assigned a value but never read" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 15 0 0 } }  } 0 10036 "Verilog HDL or VHDL warning at %2!s!: object \"%1!s!\" assigned a value but never read" 0 0 "Analysis & Synthesis" 0 -1 1752754351308 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia2023111413_006.v(40) " "Verilog HDL assignment warning at zhuhoujia2023111413_006.v(40): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 40 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752754351308 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 16 zhuhoujia2023111413_006.v(50) " "Verilog HDL assignment warning at zhuhoujia2023111413_006.v(50): truncated value with size 32 to match size of target (16)" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 50 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1752754351308 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "lcd_rw zhuhoujia2023111413_006.v(23) " "Verilog HDL Always Construct warning at zhuhoujia2023111413_006.v(23): inferring latch(es) for variable \"lcd_rw\", which holds its previous value in one or more paths through the always construct" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 23 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1752754351308 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "lcd_rw zhuhoujia2023111413_006.v(23) " "Inferred latch for \"lcd_rw\" at zhuhoujia2023111413_006.v(23)" {  } { { "zhuhoujia2023111413_006.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v" 23 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1752754351308 "|zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display"}
{ "Info" "IMLS_MLS_PRESET_POWER_UP" "" "Registers with preset signals will power-up high" {  } { { "zhuhoujia2023111413_003.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_003.v" 132 -1 0 } } { "zhuhoujia2023111413_001.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v" 54 -1 0 } } { "zhuhoujia2023111413_005.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v" 19 -1 0 } }  } 0 13000 "Registers with preset signals will power-up high" 0 0 "Analysis & Synthesis" 0 -1 1752754351770 ""}
{ "Info" "IMLS_MLS_DEV_CLRN_SETS_REGISTERS" "" "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" {  } {  } 0 13003 "DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back" 0 0 "Analysis & Synthesis" 0 -1 1752754351770 ""}
{ "Warning" "WMLS_MLS_STUCK_PIN_HDR" "" "Output pins are stuck at VCC or GND" { { "Warning" "WMLS_MLS_STUCK_PIN" "lcd_rw GND " "Pin \"lcd_rw\" is stuck at GND" {  } { { "zhuhoujia2023111413_007.v" "" { Text "E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v" 21 -1 0 } }  } 0 13410 "Pin \"%1!s!\" is stuck at %2!s!" 0 0 "Design Software" 0 -1 1752754351883 "|zhuhoujia2023111413_007|lcd_rw"}  } {  } 0 13024 "Output pins are stuck at VCC or GND" 0 0 "Analysis & Synthesis" 0 -1 1752754351883 ""}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Timing-Driven Synthesis is running" {  } {  } 0 286030 "Timing-Driven Synthesis is running" 0 0 "Analysis & Synthesis" 0 -1 1752754351951 ""}
{ "Info" "ISCL_SCL_LOST_FANOUT_MSG_HDR" "7 " "7 registers lost all their fanouts during netlist optimizations." {  } {  } 0 17049 "%1!d! registers lost all their fanouts during netlist optimizations." 0 0 "Analysis & Synthesis" 0 -1 1752754352419 ""}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "0 0 0 0 0 " "Adding 0 node(s), including 0 DDIO, 0 PLL, 0 transceiver and 0 LCELL" {  } {  } 0 16011 "Adding %1!d! node(s), including %2!d! DDIO, %3!d! PLL, %4!d! transceiver and %5!d! LCELL" 0 0 "Design Software" 0 -1 1752754352534 ""}  } {  } 0 16010 "Generating hard_block partition \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1752754352534 ""}
{ "Info" "ICUT_CUT_TM_SUMMARY" "384 " "Implemented 384 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "8 " "Implemented 8 input pins" {  } {  } 0 21058 "Implemented %1!d! input pins" 0 0 "Design Software" 0 -1 1752754352581 ""} { "Info" "ICUT_CUT_TM_OPINS" "23 " "Implemented 23 output pins" {  } {  } 0 21059 "Implemented %1!d! output pins" 0 0 "Design Software" 0 -1 1752754352581 ""} { "Info" "ICUT_CUT_TM_BIDIRS" "1 " "Implemented 1 bidirectional pins" {  } {  } 0 21060 "Implemented %1!d! bidirectional pins" 0 0 "Design Software" 0 -1 1752754352581 ""} { "Info" "ICUT_CUT_TM_LCELLS" "352 " "Implemented 352 logic cells" {  } {  } 0 21061 "Implemented %1!d! logic cells" 0 0 "Design Software" 0 -1 1752754352581 ""}  } {  } 0 21057 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "Analysis & Synthesis" 0 -1 1752754352581 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 18 s Quartus Prime " "Quartus Prime Analysis & Synthesis was successful. 0 errors, 18 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4859 " "Peak virtual memory: 4859 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1752754352600 ""} { "Info" "IQEXE_END_BANNER_TIME" "Thu Jul 17 20:12:32 2025 " "Processing ended: Thu Jul 17 20:12:32 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1752754352600 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:10 " "Elapsed time: 00:00:10" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1752754352600 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:18 " "Total CPU time (on all processors): 00:00:18" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1752754352600 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1752754352600 ""}
