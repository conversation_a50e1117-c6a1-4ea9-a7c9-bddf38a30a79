`timescale 1ns / 1ps

module motor_pwm_test;

// 测试信号
reg clk_50MHz;
reg rst_n;
reg [3:0] line_sensor;
reg [1:0] obstacle_sensor;
wire [3:0] motor_pwm;
wire [3:0] motor_dir;
wire clk_1m;
wire clk_100hz;
wire lcd_rst;
wire lcd_rw;
wire lcd_en;
wire [7:0] lcd_data;

// DHT11数据模拟
reg dht11_data_reg;
reg dht11_oe_sim;
wire dht11_data;
assign dht11_data = dht11_oe_sim ? dht11_data_reg : 1'bz;

// 实例化顶层模块
zhuhoujia2023111413_007 uut (
    .clk_50MHz(clk_50MHz),
    .rst_n(rst_n),
    .line_sensor(line_sensor),
    .obstacle_sensor(obstacle_sensor),
    .dht11_data(dht11_data),
    .motor_pwm(motor_pwm),
    .motor_dir(motor_dir),
    .clk_1m(clk_1m),
    .clk_100hz(clk_100hz),
    .lcd_rst(lcd_rst),
    .lcd_rw(lcd_rw),
    .lcd_en(lcd_en),
    .lcd_data(lcd_data)
);

// 时钟生成 - 50MHz
initial begin
    clk_50MHz = 0;
    forever #10 clk_50MHz = ~clk_50MHz;
end

// 测试场景任务
task test_motor_scenario;
    input [3:0] line_input;
    input [1:0] obstacle_input;
    input string scenario_name;
    begin
        line_sensor = line_input;
        obstacle_sensor = obstacle_input;
        #10000; // 等待系统响应
        
        $display("=== %s ===", scenario_name);
        $display("输入 - 循线传感器: %b, 避障传感器: %b", line_input, obstacle_input);
        $display("输出 - 电机PWM: %b, 电机方向: %b", motor_pwm, motor_dir);
        $display("内部信号 - 电机控制: %b, 速度控制: %d", 
                 uut.motor_control, uut.speed_control);
        $display("时钟信号 - 1MHz: %b, 100Hz: %b", clk_1m, clk_100hz);
        $display("LCD显示 - 数据: 0x%h ('%c')", lcd_data, 
                 (lcd_data >= 8'h20 && lcd_data <= 8'h7E) ? lcd_data : 8'h20);
        $display("");
    end
endtask

// 主测试序列
initial begin
    // 初始化
    rst_n = 0;
    line_sensor = 4'b0000;
    obstacle_sensor = 2'b00;
    dht11_data_reg = 1;
    dht11_oe_sim = 0;
    
    $display("========================================");
    $display("智能小车PWM电机控制测试开始");
    $display("========================================");
    
    // 释放复位
    #1000;
    rst_n = 1;
    #5000;
    
    // 测试1：正常循线前进
    test_motor_scenario(4'b0110, 2'b00, "场景1: 正常循线前进");
    
    // 测试2：轻微左偏
    test_motor_scenario(4'b1110, 2'b00, "场景2: 轻微左偏缓左转");
    
    // 测试3：严重右偏
    test_motor_scenario(4'b0001, 2'b00, "场景3: 严重右偏急右转");
    
    // 测试4：右侧避障
    test_motor_scenario(4'b0110, 2'b01, "场景4: 右侧避障急左转");
    
    // 测试5：前方障碍后退
    test_motor_scenario(4'b0110, 2'b11, "场景5: 前方障碍后退");
    
    // 测试6：停止状态
    test_motor_scenario(4'b1111, 2'b00, "场景6: 到达终点停止");
    
    // 测试7：丢失线路
    test_motor_scenario(4'b0000, 2'b00, "场景7: 丢失线路缓慢前进");
    
    // 观察PWM波形
    $display("========================================");
    $display("观察PWM波形变化（前进状态）");
    $display("========================================");
    line_sensor = 4'b0110;  // 正常前进
    obstacle_sensor = 2'b00;
    
    // 观察100个时钟周期的PWM变化
    repeat(100) begin
        @(posedge clk_50MHz);
        if ($time % 200 == 0) begin  // 每200ns打印一次
            $display("时间=%0t: PWM=%b, 方向=%b, PWM计数器=%d", 
                     $time, motor_pwm, motor_dir, uut.motor_driver.pwm_counter);
        end
    end
    
    $display("========================================");
    $display("PWM电机控制测试完成");
    $display("========================================");
    $finish;
end

// 监控时钟分频器
reg clk_1m_prev, clk_100hz_prev;
always @(posedge clk_50MHz) begin
    clk_1m_prev <= clk_1m;
    clk_100hz_prev <= clk_100hz;
    
    if (clk_1m != clk_1m_prev && $time > 5000) begin
        $display("1MHz时钟切换: 时间=%0t, 状态=%b", $time, clk_1m);
    end
    
    if (clk_100hz != clk_100hz_prev && $time > 5000) begin
        $display("100Hz时钟切换: 时间=%0t, 状态=%b", $time, clk_100hz);
    end
end

// 监控电机状态变化
always @(motor_pwm or motor_dir) begin
    if ($time > 5000) begin
        $display("电机状态变化: 时间=%0t, PWM=%b, 方向=%b", 
                 $time, motor_pwm, motor_dir);
    end
end

// 生成波形文件
initial begin
    $dumpfile("motor_pwm_test.vcd");
    $dumpvars(0, motor_pwm_test);
end

endmodule
