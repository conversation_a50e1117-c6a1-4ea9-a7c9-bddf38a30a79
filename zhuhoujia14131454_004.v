module zhuhoujia14131454_004(
    input wire clk,            // 50MHz系统时钟
    input wire rst_n,          // 复位信号
    input wire [3:0] motor_control, // 电机控制命令
    input wire [7:0] speed_control, // 速度控制值

    // 8个电机驱动引脚 - 每个电机2个引脚（A和B）
    output reg motor1_a, motor1_b,  // 电机1（左前）
    output reg motor2_a, motor2_b,  // 电机2（左后）
    output reg motor3_a, motor3_b,  // 电机3（右前）
    output reg motor4_a, motor4_b,  // 电机4（右后）

    output reg clk_1m,           // 1MHz输出时钟
    output reg clk_100hz         // 100Hz输出时钟
);

    // PWM计数器
    reg [7:0] pwm_counter;

    // 分频器计数器
    reg [24:0] counter_1m;      // 50分频计数器
    reg [19:0] counter_100hz;   // 500000分频计数器

    // 电机定义
    localparam MOTOR_LEFT_FRONT = 2'd0,
               MOTOR_LEFT_REAR = 2'd1,
               MOTOR_RIGHT_FRONT = 2'd2,
               MOTOR_RIGHT_REAR = 2'd3;

    // PWM生成
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            pwm_counter <= 8'd0;
        end else begin
            pwm_counter <= pwm_counter + 1'b1;
        end
    end

    // 生成1MHz时钟
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            counter_1m <= 25'd0;
            clk_1m <= 1'b0;
        end else begin
            if (counter_1m >= 25'd24) begin
                counter_1m <= 25'd0;
                clk_1m <= ~clk_1m;
            end else begin
                counter_1m <= counter_1m + 1'b1;
            end
        end
    end

    // 生成100Hz时钟
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            counter_100hz <= 20'd0;
            clk_100hz <= 1'b0;
        end else begin
            if (counter_100hz >= 20'd249999) begin
                counter_100hz <= 20'd0;
                clk_100hz <= ~clk_100hz;
            end else begin
                counter_100hz <= counter_100hz + 1'b1;
            end
        end
    end

    // 电机控制逻辑 - 使用8个独立引脚控制4个电机
    always @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            // 复位时所有电机停止
            motor1_a <= 1'b0; motor1_b <= 1'b0;
            motor2_a <= 1'b0; motor2_b <= 1'b0;
            motor3_a <= 1'b0; motor3_b <= 1'b0;
            motor4_a <= 1'b0; motor4_b <= 1'b0;
        end else begin
            case (motor_control)
                // 停止
                4'd0: begin
                    motor1_a <= 1'b0; motor1_b <= 1'b0;
                    motor2_a <= 1'b0; motor2_b <= 1'b0;
                    motor3_a <= 1'b0; motor3_b <= 1'b0;
                    motor4_a <= 1'b0; motor4_b <= 1'b0;
                end

                // 前进 - 所有电机正转
                4'd1: begin
                    // 电机1（左前）正转
                    if (pwm_counter < speed_control) begin
                        motor1_a <= 1'b1; motor1_b <= 1'b0;
                    end else begin
                        motor1_a <= 1'b0; motor1_b <= 1'b0;
                    end

                    // 电机2（左后）正转
                    if (pwm_counter < speed_control) begin
                        motor2_a <= 1'b1; motor2_b <= 1'b0;
                    end else begin
                        motor2_a <= 1'b0; motor2_b <= 1'b0;
                    end

                    // 电机3（右前）正转
                    if (pwm_counter < speed_control) begin
                        motor3_a <= 1'b1; motor3_b <= 1'b0;
                    end else begin
                        motor3_a <= 1'b0; motor3_b <= 1'b0;
                    end

                    // 电机4（右后）正转
                    if (pwm_counter < speed_control) begin
                        motor4_a <= 1'b1; motor4_b <= 1'b0;
                    end else begin
                        motor4_a <= 1'b0; motor4_b <= 1'b0;
                    end
                end

                // 后退 - 所有电机反转
                4'd2: begin
                    // 电机1（左前）反转
                    if (pwm_counter < speed_control) begin
                        motor1_a <= 1'b0; motor1_b <= 1'b1;
                    end else begin
                        motor1_a <= 1'b0; motor1_b <= 1'b0;
                    end

                    // 电机2（左后）反转
                    if (pwm_counter < speed_control) begin
                        motor2_a <= 1'b0; motor2_b <= 1'b1;
                    end else begin
                        motor2_a <= 1'b0; motor2_b <= 1'b0;
                    end

                    // 电机3（右前）反转
                    if (pwm_counter < speed_control) begin
                        motor3_a <= 1'b0; motor3_b <= 1'b1;
                    end else begin
                        motor3_a <= 1'b0; motor3_b <= 1'b0;
                    end

                    // 电机4（右后）反转
                    if (pwm_counter < speed_control) begin
                        motor4_a <= 1'b0; motor4_b <= 1'b1;
                    end else begin
                        motor4_a <= 1'b0; motor4_b <= 1'b0;
                    end
                end

                // 左转 - 左侧电机反转，右侧电机正转
                4'd3: begin
                    // 电机1（左前）反转
                    if (pwm_counter < speed_control) begin
                        motor1_a <= 1'b0; motor1_b <= 1'b1;
                    end else begin
                        motor1_a <= 1'b0; motor1_b <= 1'b0;
                    end

                    // 电机2（左后）反转
                    if (pwm_counter < speed_control) begin
                        motor2_a <= 1'b0; motor2_b <= 1'b1;
                    end else begin
                        motor2_a <= 1'b0; motor2_b <= 1'b0;
                    end

                    // 电机3（右前）正转
                    if (pwm_counter < speed_control) begin
                        motor3_a <= 1'b1; motor3_b <= 1'b0;
                    end else begin
                        motor3_a <= 1'b0; motor3_b <= 1'b0;
                    end

                    // 电机4（右后）正转
                    if (pwm_counter < speed_control) begin
                        motor4_a <= 1'b1; motor4_b <= 1'b0;
                    end else begin
                        motor4_a <= 1'b0; motor4_b <= 1'b0;
                    end
                end

                // 右转 - 左侧电机正转，右侧电机反转
                4'd4: begin
                    // 电机1（左前）正转
                    if (pwm_counter < speed_control) begin
                        motor1_a <= 1'b1; motor1_b <= 1'b0;
                    end else begin
                        motor1_a <= 1'b0; motor1_b <= 1'b0;
                    end

                    // 电机2（左后）正转
                    if (pwm_counter < speed_control) begin
                        motor2_a <= 1'b1; motor2_b <= 1'b0;
                    end else begin
                        motor2_a <= 1'b0; motor2_b <= 1'b0;
                    end

                    // 电机3（右前）反转
                    if (pwm_counter < speed_control) begin
                        motor3_a <= 1'b0; motor3_b <= 1'b1;
                    end else begin
                        motor3_a <= 1'b0; motor3_b <= 1'b0;
                    end

                    // 电机4（右后）反转
                    if (pwm_counter < speed_control) begin
                        motor4_a <= 1'b0; motor4_b <= 1'b1;
                    end else begin
                        motor4_a <= 1'b0; motor4_b <= 1'b0;
                    end
                end

                // 缓左转 - 左侧电机慢速，右侧电机快速（差速转向）
                4'd5: begin
                    // 电机1（左前）慢速正转
                    if (pwm_counter < (speed_control/2)) begin
                        motor1_a <= 1'b1; motor1_b <= 1'b0;
                    end else begin
                        motor1_a <= 1'b0; motor1_b <= 1'b0;
                    end

                    // 电机2（左后）慢速正转
                    if (pwm_counter < (speed_control/2)) begin
                        motor2_a <= 1'b1; motor2_b <= 1'b0;
                    end else begin
                        motor2_a <= 1'b0; motor2_b <= 1'b0;
                    end

                    // 电机3（右前）快速正转
                    if (pwm_counter < speed_control) begin
                        motor3_a <= 1'b1; motor3_b <= 1'b0;
                    end else begin
                        motor3_a <= 1'b0; motor3_b <= 1'b0;
                    end

                    // 电机4（右后）快速正转
                    if (pwm_counter < speed_control) begin
                        motor4_a <= 1'b1; motor4_b <= 1'b0;
                    end else begin
                        motor4_a <= 1'b0; motor4_b <= 1'b0;
                    end
                end

                // 缓右转 - 左侧电机快速，右侧电机慢速（差速转向）
                4'd6: begin
                    // 电机1（左前）快速正转
                    if (pwm_counter < speed_control) begin
                        motor1_a <= 1'b1; motor1_b <= 1'b0;
                    end else begin
                        motor1_a <= 1'b0; motor1_b <= 1'b0;
                    end

                    // 电机2（左后）快速正转
                    if (pwm_counter < speed_control) begin
                        motor2_a <= 1'b1; motor2_b <= 1'b0;
                    end else begin
                        motor2_a <= 1'b0; motor2_b <= 1'b0;
                    end

                    // 电机3（右前）慢速正转
                    if (pwm_counter < (speed_control/2)) begin
                        motor3_a <= 1'b1; motor3_b <= 1'b0;
                    end else begin
                        motor3_a <= 1'b0; motor3_b <= 1'b0;
                    end

                    // 电机4（右后）慢速正转
                    if (pwm_counter < (speed_control/2)) begin
                        motor4_a <= 1'b1; motor4_b <= 1'b0;
                    end else begin
                        motor4_a <= 1'b0; motor4_b <= 1'b0;
                    end
                end

                // 急左转 - 左侧电机反转，右侧电机正转（原地转向）
                4'd7: begin
                    // 电机1（左前）反转
                    if (pwm_counter < speed_control) begin
                        motor1_a <= 1'b0; motor1_b <= 1'b1;
                    end else begin
                        motor1_a <= 1'b0; motor1_b <= 1'b0;
                    end

                    // 电机2（左后）反转
                    if (pwm_counter < speed_control) begin
                        motor2_a <= 1'b0; motor2_b <= 1'b1;
                    end else begin
                        motor2_a <= 1'b0; motor2_b <= 1'b0;
                    end

                    // 电机3（右前）正转
                    if (pwm_counter < speed_control) begin
                        motor3_a <= 1'b1; motor3_b <= 1'b0;
                    end else begin
                        motor3_a <= 1'b0; motor3_b <= 1'b0;
                    end

                    // 电机4（右后）正转
                    if (pwm_counter < speed_control) begin
                        motor4_a <= 1'b1; motor4_b <= 1'b0;
                    end else begin
                        motor4_a <= 1'b0; motor4_b <= 1'b0;
                    end
                end

                // 急右转 - 左侧电机正转，右侧电机反转（原地转向）
                4'd8: begin
                    // 电机1（左前）正转
                    if (pwm_counter < speed_control) begin
                        motor1_a <= 1'b1; motor1_b <= 1'b0;
                    end else begin
                        motor1_a <= 1'b0; motor1_b <= 1'b0;
                    end

                    // 电机2（左后）正转
                    if (pwm_counter < speed_control) begin
                        motor2_a <= 1'b1; motor2_b <= 1'b0;
                    end else begin
                        motor2_a <= 1'b0; motor2_b <= 1'b0;
                    end

                    // 电机3（右前）反转
                    if (pwm_counter < speed_control) begin
                        motor3_a <= 1'b0; motor3_b <= 1'b1;
                    end else begin
                        motor3_a <= 1'b0; motor3_b <= 1'b0;
                    end

                    // 电机4（右后）反转
                    if (pwm_counter < speed_control) begin
                        motor4_a <= 1'b0; motor4_b <= 1'b1;
                    end else begin
                        motor4_a <= 1'b0; motor4_b <= 1'b0;
                    end
                end

                // 默认情况 - 停止
                default: begin
                    motor1_a <= 1'b0; motor1_b <= 1'b0;
                    motor2_a <= 1'b0; motor2_b <= 1'b0;
                    motor3_a <= 1'b0; motor3_b <= 1'b0;
                    motor4_a <= 1'b0; motor4_b <= 1'b0;
                end
            endcase
        end
    end

endmodule


