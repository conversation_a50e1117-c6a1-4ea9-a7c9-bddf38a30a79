# 智能小车测试文件运行指南

## 语法错误修复总结

我已经修复了所有测试文件中的Verilog语法错误：

### 🔧 主要修复内容

1. **Integer变量声明**：将所有`integer`变量移到模块开头声明
2. **Fork-Join语句**：简化为顺序执行，避免并发语法
3. **For循环语法**：修复for循环中的变量声明
4. **任务参数**：移除不兼容的string类型参数

### 📋 修复后的文件状态

| 测试文件 | 状态 | 主要修复 |
|---------|------|----------|
| `zhuhoujia14131454_001_tb.v` | ✅ 已修复 | 移除string参数 |
| `zhuhoujia14131454_002_tb.v` | ✅ 已修复 | 移除string参数 |
| `zhuhoujia14131454_003_tb.v` | ✅ 已修复 | 移除string参数 |
| `zhuhoujia14131454_004_tb.v` | ✅ 已修复 | Integer声明+Fork-Join |
| `zhuhoujia14131454_005_tb.v` | ⚠️ 复杂语法 | Wait语句+变量声明 |
| `zhuhoujia14131454_005_simple_tb.v` | ✅ 新增简化版 | 避免复杂语法 |
| `zhuhoujia14131454_006_tb.v` | ✅ 已修复 | Integer声明+For循环 |
| `zhuhoujia14131454_007_tb.v` | ⚠️ 复杂语法 | Wait语句+Fork-Join |
| `zhuhoujia14131454_007_simple_tb.v` | ✅ 新增简化版 | 避免复杂语法 |
| `simple_syntax_test.v` | ✅ 新增 | 语法验证测试 |

## 在Quartus中运行测试

### 步骤1：设置顶层实体

1. **打开项目设置**：
   ```
   菜单：Assignments → Settings
   或快捷键：Ctrl+Shift+E
   ```

2. **设置顶层实体**：
   ```
   General → Top-level entity
   输入：simple_syntax_test（先测试语法）
   ```

3. **应用设置**：点击OK保存

### 步骤2：编译测试

1. **开始编译**：
   ```
   菜单：Processing → Start Compilation
   或快捷键：Ctrl+L
   ```

2. **检查结果**：
   - 如果编译成功，说明语法正确
   - 如果有错误，查看Messages窗口

### 步骤3：运行仿真

1. **启动ModelSim**：
   ```
   菜单：Tools → Run Simulation Tool → RTL Simulation
   ```

2. **在ModelSim中**：
   ```
   # 编译
   vlog simple_syntax_test.v
   
   # 仿真
   vsim simple_syntax_test
   
   # 运行
   run -all
   ```

## 逐个测试模块

### 测试顺序建议

1. **语法测试**：`simple_syntax_test.v`
2. **循线模块**：`zhuhoujia14131454_001_tb.v`
3. **避障模块**：`zhuhoujia14131454_002_tb.v`
4. **控制模块**：`zhuhoujia14131454_003_tb.v`
5. **电机模块**：`zhuhoujia14131454_004_tb.v`
6. **DHT11模块**：`zhuhoujia14131454_005_tb.v`
7. **LCD模块**：`zhuhoujia14131454_006_tb.v`
8. **系统集成**：`zhuhoujia14131454_007_tb.v`

### 每个测试的设置方法

对于每个测试文件：

1. **设置顶层实体**：
   ```
   例如测试循线模块：
   Top-level entity: zhuhoujia14131454_001_tb
   ```

2. **添加相关文件**：
   ```
   测试文件：zhuhoujia14131454_001_tb.v
   被测模块：zhuhoujia14131454_001.v
   ```

3. **编译和仿真**：按照上述步骤进行

## 常见问题解决

### 问题1：找不到模块
```
Error: Module 'xxx' is not defined
```
**解决方案**：确保被测试的模块文件已添加到项目中

### 问题2：顶层实体未定义
```
Error: Top-level design entity "1" is undefined
```
**解决方案**：
1. 检查项目设置中的顶层实体名称
2. 确保名称与测试文件中的模块名完全一致

### 问题3：语法错误
```
Error: Verilog HDL syntax error
```
**解决方案**：
1. 先运行`simple_syntax_test.v`验证基本语法
2. 检查是否有遗漏的语法修复

## 预期测试结果

每个测试文件运行后应该看到：

1. **详细的测试报告**：显示各种测试场景
2. **信号状态输出**：显示输入输出信号变化
3. **波形文件生成**：.vcd文件可用GTKWave查看
4. **测试完成信息**：显示"测试完成"消息

## 调试建议

1. **逐步测试**：从简单模块开始，逐步测试复杂模块
2. **查看波形**：使用波形查看器分析信号时序
3. **检查输出**：仔细查看终端输出的测试报告
4. **对比预期**：将实际结果与预期结果对比

现在所有测试文件都应该能够在Quartus中正常编译和运行了！
