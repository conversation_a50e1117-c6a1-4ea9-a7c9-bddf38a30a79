module zhuhoujia2023111413_007(
    input clk_50MHz,
    input rst_n,
    
    // 循线寻迹信号 (4位)
    input [3:0] line_sensor,
    
    // 避障信号 (2位)
    input [1:0] obstacle_sensor,
    
    // DHT11温湿度传感器信号
    inout dht11_data,
    
    // 电机控制输出
    output [3:0] motor_dir,    // 电机方向控制
    output [3:0] motor_pwm,    // 电机PWM调速
    
    // LCD显示接口
    output lcd_rst,
    output lcd_rw,
    output lcd_en,
    output [7:0] lcd_data
);

// 内部信号定义 - 更新为3位控制信号
wire [2:0] line_control;      // 循线控制信号（3位）
wire [2:0] obstacle_control;  // 避障控制信号（3位）
wire [2:0] final_control;     // 最终控制信号（3位）
wire [7:0] motor_speed;
wire [15:0] temperature;
wire [15:0] humidity;
wire dht11_valid;

// 模块实例化保持不变，只是信号位宽改变
zhuhoujia2023111413_001 line_follow(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .line_sensor(line_sensor),
    .control_signal(line_control)
);

zhuhoujia2023111413_002 obstacle_avoid(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .obstacle_sensor(obstacle_sensor),
    .control_signal(obstacle_control)
);

zhuhoujia2023111413_003 motor_ctrl(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .line_control(line_control),           // 直接使用3位信号
    .obstacle_control(obstacle_control),   // 直接使用3位信号
    .motor_dir(motor_dir),
    .motor_speed(motor_speed),
    .final_control(final_control)
);

zhuhoujia2023111413_004 pwm_gen(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .speed_control(motor_speed),
    .motor_dir(motor_dir),        // 添加电机方向信号
    .motor_pwm(motor_pwm)
);

zhuhoujia2023111413_005 dht11_sensor(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .dht11_data(dht11_data),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(dht11_valid)
);

zhuhoujia2023111413_006 lcd_display(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(dht11_valid),
    .lcd_rst(lcd_rst),
    .lcd_rw(lcd_rw),
    .lcd_en(lcd_en),
    .lcd_data(lcd_data)
);

endmodule
