`timescale 1ns / 1ps

module simple_motor_test;

// 测试信号
reg clk_50MHz;
reg rst_n;
reg [3:0] line_sensor;
reg [1:0] obstacle_sensor;
wire [3:0] motor_dir;
wire [3:0] motor_pwm_a;
wire [3:0] motor_pwm_b;

// 实例化顶层模块（简化版，只测试电机控制部分）
zhuhoujia2023111413_001 line_follow(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .line_sensor(line_sensor),
    .control_signal(line_control)
);

zhuhoujia2023111413_002 obstacle_avoid(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .obstacle_sensor(obstacle_sensor),
    .control_signal(obstacle_control)
);

zhuhoujia2023111413_003 motor_ctrl(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .line_control(line_control),
    .obstacle_control(obstacle_control),
    .motor_dir(motor_dir),
    .motor_speed(motor_speed),
    .final_control(final_control)
);

zhuhoujia2023111413_004 pwm_gen(
    .clk(clk_50MHz),
    .rst_n(rst_n),
    .speed_control(motor_speed),
    .motor_dir(motor_dir),
    .motor_pwm_a(motor_pwm_a),
    .motor_pwm_b(motor_pwm_b)
);

// 内部信号
wire [2:0] line_control;
wire [2:0] obstacle_control;
wire [2:0] final_control;
wire [7:0] motor_speed;

// 时钟生成 - 50MHz
initial begin
    clk_50MHz = 0;
    forever #10 clk_50MHz = ~clk_50MHz;
end

// 测试序列
initial begin
    // 初始化
    rst_n = 0;
    line_sensor = 4'b0000;
    obstacle_sensor = 2'b00;
    
    $display("========================================");
    $display("简化电机控制测试开始");
    $display("========================================");
    
    // 释放复位
    #1000;
    rst_n = 1;
    #2000;
    
    // 测试1：正常循线前进
    $display("\n=== 测试1: 正常循线前进 ===");
    line_sensor = 4'b0110;  // 中间传感器检测到线
    obstacle_sensor = 2'b00; // 无障碍
    #5000;
    $display("循线控制: %b, 避障控制: %b, 最终控制: %b", line_control, obstacle_control, final_control);
    $display("电机方向: %b, 电机速度: %d", motor_dir, motor_speed);
    $display("PWM_A: %b, PWM_B: %b", motor_pwm_a, motor_pwm_b);
    
    // 测试2：左偏修正
    $display("\n=== 测试2: 左偏修正 ===");
    line_sensor = 4'b1110;  // 左偏
    obstacle_sensor = 2'b00; // 无障碍
    #5000;
    $display("循线控制: %b, 避障控制: %b, 最终控制: %b", line_control, obstacle_control, final_control);
    $display("电机方向: %b, 电机速度: %d", motor_dir, motor_speed);
    $display("PWM_A: %b, PWM_B: %b", motor_pwm_a, motor_pwm_b);
    
    // 测试3：右侧避障
    $display("\n=== 测试3: 右侧避障 ===");
    line_sensor = 4'b0110;  // 正常循线
    obstacle_sensor = 2'b01; // 右侧障碍
    #5000;
    $display("循线控制: %b, 避障控制: %b, 最终控制: %b", line_control, obstacle_control, final_control);
    $display("电机方向: %b, 电机速度: %d", motor_dir, motor_speed);
    $display("PWM_A: %b, PWM_B: %b", motor_pwm_a, motor_pwm_b);
    
    // 测试4：前方障碍后退
    $display("\n=== 测试4: 前方障碍后退 ===");
    line_sensor = 4'b0110;  // 正常循线
    obstacle_sensor = 2'b11; // 前方障碍
    #5000;
    $display("循线控制: %b, 避障控制: %b, 最终控制: %b", line_control, obstacle_control, final_control);
    $display("电机方向: %b, 电机速度: %d", motor_dir, motor_speed);
    $display("PWM_A: %b, PWM_B: %b", motor_pwm_a, motor_pwm_b);
    
    // 测试5：停止状态
    $display("\n=== 测试5: 停止状态 ===");
    line_sensor = 4'b1111;  // 全部检测到（终点）
    obstacle_sensor = 2'b00; // 无障碍
    #5000;
    $display("循线控制: %b, 避障控制: %b, 最终控制: %b", line_control, obstacle_control, final_control);
    $display("电机方向: %b, 电机速度: %d", motor_dir, motor_speed);
    $display("PWM_A: %b, PWM_B: %b", motor_pwm_a, motor_pwm_b);
    
    $display("\n========================================");
    $display("简化电机控制测试完成");
    $display("========================================");
    $finish;
end

// 实时监控PWM信号
always @(posedge clk_50MHz) begin
    if ($time > 3000 && (motor_pwm_a != 4'b0000 || motor_pwm_b != 4'b0000)) begin
        if ($time % 1000 == 0) begin  // 每1000个时钟周期打印一次
            $display("时间=%0t: PWM_A=%b, PWM_B=%b, 方向=%b, 速度=%d", 
                     $time, motor_pwm_a, motor_pwm_b, motor_dir, motor_speed);
        end
    end
end

// 生成波形文件
initial begin
    $dumpfile("simple_motor_test.vcd");
    $dumpvars(0, simple_motor_test);
end

endmodule
