`timescale 1ns / 1ps

module zhuhoujia2023111413_006_tb;

// 测试信号定义
reg clk;
reg rst_n;
reg [15:0] temperature;
reg [15:0] humidity;
reg data_valid;
wire lcd_rst;
wire lcd_rw;
wire lcd_en;
wire [7:0] lcd_data;

// 实例化被测试模块 - LCD显示模块
zhuhoujia2023111413_006 uut (
    .clk(clk),
    .rst_n(rst_n),
    .temperature(temperature),
    .humidity(humidity),
    .data_valid(data_valid),
    .lcd_rst(lcd_rst),
    .lcd_rw(lcd_rw),
    .lcd_en(lcd_en),
    .lcd_data(lcd_data)
);

// 时钟生成 - 50MHz时钟
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// 状态监控任务
task monitor_state;
    input [3:0] expected_state;
    input string state_name;
    begin
        wait(uut.state == expected_state);
        $display("时间=%0t: 进入%s状态, lcd_rst=%b, lcd_en=%b, lcd_data=0x%h", 
                 $time, state_name, lcd_rst, lcd_en, lcd_data);
    end
endtask

// 测试序列
initial begin
    // 初始化信号
    rst_n = 0;
    temperature = 16'h0000;
    humidity = 16'h0000;
    data_valid = 0;
    
    $display("开始LCD显示模块测试...");
    $display("状态编码: 0=RESET, 1=INIT, 2=DISPLAY_TEMP, 3=DISPLAY_HUM, 4=IDLE");
    
    // 等待100ns后释放复位
    #100;
    rst_n = 1;
    
    // 监控RESET状态
    fork
        monitor_state(4'd0, "RESET");
    join_none
    
    // 等待复位延时完成，进入INIT状态
    wait(uut.state == 4'd1);
    $display("时间=%0t: 进入INIT状态，开始LCD初始化", $time);
    
    // 等待初始化完成
    wait(uut.state == 4'd2);
    $display("时间=%0t: 初始化完成，等待温湿度数据", $time);
    
    // 测试用例1：提供有效的温湿度数据
    temperature = 16'h1900; // 25°C (0x19 = 25)
    humidity = 16'h3C00;    // 60% (0x3C = 60)
    data_valid = 1;
    
    $display("测试1 - 提供温度数据: 温度=%d°C", temperature[15:8]);
    
    // 等待显示温度
    wait(uut.state == 4'd3);
    $display("时间=%0t: 显示温度完成，lcd_data=0x%h", $time, lcd_data);
    
    // 等待显示湿度
    wait(uut.state == 4'd4);
    $display("时间=%0t: 显示湿度完成，湿度=%d%%, lcd_data=0x%h", 
             $time, humidity[15:8], lcd_data);
    
    // 测试用例2：数据无效时的行为
    data_valid = 0;
    #1000;
    $display("测试2 - 数据无效时LCD状态保持");
    
    // 测试用例3：新的温湿度数据
    temperature = 16'h1E00; // 30°C
    humidity = 16'h4B00;    // 75%
    data_valid = 1;
    
    $display("测试3 - 新数据: 温度=%d°C, 湿度=%d%%", 
             temperature[15:8], humidity[15:8]);
    
    // 等待一个完整的显示周期
    wait(uut.state == 4'd4);
    #2000;
    
    // 测试用例4：复位功能测试
    $display("测试4 - 复位功能测试");
    rst_n = 0;
    #100;
    
    if (uut.state == 4'd0 && lcd_rst == 1'b0) begin
        $display("复位测试通过: 状态=%d, lcd_rst=%b", uut.state, lcd_rst);
    end else begin
        $display("复位测试失败: 状态=%d, lcd_rst=%b", uut.state, lcd_rst);
    end
    
    rst_n = 1;
    #100;
    
    $display("LCD显示模块测试完成！");
    $finish;
end

// 实时监控LCD控制信号
always @(posedge clk) begin
    if (lcd_en && $time > 200) begin
        $display("LCD写入: 时间=%0t, 数据=0x%h, 状态=%d", 
                 $time, lcd_data, uut.state);
    end
end

// 监控状态变化
always @(uut.state) begin
    if ($time > 0) begin
        case (uut.state)
            4'd0: $display("状态变化: RESET");
            4'd1: $display("状态变化: INIT");
            4'd2: $display("状态变化: DISPLAY_TEMP");
            4'd3: $display("状态变化: DISPLAY_HUM");
            4'd4: $display("状态变化: IDLE");
            default: $display("状态变化: 未知状态(%d)", uut.state);
        endcase
    end
end

// 生成波形文件
initial begin
    $dumpfile("zhuhoujia2023111413_006_tb.vcd");
    $dumpvars(0, zhuhoujia2023111413_006_tb);
end

endmodule