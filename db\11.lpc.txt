+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Legal Partition Candidates                                                                                                                                                                                          ;
+----------------+-------+----------------+--------------+----------------+--------+-----------------+---------------+-----------------+-------+----------------+--------------+------------------+-------------------+
; Hierarchy      ; Input ; Constant Input ; Unused Input ; Floating Input ; Output ; Constant Output ; Unused Output ; Floating Output ; Bidir ; Constant Bidir ; Unused Bidir ; Input only Bidir ; Output only Bidir ;
+----------------+-------+----------------+--------------+----------------+--------+-----------------+---------------+-----------------+-------+----------------+--------------+------------------+-------------------+
; lcd_display    ; 35    ; 1              ; 16           ; 1              ; 11     ; 1               ; 1             ; 1               ; 0     ; 0              ; 0            ; 0                ; 0                 ;
; dht11_sensor   ; 2     ; 0              ; 0            ; 0              ; 33     ; 0               ; 0             ; 0               ; 1     ; 0              ; 0            ; 0                ; 0                 ;
; pwm_gen        ; 14    ; 0              ; 4            ; 0              ; 4      ; 0               ; 0             ; 0               ; 0     ; 0              ; 0            ; 0                ; 0                 ;
; motor_ctrl     ; 8     ; 0              ; 0            ; 0              ; 15     ; 0               ; 0             ; 0               ; 0     ; 0              ; 0            ; 0                ; 0                 ;
; obstacle_avoid ; 4     ; 0              ; 0            ; 0              ; 3      ; 0               ; 0             ; 0               ; 0     ; 0              ; 0            ; 0                ; 0                 ;
; line_follow    ; 6     ; 0              ; 0            ; 0              ; 3      ; 0               ; 0             ; 0               ; 0     ; 0              ; 0            ; 0                ; 0                 ;
+----------------+-------+----------------+--------------+----------------+--------+-----------------+---------------+-----------------+-------+----------------+--------------+------------------+-------------------+
