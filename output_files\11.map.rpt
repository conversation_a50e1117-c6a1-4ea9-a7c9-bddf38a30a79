Analysis & Synthesis report for 11
Fri Jul 18 11:40:16 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Analysis & Synthesis Summary
  3. Analysis & Synthesis Settings
  4. Parallel Compilation
  5. Analysis & Synthesis Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+----------------------------------------------------------------------------------+
; Analysis & Synthesis Summary                                                     ;
+------------------------------------+---------------------------------------------+
; Analysis & Synthesis Status        ; Failed - Fri Jul 18 11:40:16 2025           ;
; Quartus Prime Version              ; 18.1.0 Build 625 09/12/2018 SJ Lite Edition ;
; Revision Name                      ; 11                                          ;
; Top-level Entity Name              ; zhuhoujia2023111413_007                     ;
; Family                             ; Cyclone IV E                                ;
; Total logic elements               ; N/A until Partition Merge                   ;
;     Total combinational functions  ; N/A until Partition Merge                   ;
;     Dedicated logic registers      ; N/A until Partition Merge                   ;
; Total registers                    ; N/A until Partition Merge                   ;
; Total pins                         ; N/A until Partition Merge                   ;
; Total virtual pins                 ; N/A until Partition Merge                   ;
; Total memory bits                  ; N/A until Partition Merge                   ;
; Embedded Multiplier 9-bit elements ; N/A until Partition Merge                   ;
; Total PLLs                         ; N/A until Partition Merge                   ;
+------------------------------------+---------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Settings                                                                                   ;
+------------------------------------------------------------------+-------------------------+--------------------+
; Option                                                           ; Setting                 ; Default Value      ;
+------------------------------------------------------------------+-------------------------+--------------------+
; Device                                                           ; EP4CE6E22C8             ;                    ;
; Top-level entity name                                            ; zhuhoujia2023111413_007 ; 11                 ;
; Family name                                                      ; Cyclone IV E            ; Cyclone V          ;
; Use smart compilation                                            ; Off                     ; Off                ;
; Enable parallel Assembler and Timing Analyzer during compilation ; On                      ; On                 ;
; Enable compact report table                                      ; Off                     ; Off                ;
; Restructure Multiplexers                                         ; Auto                    ; Auto               ;
; Create Debugging Nodes for IP Cores                              ; Off                     ; Off                ;
; Preserve fewer node names                                        ; On                      ; On                 ;
; Intel FPGA IP Evaluation Mode                                    ; Enable                  ; Enable             ;
; Verilog Version                                                  ; Verilog_2001            ; Verilog_2001       ;
; VHDL Version                                                     ; VHDL_1993               ; VHDL_1993          ;
; State Machine Processing                                         ; Auto                    ; Auto               ;
; Safe State Machine                                               ; Off                     ; Off                ;
; Extract Verilog State Machines                                   ; On                      ; On                 ;
; Extract VHDL State Machines                                      ; On                      ; On                 ;
; Ignore Verilog initial constructs                                ; Off                     ; Off                ;
; Iteration limit for constant Verilog loops                       ; 5000                    ; 5000               ;
; Iteration limit for non-constant Verilog loops                   ; 250                     ; 250                ;
; Add Pass-Through Logic to Inferred RAMs                          ; On                      ; On                 ;
; Infer RAMs from Raw Logic                                        ; On                      ; On                 ;
; Parallel Synthesis                                               ; On                      ; On                 ;
; DSP Block Balancing                                              ; Auto                    ; Auto               ;
; NOT Gate Push-Back                                               ; On                      ; On                 ;
; Power-Up Don't Care                                              ; On                      ; On                 ;
; Remove Redundant Logic Cells                                     ; Off                     ; Off                ;
; Remove Duplicate Registers                                       ; On                      ; On                 ;
; Ignore CARRY Buffers                                             ; Off                     ; Off                ;
; Ignore CASCADE Buffers                                           ; Off                     ; Off                ;
; Ignore GLOBAL Buffers                                            ; Off                     ; Off                ;
; Ignore ROW GLOBAL Buffers                                        ; Off                     ; Off                ;
; Ignore LCELL Buffers                                             ; Off                     ; Off                ;
; Ignore SOFT Buffers                                              ; On                      ; On                 ;
; Limit AHDL Integers to 32 Bits                                   ; Off                     ; Off                ;
; Optimization Technique                                           ; Balanced                ; Balanced           ;
; Carry Chain Length                                               ; 70                      ; 70                 ;
; Auto Carry Chains                                                ; On                      ; On                 ;
; Auto Open-Drain Pins                                             ; On                      ; On                 ;
; Perform WYSIWYG Primitive Resynthesis                            ; Off                     ; Off                ;
; Auto ROM Replacement                                             ; On                      ; On                 ;
; Auto RAM Replacement                                             ; On                      ; On                 ;
; Auto DSP Block Replacement                                       ; On                      ; On                 ;
; Auto Shift Register Replacement                                  ; Auto                    ; Auto               ;
; Allow Shift Register Merging across Hierarchies                  ; Auto                    ; Auto               ;
; Auto Clock Enable Replacement                                    ; On                      ; On                 ;
; Strict RAM Replacement                                           ; Off                     ; Off                ;
; Allow Synchronous Control Signals                                ; On                      ; On                 ;
; Force Use of Synchronous Clear Signals                           ; Off                     ; Off                ;
; Auto RAM Block Balancing                                         ; On                      ; On                 ;
; Auto RAM to Logic Cell Conversion                                ; Off                     ; Off                ;
; Auto Resource Sharing                                            ; Off                     ; Off                ;
; Allow Any RAM Size For Recognition                               ; Off                     ; Off                ;
; Allow Any ROM Size For Recognition                               ; Off                     ; Off                ;
; Allow Any Shift Register Size For Recognition                    ; Off                     ; Off                ;
; Use LogicLock Constraints during Resource Balancing              ; On                      ; On                 ;
; Ignore translate_off and synthesis_off directives                ; Off                     ; Off                ;
; Timing-Driven Synthesis                                          ; On                      ; On                 ;
; Report Parameter Settings                                        ; On                      ; On                 ;
; Report Source Assignments                                        ; On                      ; On                 ;
; Report Connectivity Checks                                       ; On                      ; On                 ;
; Ignore Maximum Fan-Out Assignments                               ; Off                     ; Off                ;
; Synchronization Register Chain Length                            ; 2                       ; 2                  ;
; Power Optimization During Synthesis                              ; Normal compilation      ; Normal compilation ;
; HDL message level                                                ; Level2                  ; Level2             ;
; Suppress Register Optimization Related Messages                  ; Off                     ; Off                ;
; Number of Removed Registers Reported in Synthesis Report         ; 5000                    ; 5000               ;
; Number of Swept Nodes Reported in Synthesis Report               ; 5000                    ; 5000               ;
; Number of Inverted Registers Reported in Synthesis Report        ; 100                     ; 100                ;
; Clock MUX Protection                                             ; On                      ; On                 ;
; Auto Gated Clock Conversion                                      ; Off                     ; Off                ;
; Block Design Naming                                              ; Auto                    ; Auto               ;
; SDC constraint protection                                        ; Off                     ; Off                ;
; Synthesis Effort                                                 ; Auto                    ; Auto               ;
; Shift Register Replacement - Allow Asynchronous Clear Signal     ; On                      ; On                 ;
; Pre-Mapping Resynthesis Optimization                             ; Off                     ; Off                ;
; Analysis & Synthesis Message Level                               ; Medium                  ; Medium             ;
; Disable Register Merging Across Hierarchies                      ; Auto                    ; Auto               ;
; Resource Aware Inference For Block RAM                           ; On                      ; On                 ;
+------------------------------------------------------------------+-------------------------+--------------------+


+------------------------------------------+
; Parallel Compilation                     ;
+----------------------------+-------------+
; Processors                 ; Number      ;
+----------------------------+-------------+
; Number detected on machine ; 20          ;
; Maximum allowed            ; 14          ;
;                            ;             ;
; Average used               ; 1.00        ;
; Maximum used               ; 1           ;
;                            ;             ;
; Usage by Processor         ; % Time Used ;
;     Processor 1            ; 100.0%      ;
+----------------------------+-------------+


+-------------------------------+
; Analysis & Synthesis Messages ;
+-------------------------------+
Info: *******************************************************************
Info: Running Quartus Prime Analysis & Synthesis
    Info: Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
    Info: Processing started: Fri Jul 18 11:40:09 2025
Info: Command: quartus_map --read_settings_files=on --write_settings_files=off 11 -c 11
Warning (18236): Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance.
Info (20030): Parallel compilation is enabled and will use 14 of the 14 processors detected
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_001.v
    Info (12023): Found entity 1: zhuhoujia2023111413_001 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_002.v
    Info (12023): Found entity 1: zhuhoujia2023111413_002 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_002.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_003.v
    Info (12023): Found entity 1: zhuhoujia2023111413_003 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_003.v Line: 1
Error (10170): Verilog HDL syntax error at zhuhoujia2023111413_004.v(348) near text: "endmodule";  expecting a description. Check for and fix any syntax errors that appear immediately before or at the specified keyword. The Intel FPGA Knowledge Database contains many articles with specific details on how to resolve this error. Visit the Knowledge Database at https://www.altera.com/support/support-resources/knowledge-base/search.html and search for this specific error message number. File: E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v Line: 348
Info (12021): Found 0 design units, including 0 entities, in source file zhuhoujia2023111413_004.v
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_005.v
    Info (12023): Found entity 1: zhuhoujia2023111413_005 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_006.v
    Info (12023): Found entity 1: zhuhoujia2023111413_006 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_007.v
    Info (12023): Found entity 1: zhuhoujia2023111413_007 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v Line: 1
Error: Quartus Prime Analysis & Synthesis was unsuccessful. 1 error, 1 warning
    Error: Peak virtual memory: 4711 megabytes
    Error: Processing ended: Fri Jul 18 11:40:16 2025
    Error: Elapsed time: 00:00:07
    Error: Total CPU time (on all processors): 00:00:14


