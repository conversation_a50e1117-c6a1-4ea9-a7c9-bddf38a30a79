Analysis & Synthesis report for 11
Thu Jul 17 01:45:57 2025
Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition


---------------------
; Table of Contents ;
---------------------
  1. Legal Notice
  2. Analysis & Synthesis Summary
  3. Analysis & Synthesis Settings
  4. Parallel Compilation
  5. Analysis & Synthesis Source Files Read
  6. Analysis & Synthesis Resource Usage Summary
  7. Analysis & Synthesis Resource Utilization by Entity
  8. State Machine - |zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display|state
  9. State Machine - |zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor|state
 10. Registers Removed During Synthesis
 11. General Register Statistics
 12. Inverted Register Statistics
 13. Multiplexer Restructuring Statistics (Restructuring Performed)
 14. Parameter Settings for User Entity Instance: zhuhoujia2023111413_001:line_follow
 15. Parameter Settings for User Entity Instance: zhuhoujia2023111413_002:obstacle_avoid
 16. Parameter Settings for User Entity Instance: zhuhoujia2023111413_003:motor_ctrl
 17. Parameter Settings for User Entity Instance: zhuhoujia2023111413_005:dht11_sensor
 18. Parameter Settings for User Entity Instance: zhuhoujia2023111413_006:lcd_display
 19. Port Connectivity Checks: "zhuhoujia2023111413_003:motor_ctrl"
 20. Port Connectivity Checks: "zhuhoujia2023111413_002:obstacle_avoid"
 21. Port Connectivity Checks: "zhuhoujia2023111413_001:line_follow"
 22. Post-Synthesis Netlist Statistics for Top Partition
 23. Elapsed Time Per Partition
 24. Analysis & Synthesis Messages



----------------
; Legal Notice ;
----------------
Copyright (C) 2018  Intel Corporation. All rights reserved.
Your use of Intel Corporation's design tools, logic functions 
and other software and tools, and its AMPP partner logic 
functions, and any output files from any of the foregoing 
(including device programming or simulation files), and any 
associated documentation or information are expressly subject 
to the terms and conditions of the Intel Program License 
Subscription Agreement, the Intel Quartus Prime License Agreement,
the Intel FPGA IP License Agreement, or other applicable license
agreement, including, without limitation, that your use is for
the sole purpose of programming logic devices manufactured by
Intel and sold by Intel or its authorized distributors.  Please
refer to the applicable agreement for further details.



+----------------------------------------------------------------------------------+
; Analysis & Synthesis Summary                                                     ;
+------------------------------------+---------------------------------------------+
; Analysis & Synthesis Status        ; Successful - Thu Jul 17 01:45:57 2025       ;
; Quartus Prime Version              ; 18.1.0 Build 625 09/12/2018 SJ Lite Edition ;
; Revision Name                      ; 11                                          ;
; Top-level Entity Name              ; zhuhoujia2023111413_007                     ;
; Family                             ; Cyclone IV E                                ;
; Total logic elements               ; 201                                         ;
;     Total combinational functions  ; 168                                         ;
;     Dedicated logic registers      ; 134                                         ;
; Total registers                    ; 134                                         ;
; Total pins                         ; 28                                          ;
; Total virtual pins                 ; 0                                           ;
; Total memory bits                  ; 0                                           ;
; Embedded Multiplier 9-bit elements ; 0                                           ;
; Total PLLs                         ; 0                                           ;
+------------------------------------+---------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Settings                                                                                   ;
+------------------------------------------------------------------+-------------------------+--------------------+
; Option                                                           ; Setting                 ; Default Value      ;
+------------------------------------------------------------------+-------------------------+--------------------+
; Device                                                           ; EP4CE6E22C8             ;                    ;
; Top-level entity name                                            ; zhuhoujia2023111413_007 ; 11                 ;
; Family name                                                      ; Cyclone IV E            ; Cyclone V          ;
; Use smart compilation                                            ; Off                     ; Off                ;
; Enable parallel Assembler and Timing Analyzer during compilation ; On                      ; On                 ;
; Enable compact report table                                      ; Off                     ; Off                ;
; Restructure Multiplexers                                         ; Auto                    ; Auto               ;
; Create Debugging Nodes for IP Cores                              ; Off                     ; Off                ;
; Preserve fewer node names                                        ; On                      ; On                 ;
; Intel FPGA IP Evaluation Mode                                    ; Enable                  ; Enable             ;
; Verilog Version                                                  ; Verilog_2001            ; Verilog_2001       ;
; VHDL Version                                                     ; VHDL_1993               ; VHDL_1993          ;
; State Machine Processing                                         ; Auto                    ; Auto               ;
; Safe State Machine                                               ; Off                     ; Off                ;
; Extract Verilog State Machines                                   ; On                      ; On                 ;
; Extract VHDL State Machines                                      ; On                      ; On                 ;
; Ignore Verilog initial constructs                                ; Off                     ; Off                ;
; Iteration limit for constant Verilog loops                       ; 5000                    ; 5000               ;
; Iteration limit for non-constant Verilog loops                   ; 250                     ; 250                ;
; Add Pass-Through Logic to Inferred RAMs                          ; On                      ; On                 ;
; Infer RAMs from Raw Logic                                        ; On                      ; On                 ;
; Parallel Synthesis                                               ; On                      ; On                 ;
; DSP Block Balancing                                              ; Auto                    ; Auto               ;
; NOT Gate Push-Back                                               ; On                      ; On                 ;
; Power-Up Don't Care                                              ; On                      ; On                 ;
; Remove Redundant Logic Cells                                     ; Off                     ; Off                ;
; Remove Duplicate Registers                                       ; On                      ; On                 ;
; Ignore CARRY Buffers                                             ; Off                     ; Off                ;
; Ignore CASCADE Buffers                                           ; Off                     ; Off                ;
; Ignore GLOBAL Buffers                                            ; Off                     ; Off                ;
; Ignore ROW GLOBAL Buffers                                        ; Off                     ; Off                ;
; Ignore LCELL Buffers                                             ; Off                     ; Off                ;
; Ignore SOFT Buffers                                              ; On                      ; On                 ;
; Limit AHDL Integers to 32 Bits                                   ; Off                     ; Off                ;
; Optimization Technique                                           ; Balanced                ; Balanced           ;
; Carry Chain Length                                               ; 70                      ; 70                 ;
; Auto Carry Chains                                                ; On                      ; On                 ;
; Auto Open-Drain Pins                                             ; On                      ; On                 ;
; Perform WYSIWYG Primitive Resynthesis                            ; Off                     ; Off                ;
; Auto ROM Replacement                                             ; On                      ; On                 ;
; Auto RAM Replacement                                             ; On                      ; On                 ;
; Auto DSP Block Replacement                                       ; On                      ; On                 ;
; Auto Shift Register Replacement                                  ; Auto                    ; Auto               ;
; Allow Shift Register Merging across Hierarchies                  ; Auto                    ; Auto               ;
; Auto Clock Enable Replacement                                    ; On                      ; On                 ;
; Strict RAM Replacement                                           ; Off                     ; Off                ;
; Allow Synchronous Control Signals                                ; On                      ; On                 ;
; Force Use of Synchronous Clear Signals                           ; Off                     ; Off                ;
; Auto RAM Block Balancing                                         ; On                      ; On                 ;
; Auto RAM to Logic Cell Conversion                                ; Off                     ; Off                ;
; Auto Resource Sharing                                            ; Off                     ; Off                ;
; Allow Any RAM Size For Recognition                               ; Off                     ; Off                ;
; Allow Any ROM Size For Recognition                               ; Off                     ; Off                ;
; Allow Any Shift Register Size For Recognition                    ; Off                     ; Off                ;
; Use LogicLock Constraints during Resource Balancing              ; On                      ; On                 ;
; Ignore translate_off and synthesis_off directives                ; Off                     ; Off                ;
; Timing-Driven Synthesis                                          ; On                      ; On                 ;
; Report Parameter Settings                                        ; On                      ; On                 ;
; Report Source Assignments                                        ; On                      ; On                 ;
; Report Connectivity Checks                                       ; On                      ; On                 ;
; Ignore Maximum Fan-Out Assignments                               ; Off                     ; Off                ;
; Synchronization Register Chain Length                            ; 2                       ; 2                  ;
; Power Optimization During Synthesis                              ; Normal compilation      ; Normal compilation ;
; HDL message level                                                ; Level2                  ; Level2             ;
; Suppress Register Optimization Related Messages                  ; Off                     ; Off                ;
; Number of Removed Registers Reported in Synthesis Report         ; 5000                    ; 5000               ;
; Number of Swept Nodes Reported in Synthesis Report               ; 5000                    ; 5000               ;
; Number of Inverted Registers Reported in Synthesis Report        ; 100                     ; 100                ;
; Clock MUX Protection                                             ; On                      ; On                 ;
; Auto Gated Clock Conversion                                      ; Off                     ; Off                ;
; Block Design Naming                                              ; Auto                    ; Auto               ;
; SDC constraint protection                                        ; Off                     ; Off                ;
; Synthesis Effort                                                 ; Auto                    ; Auto               ;
; Shift Register Replacement - Allow Asynchronous Clear Signal     ; On                      ; On                 ;
; Pre-Mapping Resynthesis Optimization                             ; Off                     ; Off                ;
; Analysis & Synthesis Message Level                               ; Medium                  ; Medium             ;
; Disable Register Merging Across Hierarchies                      ; Auto                    ; Auto               ;
; Resource Aware Inference For Block RAM                           ; On                      ; On                 ;
+------------------------------------------------------------------+-------------------------+--------------------+


+------------------------------------------+
; Parallel Compilation                     ;
+----------------------------+-------------+
; Processors                 ; Number      ;
+----------------------------+-------------+
; Number detected on machine ; 20          ;
; Maximum allowed            ; 14          ;
;                            ;             ;
; Average used               ; 1.00        ;
; Maximum used               ; 14          ;
;                            ;             ;
; Usage by Processor         ; % Time Used ;
;     Processor 1            ; 100.0%      ;
;     Processors 2-14        ;   0.0%      ;
+----------------------------+-------------+


+------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Source Files Read                                                                                                   ;
+----------------------------------+-----------------+------------------------+--------------------------------------------------+---------+
; File Name with User-Entered Path ; Used in Netlist ; File Type              ; File Name with Absolute Path                     ; Library ;
+----------------------------------+-----------------+------------------------+--------------------------------------------------+---------+
; zhuhoujia2023111413_001.v        ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v ;         ;
; zhuhoujia2023111413_002.v        ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia2023111413_002.v ;         ;
; zhuhoujia2023111413_003.v        ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia2023111413_003.v ;         ;
; zhuhoujia2023111413_004.v        ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v ;         ;
; zhuhoujia2023111413_005.v        ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v ;         ;
; zhuhoujia2023111413_006.v        ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v ;         ;
; zhuhoujia2023111413_007.v        ; yes             ; User Verilog HDL File  ; E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v ;         ;
+----------------------------------+-----------------+------------------------+--------------------------------------------------+---------+


+---------------------------------------------------------------+
; Analysis & Synthesis Resource Usage Summary                   ;
+---------------------------------------------+-----------------+
; Resource                                    ; Usage           ;
+---------------------------------------------+-----------------+
; Estimated Total logic elements              ; 201             ;
;                                             ;                 ;
; Total combinational functions               ; 168             ;
; Logic element usage by number of LUT inputs ;                 ;
;     -- 4 input functions                    ; 78              ;
;     -- 3 input functions                    ; 31              ;
;     -- <=2 input functions                  ; 59              ;
;                                             ;                 ;
; Logic elements by mode                      ;                 ;
;     -- normal mode                          ; 123             ;
;     -- arithmetic mode                      ; 45              ;
;                                             ;                 ;
; Total registers                             ; 134             ;
;     -- Dedicated logic registers            ; 134             ;
;     -- I/O registers                        ; 0               ;
;                                             ;                 ;
; I/O pins                                    ; 28              ;
;                                             ;                 ;
; Embedded Multiplier 9-bit elements          ; 0               ;
;                                             ;                 ;
; Maximum fan-out node                        ; clk_50MHz~input ;
; Maximum fan-out                             ; 134             ;
; Total fan-out                               ; 1079            ;
; Average fan-out                             ; 3.01            ;
+---------------------------------------------+-----------------+


+-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Analysis & Synthesis Resource Utilization by Entity                                                                                                                                                                                                                               ;
+---------------------------------------------+---------------------+---------------------------+-------------+--------------+---------+-----------+------+--------------+-----------------------------------------------------------------+-------------------------+--------------+
; Compilation Hierarchy Node                  ; Combinational ALUTs ; Dedicated Logic Registers ; Memory Bits ; DSP Elements ; DSP 9x9 ; DSP 18x18 ; Pins ; Virtual Pins ; Full Hierarchy Name                                             ; Entity Name             ; Library Name ;
+---------------------------------------------+---------------------+---------------------------+-------------+--------------+---------+-----------+------+--------------+-----------------------------------------------------------------+-------------------------+--------------+
; |zhuhoujia2023111413_007                    ; 168 (0)             ; 134 (0)                   ; 0           ; 0            ; 0       ; 0         ; 28   ; 0            ; |zhuhoujia2023111413_007                                        ; zhuhoujia2023111413_007 ; work         ;
;    |zhuhoujia2023111413_001:line_follow|    ; 2 (2)               ; 2 (2)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |zhuhoujia2023111413_007|zhuhoujia2023111413_001:line_follow    ; zhuhoujia2023111413_001 ; work         ;
;    |zhuhoujia2023111413_002:obstacle_avoid| ; 1 (1)               ; 2 (2)                     ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |zhuhoujia2023111413_007|zhuhoujia2023111413_002:obstacle_avoid ; zhuhoujia2023111413_002 ; work         ;
;    |zhuhoujia2023111413_003:motor_ctrl|     ; 10 (10)             ; 14 (14)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |zhuhoujia2023111413_007|zhuhoujia2023111413_003:motor_ctrl     ; zhuhoujia2023111413_003 ; work         ;
;    |zhuhoujia2023111413_004:pwm_gen|        ; 15 (15)             ; 19 (19)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |zhuhoujia2023111413_007|zhuhoujia2023111413_004:pwm_gen        ; zhuhoujia2023111413_004 ; work         ;
;    |zhuhoujia2023111413_005:dht11_sensor|   ; 88 (88)             ; 66 (66)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor   ; zhuhoujia2023111413_005 ; work         ;
;    |zhuhoujia2023111413_006:lcd_display|    ; 52 (52)             ; 31 (31)                   ; 0           ; 0            ; 0       ; 0         ; 0    ; 0            ; |zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display    ; zhuhoujia2023111413_006 ; work         ;
+---------------------------------------------+---------------------+---------------------------+-------------+--------------+---------+-----------+------+--------------+-----------------------------------------------------------------+-------------------------+--------------+
Note: For table entries with two numbers listed, the numbers in parentheses indicate the number of resources of the given type used by the specific entity alone. The numbers listed outside of parentheses indicate the total resources of the given type used by the specific entity and all of its sub-entities in the hierarchy.


Encoding Type:  One-Hot
+-----------------------------------------------------------------------------------------------------+
; State Machine - |zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display|state                  ;
+--------------------+-------------------+--------------------+------------+-------------+------------+
; Name               ; state.DISPLAY_HUM ; state.DISPLAY_TEMP ; state.INIT ; state.RESET ; state.IDLE ;
+--------------------+-------------------+--------------------+------------+-------------+------------+
; state.RESET        ; 0                 ; 0                  ; 0          ; 0           ; 0          ;
; state.INIT         ; 0                 ; 0                  ; 1          ; 1           ; 0          ;
; state.DISPLAY_TEMP ; 0                 ; 1                  ; 0          ; 1           ; 0          ;
; state.DISPLAY_HUM  ; 1                 ; 0                  ; 0          ; 1           ; 0          ;
; state.IDLE         ; 0                 ; 0                  ; 0          ; 1           ; 1          ;
+--------------------+-------------------+--------------------+------------+-------------+------------+


Encoding Type:  One-Hot
+-------------------------------------------------------------------------------------------+
; State Machine - |zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor|state       ;
+----------------+---------------+----------------+-------------+------------+--------------+
; Name           ; state.RECEIVE ; state.RESPONSE ; state.START ; state.IDLE ; state.FINISH ;
+----------------+---------------+----------------+-------------+------------+--------------+
; state.IDLE     ; 0             ; 0              ; 0           ; 0          ; 0            ;
; state.START    ; 0             ; 0              ; 1           ; 1          ; 0            ;
; state.RESPONSE ; 0             ; 1              ; 0           ; 1          ; 0            ;
; state.RECEIVE  ; 1             ; 0              ; 0           ; 1          ; 0            ;
; state.FINISH   ; 0             ; 0              ; 0           ; 1          ; 1            ;
+----------------+---------------+----------------+-------------+------------+--------------+


+-------------------------------------------------------------------------------------------------------------------+
; Registers Removed During Synthesis                                                                                ;
+---------------------------------------------------+---------------------------------------------------------------+
; Register name                                     ; Reason for Removal                                            ;
+---------------------------------------------------+---------------------------------------------------------------+
; zhuhoujia2023111413_003:motor_ctrl|motor_speed[1] ; Merged with zhuhoujia2023111413_003:motor_ctrl|motor_speed[4] ;
; zhuhoujia2023111413_004:pwm_gen|duty_cycle[1]     ; Merged with zhuhoujia2023111413_004:pwm_gen|duty_cycle[4]     ;
; zhuhoujia2023111413_006:lcd_display|state~9       ; Lost fanout                                                   ;
; zhuhoujia2023111413_006:lcd_display|state~10      ; Lost fanout                                                   ;
; zhuhoujia2023111413_006:lcd_display|state~12      ; Lost fanout                                                   ;
; zhuhoujia2023111413_005:dht11_sensor|state~9      ; Lost fanout                                                   ;
; zhuhoujia2023111413_005:dht11_sensor|state~10     ; Lost fanout                                                   ;
; Total Number of Removed Registers = 7             ;                                                               ;
+---------------------------------------------------+---------------------------------------------------------------+


+------------------------------------------------------+
; General Register Statistics                          ;
+----------------------------------------------+-------+
; Statistic                                    ; Value ;
+----------------------------------------------+-------+
; Total registers                              ; 134   ;
; Number of registers using Synchronous Clear  ; 42    ;
; Number of registers using Synchronous Load   ; 0     ;
; Number of registers using Asynchronous Clear ; 134   ;
; Number of registers using Asynchronous Load  ; 0     ;
; Number of registers using Clock Enable       ; 69    ;
; Number of registers using Preset             ; 0     ;
+----------------------------------------------+-------+


+-----------------------------------------------------------------+
; Inverted Register Statistics                                    ;
+-------------------------------------------------------+---------+
; Inverted Register                                     ; Fan out ;
+-------------------------------------------------------+---------+
; zhuhoujia2023111413_003:motor_ctrl|final_control[0]   ; 8       ;
; zhuhoujia2023111413_003:motor_ctrl|final_control[2]   ; 8       ;
; zhuhoujia2023111413_003:motor_ctrl|final_control[1]   ; 8       ;
; zhuhoujia2023111413_001:line_follow|control_signal[1] ; 2       ;
; zhuhoujia2023111413_001:line_follow|control_signal[0] ; 2       ;
; zhuhoujia2023111413_005:dht11_sensor|dht11_out        ; 1       ;
; Total number of inverted registers = 6                ;         ;
+-------------------------------------------------------+---------+


+---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
; Multiplexer Restructuring Statistics (Restructuring Performed)                                                                                                                              ;
+--------------------+-----------+---------------+----------------------+------------------------+------------+-------------------------------------------------------------------------------+
; Multiplexer Inputs ; Bus Width ; Baseline Area ; Area if Restructured ; Saving if Restructured ; Registered ; Example Multiplexer Output                                                    ;
+--------------------+-----------+---------------+----------------------+------------------------+------------+-------------------------------------------------------------------------------+
; 5:1                ; 6 bits    ; 18 LEs        ; 6 LEs                ; 12 LEs                 ; Yes        ; |zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor|bit_counter[0]  ;
; 5:1                ; 16 bits   ; 48 LEs        ; 16 LEs               ; 32 LEs                 ; Yes        ; |zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display|delay_counter[0] ;
; 7:1                ; 8 bits    ; 32 LEs        ; 16 LEs               ; 16 LEs                 ; Yes        ; |zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display|lcd_data[0]      ;
; 10:1               ; 20 bits   ; 120 LEs       ; 20 LEs               ; 100 LEs                ; Yes        ; |zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor|timer[16]       ;
; 5:1                ; 2 bits    ; 6 LEs         ; 4 LEs                ; 2 LEs                  ; Yes        ; |zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor|dht11_oe        ;
; 7:1                ; 3 bits    ; 12 LEs        ; 9 LEs                ; 3 LEs                  ; No         ; |zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display|Selector0        ;
; 9:1                ; 2 bits    ; 12 LEs        ; 6 LEs                ; 6 LEs                  ; No         ; |zhuhoujia2023111413_007|zhuhoujia2023111413_006:lcd_display|Selector4        ;
; 9:1                ; 5 bits    ; 30 LEs        ; 20 LEs               ; 10 LEs                 ; No         ; |zhuhoujia2023111413_007|zhuhoujia2023111413_005:dht11_sensor|Selector0       ;
+--------------------+-----------+---------------+----------------------+------------------------+------------+-------------------------------------------------------------------------------+


+----------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: zhuhoujia2023111413_001:line_follow ;
+----------------+-------+---------------------------------------------------------+
; Parameter Name ; Value ; Type                                                    ;
+----------------+-------+---------------------------------------------------------+
; FORWARD_FULL   ; 000   ; Unsigned Binary                                         ;
; FORWARD_SLOW   ; 001   ; Unsigned Binary                                         ;
; LEFT_SLOW      ; 010   ; Unsigned Binary                                         ;
; LEFT_FAST      ; 011   ; Unsigned Binary                                         ;
; RIGHT_SLOW     ; 100   ; Unsigned Binary                                         ;
; RIGHT_FAST     ; 101   ; Unsigned Binary                                         ;
; BACK_FULL      ; 110   ; Unsigned Binary                                         ;
; STOP           ; 111   ; Unsigned Binary                                         ;
+----------------+-------+---------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+-------------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: zhuhoujia2023111413_002:obstacle_avoid ;
+----------------+-------+------------------------------------------------------------+
; Parameter Name ; Value ; Type                                                       ;
+----------------+-------+------------------------------------------------------------+
; FORWARD_FULL   ; 000   ; Unsigned Binary                                            ;
; FORWARD_SLOW   ; 001   ; Unsigned Binary                                            ;
; LEFT_SLOW      ; 010   ; Unsigned Binary                                            ;
; LEFT_FAST      ; 011   ; Unsigned Binary                                            ;
; RIGHT_SLOW     ; 100   ; Unsigned Binary                                            ;
; RIGHT_FAST     ; 101   ; Unsigned Binary                                            ;
; BACK_FULL      ; 110   ; Unsigned Binary                                            ;
; STOP           ; 111   ; Unsigned Binary                                            ;
+----------------+-------+------------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+---------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: zhuhoujia2023111413_003:motor_ctrl ;
+----------------+----------+-----------------------------------------------------+
; Parameter Name ; Value    ; Type                                                ;
+----------------+----------+-----------------------------------------------------+
; FORWARD_FULL   ; 000      ; Unsigned Binary                                     ;
; FORWARD_SLOW   ; 001      ; Unsigned Binary                                     ;
; LEFT_SLOW      ; 010      ; Unsigned Binary                                     ;
; LEFT_FAST      ; 011      ; Unsigned Binary                                     ;
; RIGHT_SLOW     ; 100      ; Unsigned Binary                                     ;
; RIGHT_FAST     ; 101      ; Unsigned Binary                                     ;
; BACK_FULL      ; 110      ; Unsigned Binary                                     ;
; STOP           ; 111      ; Unsigned Binary                                     ;
; MOTOR_FORWARD  ; 1010     ; Unsigned Binary                                     ;
; MOTOR_LEFT     ; 0010     ; Unsigned Binary                                     ;
; MOTOR_RIGHT    ; 1000     ; Unsigned Binary                                     ;
; MOTOR_BACK     ; 0101     ; Unsigned Binary                                     ;
; MOTOR_STOP     ; 0000     ; Unsigned Binary                                     ;
; SPEED_FULL     ; 11111111 ; Unsigned Binary                                     ;
; SPEED_HIGH     ; 11001000 ; Unsigned Binary                                     ;
; SPEED_MEDIUM   ; 10010110 ; Unsigned Binary                                     ;
; SPEED_LOW      ; 01100100 ; Unsigned Binary                                     ;
; SPEED_SLOW     ; 01010000 ; Unsigned Binary                                     ;
; SPEED_ZERO     ; 00000000 ; Unsigned Binary                                     ;
+----------------+----------+-----------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+-----------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: zhuhoujia2023111413_005:dht11_sensor ;
+----------------+----------------------+-------------------------------------------+
; Parameter Name ; Value                ; Type                                      ;
+----------------+----------------------+-------------------------------------------+
; WAIT_TIME      ; 11110100001001000000 ; Unsigned Binary                           ;
; START_LOW      ; 1001110001000000     ; Unsigned Binary                           ;
; START_HIGH     ; 0000011001000000     ; Unsigned Binary                           ;
; IDLE           ; 000                  ; Unsigned Binary                           ;
; START          ; 001                  ; Unsigned Binary                           ;
; RESPONSE       ; 010                  ; Unsigned Binary                           ;
; RECEIVE        ; 011                  ; Unsigned Binary                           ;
; FINISH         ; 100                  ; Unsigned Binary                           ;
+----------------+----------------------+-------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+----------------------------------------------------------------------------------+
; Parameter Settings for User Entity Instance: zhuhoujia2023111413_006:lcd_display ;
+----------------+-------+---------------------------------------------------------+
; Parameter Name ; Value ; Type                                                    ;
+----------------+-------+---------------------------------------------------------+
; RESET          ; 0000  ; Unsigned Binary                                         ;
; INIT           ; 0001  ; Unsigned Binary                                         ;
; DISPLAY_TEMP   ; 0010  ; Unsigned Binary                                         ;
; DISPLAY_HUM    ; 0011  ; Unsigned Binary                                         ;
; IDLE           ; 0100  ; Unsigned Binary                                         ;
+----------------+-------+---------------------------------------------------------+
Note: In order to hide this table in the UI and the text report file, please set the "Show Parameter Settings in Synthesis Report" option in "Analysis and Synthesis Settings -> More Settings" to "Off".


+-------------------------------------------------------------------------------------------------------------------------+
; Port Connectivity Checks: "zhuhoujia2023111413_003:motor_ctrl"                                                          ;
+---------------+--------+----------+-------------------------------------------------------------------------------------+
; Port          ; Type   ; Severity ; Details                                                                             ;
+---------------+--------+----------+-------------------------------------------------------------------------------------+
; final_control ; Output ; Info     ; Connected to dangling logic. Logic that only feeds a dangling port will be removed. ;
+---------------+--------+----------+-------------------------------------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------------------+
; Port Connectivity Checks: "zhuhoujia2023111413_002:obstacle_avoid"                                                          ;
+-------------------+--------+----------+-------------------------------------------------------------------------------------+
; Port              ; Type   ; Severity ; Details                                                                             ;
+-------------------+--------+----------+-------------------------------------------------------------------------------------+
; control_signal[2] ; Output ; Info     ; Connected to dangling logic. Logic that only feeds a dangling port will be removed. ;
+-------------------+--------+----------+-------------------------------------------------------------------------------------+


+-----------------------------------------------------------------------------------------------------------------------------+
; Port Connectivity Checks: "zhuhoujia2023111413_001:line_follow"                                                             ;
+-------------------+--------+----------+-------------------------------------------------------------------------------------+
; Port              ; Type   ; Severity ; Details                                                                             ;
+-------------------+--------+----------+-------------------------------------------------------------------------------------+
; control_signal[2] ; Output ; Info     ; Connected to dangling logic. Logic that only feeds a dangling port will be removed. ;
+-------------------+--------+----------+-------------------------------------------------------------------------------------+


+-----------------------------------------------------+
; Post-Synthesis Netlist Statistics for Top Partition ;
+-----------------------+-----------------------------+
; Type                  ; Count                       ;
+-----------------------+-----------------------------+
; boundary_port         ; 28                          ;
; cycloneiii_ff         ; 134                         ;
;     CLR               ; 65                          ;
;     ENA CLR           ; 27                          ;
;     ENA CLR SCLR      ; 42                          ;
; cycloneiii_io_obuf    ; 1                           ;
; cycloneiii_lcell_comb ; 169                         ;
;     arith             ; 45                          ;
;         2 data inputs ; 44                          ;
;         3 data inputs ; 1                           ;
;     normal            ; 124                         ;
;         0 data inputs ; 1                           ;
;         1 data inputs ; 5                           ;
;         2 data inputs ; 10                          ;
;         3 data inputs ; 30                          ;
;         4 data inputs ; 78                          ;
;                       ;                             ;
; Max LUT depth         ; 7.00                        ;
; Average LUT depth     ; 2.95                        ;
+-----------------------+-----------------------------+


+-------------------------------+
; Elapsed Time Per Partition    ;
+----------------+--------------+
; Partition Name ; Elapsed Time ;
+----------------+--------------+
; Top            ; 00:00:00     ;
+----------------+--------------+


+-------------------------------+
; Analysis & Synthesis Messages ;
+-------------------------------+
Info: *******************************************************************
Info: Running Quartus Prime Analysis & Synthesis
    Info: Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
    Info: Processing started: Thu Jul 17 01:45:50 2025
Info: Command: quartus_map --read_settings_files=on --write_settings_files=off 11 -c 11
Warning (18236): Number of processors has not been specified which may cause overloading on shared machines.  Set the global assignment NUM_PARALLEL_PROCESSORS in your QSF to an appropriate value for best performance.
Info (20030): Parallel compilation is enabled and will use 14 of the 14 processors detected
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_001.v
    Info (12023): Found entity 1: zhuhoujia2023111413_001 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_002.v
    Info (12023): Found entity 1: zhuhoujia2023111413_002 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_002.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_003.v
    Info (12023): Found entity 1: zhuhoujia2023111413_003 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_003.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_004.v
    Info (12023): Found entity 1: zhuhoujia2023111413_004 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_005.v
    Info (12023): Found entity 1: zhuhoujia2023111413_005 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_006.v
    Info (12023): Found entity 1: zhuhoujia2023111413_006 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v Line: 1
Info (12021): Found 1 design units, including 1 entities, in source file zhuhoujia2023111413_007.v
    Info (12023): Found entity 1: zhuhoujia2023111413_007 File: E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v Line: 1
Info (12127): Elaborating entity "zhuhoujia2023111413_007" for the top level hierarchy
Info (12128): Elaborating entity "zhuhoujia2023111413_001" for hierarchy "zhuhoujia2023111413_001:line_follow" File: E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v Line: 40
Warning (10272): Verilog HDL Case Statement warning at zhuhoujia2023111413_001.v(40): case item expression covers a value already covered by a previous case item File: E:/zhuhoujia2023111413/zhuhoujia2023111413_001.v Line: 40
Info (12128): Elaborating entity "zhuhoujia2023111413_002" for hierarchy "zhuhoujia2023111413_002:obstacle_avoid" File: E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v Line: 47
Info (12128): Elaborating entity "zhuhoujia2023111413_003" for hierarchy "zhuhoujia2023111413_003:motor_ctrl" File: E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v Line: 57
Info (12128): Elaborating entity "zhuhoujia2023111413_004" for hierarchy "zhuhoujia2023111413_004:pwm_gen" File: E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v Line: 64
Warning (10230): Verilog HDL assignment warning at zhuhoujia2023111413_004.v(16): truncated value with size 32 to match size of target (8) File: E:/zhuhoujia2023111413/zhuhoujia2023111413_004.v Line: 16
Info (12128): Elaborating entity "zhuhoujia2023111413_005" for hierarchy "zhuhoujia2023111413_005:dht11_sensor" File: E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v Line: 73
Warning (10230): Verilog HDL assignment warning at zhuhoujia2023111413_005.v(50): truncated value with size 32 to match size of target (20) File: E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v Line: 50
Warning (10230): Verilog HDL assignment warning at zhuhoujia2023111413_005.v(61): truncated value with size 32 to match size of target (20) File: E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v Line: 61
Warning (10230): Verilog HDL assignment warning at zhuhoujia2023111413_005.v(71): truncated value with size 32 to match size of target (20) File: E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v Line: 71
Warning (10230): Verilog HDL assignment warning at zhuhoujia2023111413_005.v(80): truncated value with size 32 to match size of target (6) File: E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v Line: 80
Warning (10230): Verilog HDL assignment warning at zhuhoujia2023111413_005.v(83): truncated value with size 32 to match size of target (20) File: E:/zhuhoujia2023111413/zhuhoujia2023111413_005.v Line: 83
Info (12128): Elaborating entity "zhuhoujia2023111413_006" for hierarchy "zhuhoujia2023111413_006:lcd_display" File: E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v Line: 85
Warning (10036): Verilog HDL or VHDL warning at zhuhoujia2023111413_006.v(15): object "char_counter" assigned a value but never read File: E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v Line: 15
Warning (10230): Verilog HDL assignment warning at zhuhoujia2023111413_006.v(40): truncated value with size 32 to match size of target (16) File: E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v Line: 40
Warning (10230): Verilog HDL assignment warning at zhuhoujia2023111413_006.v(50): truncated value with size 32 to match size of target (16) File: E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v Line: 50
Warning (10240): Verilog HDL Always Construct warning at zhuhoujia2023111413_006.v(23): inferring latch(es) for variable "lcd_rw", which holds its previous value in one or more paths through the always construct File: E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v Line: 23
Info (10041): Inferred latch for "lcd_rw" at zhuhoujia2023111413_006.v(23) File: E:/zhuhoujia2023111413/zhuhoujia2023111413_006.v Line: 23
Info (13000): Registers with preset signals will power-up high File: E:/zhuhoujia2023111413/zhuhoujia2023111413_003.v Line: 70
Info (13003): DEV_CLRn pin will set, and not reset, register with preset signal due to NOT Gate Push-Back
Warning (13024): Output pins are stuck at VCC or GND
    Warning (13410): Pin "lcd_rw" is stuck at GND File: E:/zhuhoujia2023111413/zhuhoujia2023111413_007.v Line: 20
Info (286030): Timing-Driven Synthesis is running
Info (17049): 5 registers lost all their fanouts during netlist optimizations.
Info (16010): Generating hard_block partition "hard_block:auto_generated_inst"
    Info (16011): Adding 0 node(s), including 0 DDIO, 0 PLL, 0 transceiver and 0 LCELL
Info (21057): Implemented 232 device resources after synthesis - the final resource count might be different
    Info (21058): Implemented 8 input pins
    Info (21059): Implemented 19 output pins
    Info (21060): Implemented 1 bidirectional pins
    Info (21061): Implemented 204 logic cells
Info: Quartus Prime Analysis & Synthesis was successful. 0 errors, 14 warnings
    Info: Peak virtual memory: 4834 megabytes
    Info: Processing ended: Thu Jul 17 01:45:57 2025
    Info: Elapsed time: 00:00:07
    Info: Total CPU time (on all processors): 00:00:14


