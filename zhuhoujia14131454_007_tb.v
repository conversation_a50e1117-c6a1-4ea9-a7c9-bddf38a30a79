`timescale 1ns / 1ps

module zhuhoujia14131454_007_tb;

// 测试信号定义
reg clk_50MHz;
reg rst_n;
reg [3:0] line_sensor;
reg [1:0] obstacle_sensor;
wire dht11_data;
wire motor1_a, motor1_b;
wire motor2_a, motor2_b;
wire motor3_a, motor3_b;
wire motor4_a, motor4_b;
wire clk_1m;
wire clk_100hz;
wire lcd_rst;
wire lcd_rw;
wire lcd_en;
wire [7:0] lcd_data;

// DHT11数据模拟
reg dht11_data_reg;
reg dht11_oe_sim;
assign dht11_data = dht11_oe_sim ? dht11_data_reg : 1'bz;

// 计数器变量声明
integer clk_1m_count;
integer clk_100hz_count;

// 实例化被测试的顶层模块
zhuhoujia14131454_007 uut (
    .clk_50MHz(clk_50MHz),
    .rst_n(rst_n),
    .line_sensor(line_sensor),
    .obstacle_sensor(obstacle_sensor),
    .dht11_data(dht11_data),
    .motor1_a(motor1_a),
    .motor1_b(motor1_b),
    .motor2_a(motor2_a),
    .motor2_b(motor2_b),
    .motor3_a(motor3_a),
    .motor3_b(motor3_b),
    .motor4_a(motor4_a),
    .motor4_b(motor4_b),
    .clk_1m(clk_1m),
    .clk_100hz(clk_100hz),
    .lcd_rst(lcd_rst),
    .lcd_rw(lcd_rw),
    .lcd_en(lcd_en),
    .lcd_data(lcd_data)
);

// 时钟生成 - 50MHz
initial begin
    clk_50MHz = 0;
    forever #10 clk_50MHz = ~clk_50MHz;
end

// 电机状态解码任务
task decode_motor_state;
    input motor_a, motor_b;
    begin
        case ({motor_a, motor_b})
            2'b00: $display("停止");
            2'b01: $display("反转");
            2'b10: $display("正转");
            2'b11: $display("制动");
        endcase
    end
endtask

// 系统状态显示任务
task display_system_status;
    begin
        $display("传感器输入:");
        $display("  循线传感器: %b", line_sensor);
        $display("  避障传感器: %b", obstacle_sensor);

        $display("内部控制信号:");
        $display("  循线控制: %b", uut.line_control);
        $display("  避障控制: %b", uut.obstacle_control);
        $display("  最终控制: %b", uut.final_control);
        $display("  电机控制命令: %d", uut.motor_control);
        $display("  速度控制值: %d", uut.speed_control);

        $display("电机输出状态:");
        $write("电机1(左前): ");
        decode_motor_state(motor1_a, motor1_b);
        $write("电机2(左后): ");
        decode_motor_state(motor2_a, motor2_b);
        $write("电机3(右前): ");
        decode_motor_state(motor3_a, motor3_b);
        $write("电机4(右后): ");
        decode_motor_state(motor4_a, motor4_b);

        $display("时钟输出:");
        $display("  1MHz时钟: %b", clk_1m);
        $display("  100Hz时钟: %b", clk_100hz);

        $display("LCD状态:");
        $display("  复位: %b, 读写: %b, 使能: %b", lcd_rst, lcd_rw, lcd_en);
        if (lcd_data >= 8'h20 && lcd_data <= 8'h7E) begin
            $display("  数据: 0x%02h ('%c')", lcd_data, lcd_data);
        end else begin
            $display("  数据: 0x%02h", lcd_data);
        end

        $display("温湿度数据:");
        $display("  温度: %d.%d°C", uut.temperature[15:8], uut.temperature[7:0]);
        $display("  湿度: %d.%d%%", uut.humidity[15:8], uut.humidity[7:0]);
        $display("  数据有效: %b", uut.dht11_valid);
        $display("");
    end
endtask

// 综合测试场景任务
task test_integrated_scenario;
    input [3:0] line_input;
    input [1:0] obstacle_input;
    begin
        line_sensor = line_input;
        obstacle_sensor = obstacle_input;
        #5000; // 等待系统稳定
        display_system_status();
    end
endtask

// DHT11响应模拟任务（简化版）
task simulate_dht11_simple;
    input [15:0] temp_data;
    input [15:0] humi_data;
    begin
        // 等待DHT11模块发起通信（使用延时代替wait）
        #20000; // 等待启动状态
        #2000;
        
        // 模拟DHT11响应
        dht11_oe_sim = 1;
        dht11_data_reg = 0; #1600; // 响应信号
        dht11_data_reg = 1; #1600;
        
        // 发送简化的数据（只发送几位作为演示）
        repeat(8) begin
            dht11_data_reg = 0; #1000;
            dht11_data_reg = 1; #1400; // 发送'1'
        end
        
        dht11_data_reg = 0; #1000;
        dht11_oe_sim = 0;
    end
endtask

// 主测试序列
initial begin
    // 初始化
    rst_n = 0;
    line_sensor = 4'b0000;
    obstacle_sensor = 2'b00;
    dht11_data_reg = 1;
    dht11_oe_sim = 0;
    
    $display("========================================");
    $display("智能小车系统集成测试开始");
    $display("========================================");
    
    // 释放复位
    #1000;
    rst_n = 1;
    #2000;
    
    // 测试1: 基本循线功能
    $display("【测试组1: 基本循线功能】");
    $display("=== 正常循线前进 ===");
    test_integrated_scenario(4'b0110, 2'b00);
    $display("=== 轻微左偏调整 ===");
    test_integrated_scenario(4'b1110, 2'b00);
    $display("=== 严重右偏调整 ===");
    test_integrated_scenario(4'b0001, 2'b00);
    $display("=== 到达终点停止 ===");
    test_integrated_scenario(4'b1111, 2'b00);

    // 测试2: 避障优先级功能
    $display("【测试组2: 避障优先级功能】");
    $display("=== 循线时右侧避障 ===");
    test_integrated_scenario(4'b0110, 2'b01);
    $display("=== 循线时左侧避障 ===");
    test_integrated_scenario(4'b0110, 2'b10);
    $display("=== 循线时前方避障 ===");
    test_integrated_scenario(4'b0110, 2'b11);
    $display("=== 左偏时右侧避障 ===");
    test_integrated_scenario(4'b1110, 2'b01);

    // 测试3: 复杂场景测试
    $display("【测试组3: 复杂场景测试】");
    $display("=== 丢失线路搜索 ===");
    test_integrated_scenario(4'b0000, 2'b00);
    $display("=== 严重左偏遇左侧障碍 ===");
    test_integrated_scenario(4'b1000, 2'b10);
    $display("=== 轻微右偏遇前方障碍 ===");
    test_integrated_scenario(4'b0111, 2'b11);
    
    // 测试4: 动态场景模拟
    $display("【测试组4: 动态场景模拟】");
    $display("模拟小车运行过程中的动态变化:");
    
    // 场景：正常循线 → 遇到障碍 → 避障 → 恢复循线
    line_sensor = 4'b0110; obstacle_sensor = 2'b00; #3000;
    $display("步骤1: 正常循线前进");
    display_system_status();

    line_sensor = 4'b0110; obstacle_sensor = 2'b01; #3000;
    $display("步骤2: 遇到右侧障碍，执行左转避障");
    display_system_status();

    line_sensor = 4'b1110; obstacle_sensor = 2'b00; #3000;
    $display("步骤3: 避障后线路左偏，调整方向");
    display_system_status();

    line_sensor = 4'b0110; obstacle_sensor = 2'b00; #3000;
    $display("步骤4: 恢复正常循线");
    display_system_status();
    
    // 测试5: 时钟输出验证
    $display("【测试组5: 时钟输出验证】");
    $display("验证1MHz和100Hz时钟输出...");
    
    // 简单的时钟计数验证
    clk_1m_count = 0;
    clk_100hz_count = 0;

    // 简化的时钟测试（避免使用fork-join）
    // 测量1MHz时钟
    repeat(50) begin
        @(posedge clk_1m);
        clk_1m_count = clk_1m_count + 1;
    end

    // 测量100Hz时钟
    repeat(3) begin
        @(posedge clk_100hz);
        clk_100hz_count = clk_100hz_count + 1;
    end
    
    $display("1MHz时钟计数: %d", clk_1m_count);
    $display("100Hz时钟计数: %d", clk_100hz_count);
    
    // 测试6: LCD显示验证
    $display("【测试组6: LCD显示验证】");
    $display("等待LCD显示\"2023111413\"...");
    
    // 等待LCD初始化完成（使用延时代替wait）
    #15000; // 等待LCD初始化和开始显示
    $display("LCD开始显示文本");
    
    // 观察几个字符的显示
    repeat(5) begin
        @(posedge clk_50MHz);
        if (uut.lcd_display.delay_counter == 16'd0) begin
            if (lcd_data >= 8'h30 && lcd_data <= 8'h39) begin
                $display("LCD显示字符: '%c'", lcd_data);
            end
        end
    end
    
    // 测试7: DHT11传感器模拟
    $display("【测试组7: DHT11传感器模拟】");
    fork
        simulate_dht11_simple(16'h1905, 16'h3C00); // 25.5°C, 60.0%
    join_none
    
    // 等待温湿度数据
    #20000;
    $display("DHT11数据读取结果:");
    $display("  温度: %d.%d°C", uut.temperature[15:8], uut.temperature[7:0]);
    $display("  湿度: %d.%d%%", uut.humidity[15:8], uut.humidity[7:0]);
    
    // 测试8: 系统复位验证
    $display("【测试组8: 系统复位验证】");
    $display("执行系统复位测试...");
    
    line_sensor = 4'b1111; obstacle_sensor = 2'b11; #1000;
    $display("复位前状态:");
    display_system_status();
    
    rst_n = 0; #1000;
    $display("复位后所有输出应为初始状态:");
    $display("电机状态: %b%b %b%b %b%b %b%b", 
             motor1_a, motor1_b, motor2_a, motor2_b, 
             motor3_a, motor3_b, motor4_a, motor4_b);
    $display("LCD复位: %b", lcd_rst);
    
    rst_n = 1; #2000;
    
    $display("\n========================================");
    $display("智能小车系统集成测试完成");
    $display("========================================");
    $finish;
end

// 系统状态监控
always @(uut.final_control) begin
    if ($time > 3000) begin
        $display("时间=%0t: 系统最终控制信号变化为 %b", $time, uut.final_control);
    end
end

// 电机状态监控
always @(motor1_a or motor1_b or motor2_a or motor2_b or motor3_a or motor3_b or motor4_a or motor4_b) begin
    if ($time > 3000) begin
        $display("时间=%0t: 电机状态发生变化", $time);
    end
end

// 生成波形文件
initial begin
    $dumpfile("zhuhoujia14131454_007_tb.vcd");
    $dumpvars(0, zhuhoujia14131454_007_tb);
end

endmodule
