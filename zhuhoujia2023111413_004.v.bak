module pwm_generator_module(
    input clk,
    input rst_n,
    input [7:0] speed_control,
    output reg [3:0] motor_pwm
);

reg [7:0] pwm_counter;
reg [7:0] duty_cycle;

always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        pwm_counter <= 8'd0;
        duty_cycle <= 8'd0;
    end else begin
        pwm_counter <= pwm_counter + 1;
        duty_cycle <= speed_control;
    end
end

// 生成4路PWM信号
always @(posedge clk or negedge rst_n) begin
    if (!rst_n) begin
        motor_pwm <= 4'b0000;
    end else begin
        motor_pwm[0] <= (pwm_counter < duty_cycle) ? 1'b1 : 1'b0;
        motor_pwm[1] <= (pwm_counter < duty_cycle) ? 1'b1 : 1'b0;
        motor_pwm[2] <= (pwm_counter < duty_cycle) ? 1'b1 : 1'b0;
        motor_pwm[3] <= (pwm_counter < duty_cycle) ? 1'b1 : 1'b0;
    end
end

endmodule
