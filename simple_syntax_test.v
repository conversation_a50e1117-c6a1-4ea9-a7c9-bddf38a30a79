`timescale 1ns / 1ps

// 简单的语法测试模块
module simple_syntax_test;

// 测试信号
reg clk;
reg rst_n;
reg [3:0] test_input;
wire [2:0] test_output;

// 计数器变量
integer count;

// 简单的测试模块实例（如果存在的话）
// 这里我们只做语法测试，不实例化具体模块

// 时钟生成
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// 简单的测试任务
task simple_test;
    input [3:0] input_val;
    begin
        test_input = input_val;
        #100;
        $display("测试输入: %b", input_val);
    end
endtask

// 主测试序列
initial begin
    // 初始化
    rst_n = 0;
    test_input = 4'b0000;
    count = 0;
    
    $display("========================================");
    $display("简单语法测试开始");
    $display("========================================");
    
    // 释放复位
    #100;
    rst_n = 1;
    #100;
    
    // 测试基本功能
    $display("=== 基本功能测试 ===");
    simple_test(4'b0001);
    simple_test(4'b0010);
    simple_test(4'b0100);
    simple_test(4'b1000);
    
    // 测试计数器
    $display("=== 计数器测试 ===");
    for (count = 0; count < 5; count = count + 1) begin
        $display("计数: %d", count);
        #100;
    end
    
    // 测试while循环
    $display("=== While循环测试 ===");
    count = 0;
    while (count < 3) begin
        $display("While计数: %d", count);
        count = count + 1;
        #100;
    end
    
    $display("========================================");
    $display("简单语法测试完成");
    $display("========================================");
    $finish;
end

// 监控信号变化
always @(test_input) begin
    if ($time > 200) begin
        $display("时间=%0t: 输入变化为 %b", $time, test_input);
    end
end

// 生成波形文件
initial begin
    $dumpfile("simple_syntax_test.vcd");
    $dumpvars(0, simple_syntax_test);
end

endmodule
