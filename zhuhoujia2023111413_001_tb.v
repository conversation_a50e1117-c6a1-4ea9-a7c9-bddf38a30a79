`timescale 1ns / 1ps

module zhuhoujia2023111413_001_tb;

// 测试信号定义
reg clk;
reg rst_n;
reg [3:0] line_sensor;
wire [1:0] control_signal;

// 实例化被测试模块
zhuhoujia2023111413_001 uut (
    .clk(clk),
    .rst_n(rst_n),
    .line_sensor(line_sensor),
    .control_signal(control_signal)
);

// 时钟生成 - 50MHz时钟，周期20ns
initial begin
    clk = 0;
    forever #10 clk = ~clk;
end

// 测试序列
initial begin
    // 初始化信号
    rst_n = 0;
    line_sensor = 4'b0000;
    
    // 等待100ns后释放复位
    #100;
    rst_n = 1;
    #20;
    
    // 测试用例1：正常前进（中间两个传感器检测到线）
    line_sensor = 4'b0110;
    #40;
    $display("测试1 - 正常前进: line_sensor=%b, control_signal=%b (期望:00)", line_sensor, control_signal);
    
    // 测试用例2：轻微左偏
    line_sensor = 4'b1110;
    #40;
    $display("测试2 - 轻微左偏: line_sensor=%b, control_signal=%b (期望:01)", line_sensor, control_signal);
    
    // 测试用例3：轻微右偏
    line_sensor = 4'b0111;
    #40;
    $display("测试3 - 轻微右偏: line_sensor=%b, control_signal=%b (期望:10)", line_sensor, control_signal);
    
    // 测试用例4：大幅左偏
    line_sensor = 4'b1100;
    #40;
    $display("测试4 - 大幅左偏: line_sensor=%b, control_signal=%b (期望:01)", line_sensor, control_signal);
    
    // 测试用例5：大幅右偏
    line_sensor = 4'b0011;
    #40;
    $display("测试5 - 大幅右偏: line_sensor=%b, control_signal=%b (期望:10)", line_sensor, control_signal);
    
    // 测试用例6：急剧左偏
    line_sensor = 4'b1000;
    #40;
    $display("测试6 - 急剧左偏: line_sensor=%b, control_signal=%b (期望:01)", line_sensor, control_signal);
    
    // 测试用例7：急剧右偏
    line_sensor = 4'b0001;
    #40;
    $display("测试7 - 急剧右偏: line_sensor=%b, control_signal=%b (期望:10)", line_sensor, control_signal);
    
    // 测试用例8：全部检测到（终点）
    line_sensor = 4'b1111;
    #40;
    $display("测试8 - 全部检测到: line_sensor=%b, control_signal=%b (期望:11)", line_sensor, control_signal);
    
    // 测试用例9：全部未检测到（继续前进）
    line_sensor = 4'b0000;
    #40;
    $display("测试9 - 全部未检测到: line_sensor=%b, control_signal=%b (期望:00)", line_sensor, control_signal);
    
    // 测试用例10：未定义状态（默认停止）
    line_sensor = 4'b1010;
    #40;
    $display("测试10 - 未定义状态: line_sensor=%b, control_signal=%b (期望:11)", line_sensor, control_signal);
    
    // 测试复位功能
    rst_n = 0;
    #40;
    $display("测试11 - 复位状态: rst_n=%b, control_signal=%b (期望:11)", rst_n, control_signal);
    
    rst_n = 1;
    #40;
    
    $display("所有测试完成！");
    $finish;
end

// 监控信号变化
initial begin
    $monitor("时间=%0t, rst_n=%b, line_sensor=%b, control_signal=%b", 
             $time, rst_n, line_sensor, control_signal);
end

// 生成波形文件
initial begin
    $dumpfile("zhuhoujia2023111413_001_tb.vcd");
    $dumpvars(0, zhuhoujia2023111413_001_tb);
end

endmodule